import { Callbacks } from "@langchain/core/callbacks/manager";
import { END, START, StateGraph, StateGraphArgs } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";

async function langfuse() {
  const { CallbackHandler } = await import("langfuse-langchain");

  const langfuseHandler = new CallbackHandler({
    secretKey: process.env.LANGFUSE_SECRET_KEY,
    publicKey: process.env.LANGFUSE_PUBLIC_KEY,
    baseUrl: process.env.LANGFUSE_BASE_URL,
  });

  await example([langfuseHandler]);
}

async function langsmith() {
  // no setup needed

  await example();
}

async function braintrust() {
  const { initLogger } = await import("braintrust");
  const { BraintrustCallbackHandler } = await import(
    "@braintrust/langchain-js"
  );

  initLogger({
    projectName: process.env.BRAINTRUST_PROJECT,
  });

  const handler = new BraintrustCallbackHandler();

  await example([handler]);
}

async function example(callbacks?: Callbacks) {
  // derived from: https://techcommunity.microsoft.com/blog/educatordeveloperblog/an-absolute-beginners-guide-to-langgraph-js/4212496
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  type HelloWorldGraphState = Record<string, any>;

  const graphStateChannels: StateGraphArgs<HelloWorldGraphState>["channels"] =
    {};

  const model = new ChatOpenAI({
    model: "gpt-4o-mini",
  });

  async function sayHello(state: HelloWorldGraphState) {
    const res = await model.invoke("Say hello");
    return res.content;
  }

  function sayBye(state: HelloWorldGraphState) {
    console.log(`From the 'sayBye' node: Bye world!`);
    return {};
  }

  const graphBuilder = new StateGraph({ channels: graphStateChannels }) // Add our nodes to the Graph
    .addNode("sayHello", sayHello)
    .addNode("sayBye", sayBye) // Add the edges between nodes
    .addEdge(START, "sayHello")
    .addEdge("sayHello", "sayBye")
    .addEdge("sayBye", END);

  const helloWorldGraph = graphBuilder.compile();

  await helloWorldGraph.invoke({}, { callbacks });
}

async function main() {
  await braintrust();
  await langfuse();
  await langsmith();
}

main().catch(console.error);
