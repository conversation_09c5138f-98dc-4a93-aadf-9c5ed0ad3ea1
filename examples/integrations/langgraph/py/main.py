import asyncio

from langchain_core.callbacks import BaseCallbackHandler
from typing_extensions import List


async def langfuse():
    from langfuse.langchain import CallbackHandler

    langfuse_handler = CallbackHandler()

    await example([langfuse_handler])


async def langsmith():
    # no setup needed

    await example()


async def braintrust():
    from braintrust_langchain import BraintrustCallbackHandler, set_global_handler

    from braintrust.logger import init_logger

    init_logger(project="langgraph-py")

    handler = BraintrustCallbackHandler()
    set_global_handler(handler)

    await example([])


async def example(callbacks: List[BaseCallbackHandler] = []):
    from langchain_core.prompts import ChatPromptTemplate
    from langchain_openai import ChatOpenAI

    # Create your LangChain components
    llm = ChatOpenAI(model="gpt-4o")
    prompt = ChatPromptTemplate.from_template("Tell me a joke about {topic}")
    chain = prompt | llm

    # Run your chain with <PERSON><PERSON> tracing
    response = chain.invoke({"topic": "cats"}, config={"callbacks": callbacks})
    print(response.content)


async def main():
    await langfuse()
    await langsmith()
    await braintrust()


asyncio.run(main())
