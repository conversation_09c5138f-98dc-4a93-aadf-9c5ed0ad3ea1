import asyncio

from braintrust_langchain import BraintrustCallbackHandler, set_global_handler
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain_openai import ChatOpenAI

from braintrust import EvalAsync, init_logger

"""
This example shows tracing a task with and without it being inside of an Eval.

- When the task is called outside of an Eval the spans are sent to the project logs.
- When the task is called within an Eval the spans are part of the Eval's spans.
"""

project_name = "langchain-example"

init_logger(project=project_name)

set_global_handler(BraintrustCallbackHandler())

data = [
    {
        "input": "2",
        "expected": "3",
    }
]


async def task(number: str) -> str:
    prompt = ChatPromptTemplate.from_template("What is 1 + {number}?")
    model = ChatOpenAI(model="gpt-4o-mini")

    chain = prompt | model
    result = await chain.ainvoke({"number": number})

    return str(result.content)


def score(output: str, expected: str) -> float:
    return 1 if expected in output else 0


def test():
    return EvalAsync(project_name, data=data, task=task, scores=[score])


async def main():
    print("Calling task outside of an eval")
    await task(data[0]["input"])

    print("Running eval")
    await test()


if __name__ == "__main__":
    asyncio.run(main())
