{"name": "langchain-js-wrap", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "for file in src/*.ts; do npx tsx \"$file\"; done"}, "type": "module", "keywords": [], "author": "", "license": "ISC", "dependencies": {"@braintrust/langchain-js": "workspace:*", "@langchain/core": "^0.3.75", "@langchain/langgraph": "^0.2.25", "@langchain/openai": "^0.3.17", "braintrust": "workspace:*", "langfuse-langchain": "^3.38.5", "typescript": "5.5.4", "zod": "3.22.4"}}