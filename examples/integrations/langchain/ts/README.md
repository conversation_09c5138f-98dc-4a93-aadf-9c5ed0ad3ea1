# @braintrust/langchain-js Examples

This example demonstrates how to use the `@braintrust/langchain-js` package to automatically log LangChain.js executions to Braintrust.

## Getting Started

```bash
direnv allow # and make sure your root .env is set up

# or

export BRAINTRUST_API_KEY="your-api-key"
export OPENAI_API_KEY="your-openai-api-key"

# install dependencies
pnpm install
```

## Usage

```bash
# run the examples
pnpm start

# or for specific examples
pnpm exec tsx src/chain.ts
```
