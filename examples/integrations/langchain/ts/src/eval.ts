import {
  Braintrust<PERSON>allback<PERSON><PERSON><PERSON>,
  setGlobal<PERSON><PERSON><PERSON>,
} from "@braintrust/langchain-js";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";
import { <PERSON><PERSON>, initLogger } from "braintrust";

/**
 * This example shows tracing a task with and without it being inside of an Eval.
 *
 * - When the task is called outside of an Eval the spans are sent to the project logs.
 * - When the task is called within an Eval the spans are part of the Eval's spans.
 **/
const projectName = "langchain-example";

initLogger({
  projectName,
});

const handler = new BraintrustCallbackHandler();

setGlobalHandler(handler);

const data = [
  {
    input: "2",
    expected: "3",
  },
];

const task = async (number: string) => {
  const prompt = ChatPromptTemplate.fromTemplate(`What is 1 + {number}?`);
  const model = new ChatOpenAI({
    model: "gpt-4o-mini",
  });

  const chain = prompt.pipe(model);

  const result = await chain.invoke({ number });

  return String(result.content);
};

const test = () =>
  Eval(projectName, {
    data,
    task,
    scores: [({ output, expected }) => (output.includes(expected) ? 1 : 0)],
  });

async function main() {
  console.log("Calling task outside of an eval");
  await task(data[0].input);

  console.log("Running eval");
  await test();
}

main();
