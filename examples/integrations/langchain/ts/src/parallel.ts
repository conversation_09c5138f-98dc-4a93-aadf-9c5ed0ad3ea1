import { Callbacks } from "@langchain/core/callbacks/manager";
import { PromptTemplate } from "@langchain/core/prompts";
import { RunnableMap } from "@langchain/core/runnables";
import { ChatOpenAI } from "@langchain/openai";

async function langfuse() {
  const { CallbackHandler } = await import("langfuse-langchain");

  const langfuseHandler = new CallbackHandler({
    secretKey: process.env.LANGFUSE_SECRET_KEY,
    publicKey: process.env.LANGFUSE_PUBLIC_KEY,
    baseUrl: process.env.LANGFUSE_BASE_URL,
  });

  await example([langfuseHandler]);
}

async function langsmith() {
  // no setup needed

  await example();
}

async function braintrust() {
  const { initLogger } = await import("braintrust");
  const { BraintrustCallbackHandler } = await import(
    "@braintrust/langchain-js"
  );

  initLogger({
    projectName: process.env.BRAINTRUST_PROJECT,
  });

  const handler = new BraintrustCallbackHandler();

  await example([handler]);
}

async function example(callbacks?: Callbacks) {
  const model = new ChatOpenAI({ model: "gpt-4o-mini" });

  const jokeChain = PromptTemplate.fromTemplate(
    "Tell me a joke about {topic}",
  ).pipe(model);

  const poemChain = PromptTemplate.fromTemplate(
    "write a 2-line poem about {topic}",
  ).pipe(model);

  const mapChain = RunnableMap.from({
    joke: jokeChain,
    poem: poemChain,
  });

  await mapChain.invoke({ topic: "bear" }, { callbacks });
}

async function main() {
  await braintrust();
  await langfuse();
  await langsmith();
}

main().catch(console.error);
