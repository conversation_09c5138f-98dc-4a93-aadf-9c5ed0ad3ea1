from google.adk.agents import Agent
from google.adk.agents.active_streaming_tool import asyncio
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types


async def langfuse():
    from langfuse import get_client
    from openinference.instrumentation.google_adk import GoogleADKInstrumentor

    langfuse = get_client()

    if langfuse.auth_check():
        print("Langfuse client is authenticated and ready!")
    else:
        print("Authentication failed. Please check your credentials and host.")

    GoogleADKInstrumentor().instrument()

    await example()


async def braintrust():
    from braintrust_adk import setup_braintrust

    setup_braintrust(project_name="googleadk")

    await example()


async def example():
    def say_hello():
        return {"greeting": "Hello Langfuse 👋"}

    agent = Agent(
        name="hello_agent",
        model="gemini-2.0-flash",
        instruction="Always greet using the say_hello tool.",
        tools=[say_hello],
    )

    APP_NAME = "hello_app"
    USER_ID = "demo-user"
    SESSION_ID = "demo-session"

    session_service = InMemorySessionService()
    await session_service.create_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)

    runner = Runner(agent=agent, app_name=APP_NAME, session_service=session_service)

    user_msg = types.Content(role="user", parts=[types.Part(text="hi")])
    for event in runner.run(user_id=USER_ID, session_id=SESSION_ID, new_message=user_msg):
        if event.is_final_response():
            print(event)


async def main():
    await langfuse()
    await braintrust()


asyncio.run(main())
