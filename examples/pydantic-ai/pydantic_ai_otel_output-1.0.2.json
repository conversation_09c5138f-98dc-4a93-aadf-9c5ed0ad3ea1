{
  "traceId": "lDuVYd5/smzT0FLHBaJ23Q==",
  "spanId": "ahawOPYk8TA=",
  "name": "chat gpt-4o",
  "startTimeUnixNano": "1757709708523277000",
  "endTimeUnixNano": "1757709709645462000",
  "attributes": [
    {
      "key": "gen_ai.operation.name",
      "value": {
        "stringValue": "chat"
      }
    },
    {
      "key": "gen_ai.system",
      "value": {
        "stringValue": "openai"
      }
    },
    {
      "key": "gen_ai.request.model",
      "value": {
        "stringValue": "gpt-4o"
      }
    },
    {
      "key": "server.address",
      "value": {
        "stringValue": "api.openai.com"
      }
    },
    {
      "key": "model_request_parameters",
      "value": {
        "stringValue": "{\"function_tools\": [{\"name\": \"roll_dice\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Roll a six-sided die and return the result.\", \"outer_typed_dict_key\": null, \"strict\": false, \"sequential\": false, \"kind\": \"function\"}, {\"name\": \"get_player_name\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Get the player's name.\", \"outer_typed_dict_key\": null, \"strict\": false, \"sequential\": false, \"kind\": \"function\"}], \"builtin_tools\": [], \"output_mode\": \"text\", \"output_object\": null, \"output_tools\": [], \"allow_text_output\": true}"
      }
    },
    {
      "key": "gen_ai.input.messages",
      "value": {
        "stringValue": "[{\"role\": \"system\", \"parts\": [{\"type\": \"text\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\"}]}, {\"role\": \"user\", \"parts\": [{\"type\": \"text\", \"content\": \"My guess is 4\"}]}]"
      }
    },
    {
      "key": "gen_ai.output.messages",
      "value": {
        "stringValue": "[{\"role\": \"assistant\", \"parts\": [{\"type\": \"tool_call\", \"id\": \"call_KmVd0l19975JnVeHizsz4k1L\", \"name\": \"roll_dice\", \"arguments\": \"{}\"}, {\"type\": \"tool_call\", \"id\": \"call_PXLu9MP7yn4SDMuAnJTesEbi\", \"name\": \"get_player_name\", \"arguments\": \"{}\"}], \"finish_reason\": \"tool_call\"}]"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"gen_ai.input.messages\": {\"type\": \"array\"}, \"gen_ai.output.messages\": {\"type\": \"array\"}, \"model_request_parameters\": {\"type\": \"object\"}}}"
      }
    },
    {
      "key": "gen_ai.usage.input_tokens",
      "value": {
        "intValue": "90"
      }
    },
    {
      "key": "gen_ai.usage.output_tokens",
      "value": {
        "intValue": "41"
      }
    },
    {
      "key": "gen_ai.response.model",
      "value": {
        "stringValue": "gpt-4o-2024-08-06"
      }
    },
    {
      "key": "operation.cost",
      "value": {
        "doubleValue": 0.000635
      }
    },
    {
      "key": "gen_ai.response.id",
      "value": {
        "stringValue": "chatcmpl-CF4sWGEW3Uz4sl3ET2YvQ5UdzDUQ3"
      }
    },
    {
      "key": "gen_ai.response.finish_reasons",
      "value": {
        "stringValue": "('tool_call',)"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "lDuVYd5/smzT0FLHBaJ23Q==",
  "spanId": "B99wcJaU/A8=",
  "name": "running tool",
  "startTimeUnixNano": "1757709709649303000",
  "endTimeUnixNano": "1757709709651974000",
  "attributes": [
    {
      "key": "gen_ai.tool.name",
      "value": {
        "stringValue": "roll_dice"
      }
    },
    {
      "key": "gen_ai.tool.call.id",
      "value": {
        "stringValue": "call_KmVd0l19975JnVeHizsz4k1L"
      }
    },
    {
      "key": "tool_arguments",
      "value": {
        "stringValue": "{}"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "running tool: roll_dice"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"tool_arguments\": {\"type\": \"object\"}, \"tool_response\": {\"type\": \"object\"}, \"gen_ai.tool.name\": {}, \"gen_ai.tool.call.id\": {}}}"
      }
    },
    {
      "key": "tool_response",
      "value": {
        "stringValue": "2"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "lDuVYd5/smzT0FLHBaJ23Q==",
  "spanId": "JY2GULzWK5Y=",
  "name": "running tool",
  "startTimeUnixNano": "1757709709649392000",
  "endTimeUnixNano": "1757709709652800000",
  "attributes": [
    {
      "key": "gen_ai.tool.name",
      "value": {
        "stringValue": "get_player_name"
      }
    },
    {
      "key": "gen_ai.tool.call.id",
      "value": {
        "stringValue": "call_PXLu9MP7yn4SDMuAnJTesEbi"
      }
    },
    {
      "key": "tool_arguments",
      "value": {
        "stringValue": "{}"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "running tool: get_player_name"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"tool_arguments\": {\"type\": \"object\"}, \"tool_response\": {\"type\": \"object\"}, \"gen_ai.tool.name\": {}, \"gen_ai.tool.call.id\": {}}}"
      }
    },
    {
      "key": "tool_response",
      "value": {
        "stringValue": "Anne"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "lDuVYd5/smzT0FLHBaJ23Q==",
  "spanId": "2PEOYV7MUAU=",
  "name": "running tools",
  "startTimeUnixNano": "1757709709648215000",
  "endTimeUnixNano": "1757709709653596000",
  "attributes": [
    {
      "key": "tools",
      "value": {
        "stringValue": "('roll_dice', 'get_player_name')"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "running 2 tools"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "lDuVYd5/smzT0FLHBaJ23Q==",
  "spanId": "j+WlU67Wz9w=",
  "name": "chat gpt-4o",
  "startTimeUnixNano": "1757709709655859000",
  "endTimeUnixNano": "1757709710737356000",
  "attributes": [
    {
      "key": "gen_ai.operation.name",
      "value": {
        "stringValue": "chat"
      }
    },
    {
      "key": "gen_ai.system",
      "value": {
        "stringValue": "openai"
      }
    },
    {
      "key": "gen_ai.request.model",
      "value": {
        "stringValue": "gpt-4o"
      }
    },
    {
      "key": "server.address",
      "value": {
        "stringValue": "api.openai.com"
      }
    },
    {
      "key": "model_request_parameters",
      "value": {
        "stringValue": "{\"function_tools\": [{\"name\": \"roll_dice\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Roll a six-sided die and return the result.\", \"outer_typed_dict_key\": null, \"strict\": false, \"sequential\": false, \"kind\": \"function\"}, {\"name\": \"get_player_name\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Get the player's name.\", \"outer_typed_dict_key\": null, \"strict\": false, \"sequential\": false, \"kind\": \"function\"}], \"builtin_tools\": [], \"output_mode\": \"text\", \"output_object\": null, \"output_tools\": [], \"allow_text_output\": true}"
      }
    },
    {
      "key": "gen_ai.input.messages",
      "value": {
        "stringValue": "[{\"role\": \"system\", \"parts\": [{\"type\": \"text\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\"}]}, {\"role\": \"user\", \"parts\": [{\"type\": \"text\", \"content\": \"My guess is 4\"}]}, {\"role\": \"assistant\", \"parts\": [{\"type\": \"tool_call\", \"id\": \"call_KmVd0l19975JnVeHizsz4k1L\", \"name\": \"roll_dice\", \"arguments\": \"{}\"}, {\"type\": \"tool_call\", \"id\": \"call_PXLu9MP7yn4SDMuAnJTesEbi\", \"name\": \"get_player_name\", \"arguments\": \"{}\"}], \"finish_reason\": \"tool_call\"}, {\"role\": \"user\", \"parts\": [{\"type\": \"tool_call_response\", \"id\": \"call_KmVd0l19975JnVeHizsz4k1L\", \"name\": \"roll_dice\", \"result\": \"2\"}, {\"type\": \"tool_call_response\", \"id\": \"call_PXLu9MP7yn4SDMuAnJTesEbi\", \"name\": \"get_player_name\", \"result\": \"Anne\"}]}]"
      }
    },
    {
      "key": "gen_ai.output.messages",
      "value": {
        "stringValue": "[{\"role\": \"assistant\", \"parts\": [{\"type\": \"text\", \"content\": \"Thanks for playing, Anne! You guessed 4, but the dice showed 2. Better luck next time!\"}], \"finish_reason\": \"stop\"}]"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"gen_ai.input.messages\": {\"type\": \"array\"}, \"gen_ai.output.messages\": {\"type\": \"array\"}, \"model_request_parameters\": {\"type\": \"object\"}}}"
      }
    },
    {
      "key": "gen_ai.usage.input_tokens",
      "value": {
        "intValue": "147"
      }
    },
    {
      "key": "gen_ai.usage.output_tokens",
      "value": {
        "intValue": "24"
      }
    },
    {
      "key": "gen_ai.response.model",
      "value": {
        "stringValue": "gpt-4o-2024-08-06"
      }
    },
    {
      "key": "operation.cost",
      "value": {
        "doubleValue": 0.0006075
      }
    },
    {
      "key": "gen_ai.response.id",
      "value": {
        "stringValue": "chatcmpl-CF4sXbPYpgTeR2Vd5LqsXH5dyOhVa"
      }
    },
    {
      "key": "gen_ai.response.finish_reasons",
      "value": {
        "stringValue": "('stop',)"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "lDuVYd5/smzT0FLHBaJ23Q==",
  "spanId": "2AVEvayc0IA=",
  "name": "agent run",
  "startTimeUnixNano": "1757709708522589000",
  "endTimeUnixNano": "1757709710737739000",
  "attributes": [
    {
      "key": "model_name",
      "value": {
        "stringValue": "gpt-4o"
      }
    },
    {
      "key": "agent_name",
      "value": {
        "stringValue": "agent"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "agent run"
      }
    },
    {
      "key": "final_result",
      "value": {
        "stringValue": "Thanks for playing, Anne! You guessed 4, but the dice showed 2. Better luck next time!"
      }
    },
    {
      "key": "gen_ai.usage.input_tokens",
      "value": {
        "intValue": "237"
      }
    },
    {
      "key": "gen_ai.usage.output_tokens",
      "value": {
        "intValue": "65"
      }
    },
    {
      "key": "pydantic_ai.all_messages",
      "value": {
        "stringValue": "[{\"role\": \"system\", \"parts\": [{\"type\": \"text\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\"}]}, {\"role\": \"user\", \"parts\": [{\"type\": \"text\", \"content\": \"My guess is 4\"}]}, {\"role\": \"assistant\", \"parts\": [{\"type\": \"tool_call\", \"id\": \"call_KmVd0l19975JnVeHizsz4k1L\", \"name\": \"roll_dice\", \"arguments\": \"{}\"}, {\"type\": \"tool_call\", \"id\": \"call_PXLu9MP7yn4SDMuAnJTesEbi\", \"name\": \"get_player_name\", \"arguments\": \"{}\"}], \"finish_reason\": \"tool_call\"}, {\"role\": \"user\", \"parts\": [{\"type\": \"tool_call_response\", \"id\": \"call_KmVd0l19975JnVeHizsz4k1L\", \"name\": \"roll_dice\", \"result\": \"2\"}, {\"type\": \"tool_call_response\", \"id\": \"call_PXLu9MP7yn4SDMuAnJTesEbi\", \"name\": \"get_player_name\", \"result\": \"Anne\"}]}, {\"role\": \"assistant\", \"parts\": [{\"type\": \"text\", \"content\": \"Thanks for playing, Anne! You guessed 4, but the dice showed 2. Better luck next time!\"}], \"finish_reason\": \"stop\"}]"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"pydantic_ai.all_messages\": {\"type\": \"array\"}, \"final_result\": {\"type\": \"object\"}}}"
      }
    }
  ],
  "status": {}
}
