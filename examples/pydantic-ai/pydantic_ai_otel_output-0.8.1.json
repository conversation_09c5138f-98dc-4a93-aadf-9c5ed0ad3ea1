{
  "traceId": "fD+LgdMt7qd32giBRg8hrQ==",
  "spanId": "/8KJ9TrpdWA=",
  "name": "chat gpt-4o",
  "startTimeUnixNano": "1757548759280129000",
  "endTimeUnixNano": "1757548760238660000",
  "attributes": [
    {
      "key": "gen_ai.operation.name",
      "value": {
        "stringValue": "chat"
      }
    },
    {
      "key": "gen_ai.system",
      "value": {
        "stringValue": "openai"
      }
    },
    {
      "key": "gen_ai.request.model",
      "value": {
        "stringValue": "gpt-4o"
      }
    },
    {
      "key": "server.address",
      "value": {
        "stringValue": "api.openai.com"
      }
    },
    {
      "key": "model_request_parameters",
      "value": {
        "stringValue": "{\"function_tools\": [{\"name\": \"roll_dice\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Roll a six-sided die and return the result.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}, {\"name\": \"get_player_name\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Get the player's name.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}], \"builtin_tools\": [], \"output_mode\": \"text\", \"output_object\": null, \"output_tools\": [], \"allow_text_output\": true}"
      }
    },
    {
      "key": "events",
      "value": {
        "stringValue": "[{\"role\": \"system\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.system.message\"}, {\"content\": \"My guess is 4\", \"role\": \"user\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.user.message\"}, {\"index\": 0, \"message\": {\"role\": \"assistant\", \"tool_calls\": [{\"id\": \"call_6F9EotEVtgDAl2AvNrbONDT2\", \"type\": \"function\", \"function\": {\"name\": \"roll_dice\", \"arguments\": \"{}\"}}, {\"id\": \"call_tyuozKxgpbEdRVuganIK6zZJ\", \"type\": \"function\", \"function\": {\"name\": \"get_player_name\", \"arguments\": \"{}\"}}]}, \"gen_ai.system\": \"openai\", \"event.name\": \"gen_ai.choice\"}]"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"events\": {\"type\": \"array\"}, \"model_request_parameters\": {\"type\": \"object\"}}}"
      }
    },
    {
      "key": "gen_ai.usage.input_tokens",
      "value": {
        "stringValue": "90"
      }
    },
    {
      "key": "gen_ai.usage.output_tokens",
      "value": {
        "stringValue": "41"
      }
    },
    {
      "key": "gen_ai.response.model",
      "value": {
        "stringValue": "gpt-4o-2024-08-06"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "fD+LgdMt7qd32giBRg8hrQ==",
  "spanId": "seE1iKyF2YA=",
  "name": "running tool",
  "startTimeUnixNano": "1757548760239057000",
  "endTimeUnixNano": "1757548760239516000",
  "attributes": [
    {
      "key": "gen_ai.tool.name",
      "value": {
        "stringValue": "roll_dice"
      }
    },
    {
      "key": "gen_ai.tool.call.id",
      "value": {
        "stringValue": "call_6F9EotEVtgDAl2AvNrbONDT2"
      }
    },
    {
      "key": "tool_arguments",
      "value": {
        "stringValue": "{}"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "running tool: roll_dice"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"tool_arguments\": {\"type\": \"object\"}, \"tool_response\": {\"type\": \"object\"}, \"gen_ai.tool.name\": {}, \"gen_ai.tool.call.id\": {}}}"
      }
    },
    {
      "key": "tool_response",
      "value": {
        "stringValue": "3"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "fD+LgdMt7qd32giBRg8hrQ==",
  "spanId": "vFxCoYIkD7s=",
  "name": "running tool",
  "startTimeUnixNano": "1757548760239143000",
  "endTimeUnixNano": "1757548760239579000",
  "attributes": [
    {
      "key": "gen_ai.tool.name",
      "value": {
        "stringValue": "get_player_name"
      }
    },
    {
      "key": "gen_ai.tool.call.id",
      "value": {
        "stringValue": "call_tyuozKxgpbEdRVuganIK6zZJ"
      }
    },
    {
      "key": "tool_arguments",
      "value": {
        "stringValue": "{}"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "running tool: get_player_name"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"tool_arguments\": {\"type\": \"object\"}, \"tool_response\": {\"type\": \"object\"}, \"gen_ai.tool.name\": {}, \"gen_ai.tool.call.id\": {}}}"
      }
    },
    {
      "key": "tool_response",
      "value": {
        "stringValue": "Anne"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "fD+LgdMt7qd32giBRg8hrQ==",
  "spanId": "a0OjfEpKTOY=",
  "name": "running tools",
  "startTimeUnixNano": "1757548760238967000",
  "endTimeUnixNano": "1757548760239622000",
  "attributes": [
    {
      "key": "tools",
      "value": {
        "stringValue": "('roll_dice', 'get_player_name')"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "running 2 tools"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "fD+LgdMt7qd32giBRg8hrQ==",
  "spanId": "JZbuo+4UNnA=",
  "name": "chat gpt-4o",
  "startTimeUnixNano": "1757548760239936000",
  "endTimeUnixNano": "1757548760918505000",
  "attributes": [
    {
      "key": "gen_ai.operation.name",
      "value": {
        "stringValue": "chat"
      }
    },
    {
      "key": "gen_ai.system",
      "value": {
        "stringValue": "openai"
      }
    },
    {
      "key": "gen_ai.request.model",
      "value": {
        "stringValue": "gpt-4o"
      }
    },
    {
      "key": "server.address",
      "value": {
        "stringValue": "api.openai.com"
      }
    },
    {
      "key": "model_request_parameters",
      "value": {
        "stringValue": "{\"function_tools\": [{\"name\": \"roll_dice\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Roll a six-sided die and return the result.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}, {\"name\": \"get_player_name\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Get the player's name.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}], \"builtin_tools\": [], \"output_mode\": \"text\", \"output_object\": null, \"output_tools\": [], \"allow_text_output\": true}"
      }
    },
    {
      "key": "events",
      "value": {
        "stringValue": "[{\"role\": \"system\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.system.message\"}, {\"content\": \"My guess is 4\", \"role\": \"user\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.user.message\"}, {\"role\": \"assistant\", \"tool_calls\": [{\"id\": \"call_6F9EotEVtgDAl2AvNrbONDT2\", \"type\": \"function\", \"function\": {\"name\": \"roll_dice\", \"arguments\": \"{}\"}}, {\"id\": \"call_tyuozKxgpbEdRVuganIK6zZJ\", \"type\": \"function\", \"function\": {\"name\": \"get_player_name\", \"arguments\": \"{}\"}}], \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 1, \"event.name\": \"gen_ai.assistant.message\"}, {\"content\": \"3\", \"role\": \"tool\", \"id\": \"call_6F9EotEVtgDAl2AvNrbONDT2\", \"name\": \"roll_dice\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"content\": \"Anne\", \"role\": \"tool\", \"id\": \"call_tyuozKxgpbEdRVuganIK6zZJ\", \"name\": \"get_player_name\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! \\ud83c\\udfb2\"}, \"gen_ai.system\": \"openai\", \"event.name\": \"gen_ai.choice\"}]"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"events\": {\"type\": \"array\"}, \"model_request_parameters\": {\"type\": \"object\"}}}"
      }
    },
    {
      "key": "gen_ai.usage.input_tokens",
      "value": {
        "stringValue": "147"
      }
    },
    {
      "key": "gen_ai.usage.output_tokens",
      "value": {
        "stringValue": "24"
      }
    },
    {
      "key": "gen_ai.response.model",
      "value": {
        "stringValue": "gpt-4o-2024-08-06"
      }
    }
  ],
  "status": {}
}
{
  "traceId": "fD+LgdMt7qd32giBRg8hrQ==",
  "spanId": "799U3iYctfs=",
  "name": "agent run",
  "startTimeUnixNano": "1757548759279468000",
  "endTimeUnixNano": "1757548760918881000",
  "attributes": [
    {
      "key": "model_name",
      "value": {
        "stringValue": "gpt-4o"
      }
    },
    {
      "key": "agent_name",
      "value": {
        "stringValue": "agent"
      }
    },
    {
      "key": "logfire.msg",
      "value": {
        "stringValue": "agent run"
      }
    },
    {
      "key": "final_result",
      "value": {
        "stringValue": "Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! \ud83c\udfb2"
      }
    },
    {
      "key": "gen_ai.usage.input_tokens",
      "value": {
        "stringValue": "237"
      }
    },
    {
      "key": "gen_ai.usage.output_tokens",
      "value": {
        "stringValue": "65"
      }
    },
    {
      "key": "all_messages_events",
      "value": {
        "stringValue": "[{\"role\": \"system\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.system.message\"}, {\"content\": \"My guess is 4\", \"role\": \"user\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.user.message\"}, {\"role\": \"assistant\", \"tool_calls\": [{\"id\": \"call_6F9EotEVtgDAl2AvNrbONDT2\", \"type\": \"function\", \"function\": {\"name\": \"roll_dice\", \"arguments\": \"{}\"}}, {\"id\": \"call_tyuozKxgpbEdRVuganIK6zZJ\", \"type\": \"function\", \"function\": {\"name\": \"get_player_name\", \"arguments\": \"{}\"}}], \"gen_ai.message.index\": 1, \"event.name\": \"gen_ai.assistant.message\"}, {\"content\": \"3\", \"role\": \"tool\", \"id\": \"call_6F9EotEVtgDAl2AvNrbONDT2\", \"name\": \"roll_dice\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"content\": \"Anne\", \"role\": \"tool\", \"id\": \"call_tyuozKxgpbEdRVuganIK6zZJ\", \"name\": \"get_player_name\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"role\": \"assistant\", \"content\": \"Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! \\ud83c\\udfb2\", \"gen_ai.message.index\": 3, \"event.name\": \"gen_ai.assistant.message\"}]"
      }
    },
    {
      "key": "logfire.json_schema",
      "value": {
        "stringValue": "{\"type\": \"object\", \"properties\": {\"all_messages_events\": {\"type\": \"array\"}, \"final_result\": {\"type\": \"object\"}}}"
      }
    }
  ],
  "status": {}
}
