"""
set up the following env vars
LANGFUSE_PUBLIC_KEY = "pk-..."
LANGFUSE_SECRET_KEY = "sk-..."
LANGFUSE_HOST = "https://us.cloud.langfuse.com"
"""

import os
import random

from langfuse import get_client, observe
from pydantic_ai.agent import Agent, RunContext

langfuse = get_client()
if not langfuse.auth_check():
    raise Exception("why do i have to check this myself lol")

Agent.instrument_all()

agent = Agent(
    "openai:gpt-4o",
    system_prompt=(
        "You are a dice game host. Roll the dice for the player and check if their guess matches. "
        "Always include the player's name in the response."
    ),
    instrument=True,
)


@agent.tool_plain
def roll_dice() -> str:
    """Roll a six-sided die and return the result."""
    return str(random.randint(1, 6))


@agent.tool
def get_player_name(ctx: RunContext[str]) -> str:
    """Get the player's name."""
    some_other_function(1)
    return ctx.deps


@observe
def some_other_function(i: int):
    return 3 + i


dice_result = agent.run_sync("My guess is 4", deps="Anne")
print(dice_result)
