import asyncio
import os
import random

from braintrust.otel import BraintrustSpanProcessor
from braintrust_local.otel_test_util import ProtoJsonPrintingExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.trace import set_tracer_provider
from pydantic_ai import Agent, RunContext
from pydantic_ai.usage import UsageLimits

# Set up the OTel tracer provider to use BraintrustSpanProcessor
provider = TracerProvider()
provider.add_span_processor(BraintrustSpanProcessor())
set_tracer_provider(provider)

# Add console exporter if environment variable is set
if os.getenv("BRAINTRUST_OTEL_CONSOLE_EXPORT", "").lower() in ("true", "1", "yes"):
    console_exporter = ConsoleSpanExporter()
    console_processor = BatchSpanProcessor(console_exporter)
    provider.add_span_processor(console_processor)

# Add proto exporter if environment variable is set
if os.getenv("BRAINTRUST_OTEL_PROTO_EXPORT", "").lower() in ("true", "1", "yes"):
    exporter = ProtoJsonPrintingExporter()
    processor = BatchSpanProcessor(exporter)
    provider.add_span_processor(processor)

model = "openai:gpt-4o"

# Instrument the Agent with OTel
Agent.instrument_all()

joke_selection_agent = Agent(
    model,
    system_prompt=(
        "Use the `joke_factory` to generate some jokes, then choose the best. " "You must return just a single joke."
    ),
)
joke_generation_agent = Agent(model, output_type=list[str])


@joke_generation_agent.tool
def get_joke_topics(ctx: RunContext[None]) -> list[str]:
    topics = ["technology", "food", "animals", "cinema"]
    return random.sample(topics, k=2)


@joke_selection_agent.tool
async def joke_factory(ctx: RunContext[None], count: int) -> list[str]:
    r = await joke_generation_agent.run(
        f"Please generate {count} jokes. Use the tool to know which topics they should be about.",
        usage=ctx.usage,
    )
    return r.output


async def main():
    result = await joke_selection_agent.run(
        "Tell me a joke.",
        usage_limits=UsageLimits(request_limit=5),
    )
    print(result.output)


if __name__ == "__main__":
    asyncio.run(main())
