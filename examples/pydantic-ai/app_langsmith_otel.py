# https://ai.pydantic.dev/logfire/#otel-without-logfire
"""
set up the following env vars
OTEL_EXPORTER_OTLP_ENDPOINT = 'https://api.smith.langchain.com/otel'
OTEL_EXPORTER_OTLP_HEADERS = 'x-api-key=...,Langsmith-Project=...'
"""
import os
import random

from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from pydantic_ai import RunContext
from pydantic_ai.agent import Agent

os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = "https://api.smith.langchain.com/otel"
os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = f"x-api-key={os.environ['LANGSMITH_API_KEY']}"
exporter = OTLPSpanExporter()
provider = TracerProvider()
trace.set_tracer_provider(provider)

provider.add_span_processor(BatchSpanProcessor(exporter))

Agent.instrument_all()

agent = Agent(
    "openai:gpt-4o",
    system_prompt=(
        "You are a dice game host. Roll the dice for the player and check if their guess matches. "
        "Always include the player's name in the response."
    ),
)


@agent.tool_plain
def roll_dice() -> str:
    """Roll a six-sided die and return the result."""
    return str(random.randint(1, 6))


@agent.tool
def get_player_name(ctx: RunContext[str]) -> str:
    """Get the player's name."""
    return ctx.deps


dice_result = agent.run_sync("My guess is 4", deps="Anne")
print(dice_result)
