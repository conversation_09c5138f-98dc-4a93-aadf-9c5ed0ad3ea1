use btql::{binder::ast::<PERSON><PERSON>, interpreter::expr::interpret_expr};
use std::{
    borrow::Cow,
    collections::{BTreeSet, HashMap},
    ops::Range,
    sync::Arc,
};
use storage::{
    directory::cached_directory::FastHashMap,
    hash_map::HashMapExt,
    index_wal_reader::{IndexWalReaderInput, IndexWalReaderOptionalInput, IndexWalReaderOpts},
    tantivy_index::{JSON_PATHS_FIELD, JSON_ROOT_FIELD},
};
use tantivy::{
    collector::{Collector, SegmentCollector},
    columnar::{ColumnarReader, DynamicColumn, StrColumn},
    common::BitSet,
    directory::DirectoryClone,
    fastfield::FastFieldReaders,
    query::{BitSetDocSet, ConstScorer, EnableScoring, TermSetQuery},
    SegmentOrdinal, SegmentReader, TantivyError,
};
use tokio::sync::mpsc;

use async_util::spawn_blocking_util::spawn_blocking_with_async_timeout;
use futures::future::join_all;
use util::{
    anyhow::Context,
    json::{set_value_at_path, PathPiece},
    schema::TantivyType,
    tracer::{trace_if, trace_if_async, EnterTraceGuard, TracedNode},
    uuid::Uuid,
    Value,
};

use crate::{
    interpreter::{
        aggregator::{aggregate::AggregatorBase, Aggregator, ExprAggregator, ValueAggregator},
        columnar::{
            aggregation::{
                execute_tuple_dim_group_by, FallibleCollector, FallibleSegmentCollector,
            },
            hash_map::FastHashSet,
            value::ColumnarExprContext,
        },
        context::InterpreterContextState,
        error::{InterpreterError, Result},
        groupby::{expr_buckets_to_value_buckets, perform_groupby},
        local::perform_filter,
        InterpreterContext, StreamValue,
    },
    optimizer::ast::{
        RealtimeWALSearchQuery, TantivyProjectedField, UnpivotProjectedField, UnpivotProjectionExpr,
    },
    planner::{
        ast::{GroupByQuery, GroupByStrategy, TantivyAggregateQuery},
        JSON_PATH_SEGMENT_SEP,
    },
    Operator,
};

use super::{
    columnstore::{batch_ordinals_decompress, collect_column_for_doc, get_columns_matching_prefix},
    expand::ROOT_SPAN_ID_FIELD,
    search::{
        convert_tantivy_value, filter_project_in_memory_docs, open_columnstore_field_readers,
        read_columnstore_values, IsPaginationKeyField,
    },
};
use crate::interpreter::columnar::aggregation::NoDimsCollector;

#[async_trait::async_trait]
impl Operator for TantivyAggregateQuery {
    fn name(&self) -> &'static str {
        "Index aggregate"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        if self.expand_traces {
            self.execute_traces(ctx, tracer, tx).await
        } else {
            self.execute_spans(ctx, tracer, tx).await
        }
    }
}

impl TantivyAggregateQuery {
    async fn execute_spans(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let aggregator_base = self.create_aggregator_base();
        let dimensions_vec = self.dimensions.clone();
        let pivot_vec = self.pivot.clone();
        let measures = self.measures.clone();
        let expr_aggs = self.expr_aggs.clone();
        let projection = self.projection.clone();
        let unpivot = self.unpivot.clone();
        let group_by = self.group_by.clone();
        let realtime_search = self.realtime_search.clone();

        let index_wal_reader = self.open_index_wal_reader(&ctx, &tracer, false).await?;

        let search_query = index_wal_reader.wrap_exclude_query(self.search);

        let directory = index_wal_reader.only_directory();
        directory.load_segment_footer(tracer.clone()).await?;

        let (tantivy_index, handle, tracer) =
            trace_if(log::Level::Info, &tracer, "Initialize tantivy", |_child| {
                let mut tantivy_index = tantivy::Index::open(directory.box_clone())?;
                tantivy_index.set_shared_multithread_executor(ctx.executor.clone())?;
                let handle = ctx.handle.clone();
                let tracer = tracer.clone();

                Ok::<_, tantivy::TantivyError>((tantivy_index, handle, tracer))
            })?;

        ctx.set_index_state(InterpreterContextState {
            index_wal_reader: index_wal_reader.clone(),
            tantivy_schema: tantivy_index.schema().clone(),
            tantivy_index: tantivy_index.clone(),
            tantivy_readers: vec![], // Unset, because downstream consumers won't use these (the operator is not streaming).
        });

        let span = tracing::Span::current();
        let buckets = spawn_blocking_with_async_timeout(
            &handle,
            {
                let realtime_segment =
                    trace_if(log::Level::Info, &tracer, "Realtime groupby", |child| {
                        realtime_groupby(
                            child,
                            ctx.clone(),
                            index_wal_reader.in_memory_docs(),
                            &realtime_search,
                            &dimensions_vec,
                            &pivot_vec,
                            &aggregator_base,
                        )
                    })?;

                let aggregator_base = aggregator_base.clone();
                let ctx = ctx.clone();
                let dimensions = Arc::new(
                    dimensions_vec
                        .iter()
                        .chain(pivot_vec.iter())
                        .cloned()
                        .collect::<Vec<_>>(),
                );
                move || {
                    let _guard = span.enter();
                    let reader = trace_if(log::Level::Info, &tracer, "Open reader", |child| {
                        child.increment_counter(
                            "num_chunks",
                            tantivy_index.searchable_segment_metas()?.len() as u64,
                        );
                        tantivy_index.reader()
                    })?;
                    let searcher = reader.searcher();

                    let docs =
                        trace_if(log::Level::Info, &tracer, "Search and aggregate", |child| {
                            Self::execute_search_and_aggregate(
                                &searcher,
                                &search_query,
                                ctx.clone(),
                                projection,
                                unpivot,
                                dimensions,
                                aggregator_base,
                                group_by,
                                child,
                            )
                        })?;

                    let docs = trace_if(
                        log::Level::Info,
                        &tracer,
                        "Incorporate realtime buckets",
                        |_child| {
                            let mut docs = docs;
                            for (dimensions, aggregators) in realtime_segment {
                                update_buckets(
                                    &mut docs,
                                    dimensions,
                                    aggregators
                                        .into_iter()
                                        .map(|agg| agg.into_value_aggregator()),
                                )?;
                            }
                            Ok::<_, InterpreterError>(docs)
                        },
                    )?;

                    Ok::<_, InterpreterError>(docs)
                }
            },
            Default::default(),
            || "tantivy aggregate".into(),
        )
        .await
        .context("Failed to join")???;

        GroupByQuery::finish(
            &ctx,
            &aggregator_base,
            buckets,
            dimensions_vec.as_ref(),
            &pivot_vec,
            &measures,
            &expr_aggs,
            tx,
        )
        .await?;

        Ok(())
    }

    async fn execute_traces(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let aggregator_base = self.create_aggregator_base();
        let dimensions_vec = self.dimensions.clone();
        let pivot_vec = self.pivot.clone();
        let measures = self.measures.clone();
        let expr_aggs = self.expr_aggs.clone();
        let projection = self.projection.clone();
        let unpivot = self.unpivot.clone();
        let group_by = self.group_by.clone();
        let realtime_search = self.realtime_search.clone();
        let search = self.search.box_clone();

        let index_wal_reader = self.open_index_wal_reader(&ctx, &tracer, true).await?;

        // Intuiitively, real-time works by:
        // - Doing the normal real-time segment processing
        // - Collecting the root span ids we see along the way
        // - In addition to the normal exclusion logic, making sure we pick up any other spans that
        //   match the root spans we saw from the real-time segment.
        let (
            mut segment_to_matched_root_span_ids,
            mut segment_to_unmatched_root_span_ids,
            mut realtime_root_span_ids,
        ) = trace_if(
            log::Level::Info,
            &tracer,
            "Collect real-time root span ids",
            |child| {
                // First get a list of all root span ids
                let mut unmatched_root_span_ids: FastHashSet<String> = FastHashSet::default();
                let all_rows = index_wal_reader
                    .in_memory_docs()
                    .iter()
                    .map(|d| {
                        if let Some(Value::String(s)) = d.get(ROOT_SPAN_ID_FIELD) {
                            unmatched_root_span_ids.insert(s.clone());
                        }

                        Cow::Borrowed(d)
                    })
                    .collect::<Vec<_>>();

                // Then, filter the rows based on the realtime search filter
                let filtered_rows = trace_if(log::Level::Info, &child, "Filter", |_child| {
                    perform_filter(&ctx.expr_ctx, all_rows, &self.realtime_search.filter)
                })?;

                // Find the corresponding set of root span ids.
                let mut segment_to_matched_root_span_ids: FastHashMap<Uuid, FastHashSet<String>> =
                    FastHashMap::default();
                let mut realtime_only_root_span_ids: FastHashSet<String> = FastHashSet::default();
                for row in filtered_rows.iter() {
                    if let Some(Value::String(root_span_id)) = row.get(ROOT_SPAN_ID_FIELD) {
                        unmatched_root_span_ids.remove(root_span_id);

                        if let Some(segments) = index_wal_reader
                            .root_span_id_to_segment_id()
                            .get(root_span_id)
                        {
                            for segment in segments {
                                segment_to_matched_root_span_ids
                                    .entry(*segment)
                                    .or_default()
                                    .insert(root_span_id.clone());
                            }
                        } else {
                            // If we don't have a segment for this root span id, then it must be a
                            // realtime-only root span id.
                            realtime_only_root_span_ids.insert(root_span_id.clone());
                        }
                    }
                }

                // For each Braintrust segment, track the set of unmatched root span ids. These are
                // rows that are not currently going to match the filter set, but they _might_ in
                // the lower-level segment searches, and if they do, we'll want to include them.
                let mut segment_to_unmatched_root_span_ids: FastHashMap<Uuid, FastHashSet<String>> =
                    FastHashMap::default();
                for root_span_id in unmatched_root_span_ids {
                    if let Some(segments) = index_wal_reader
                        .root_span_id_to_segment_id()
                        .get(&root_span_id)
                    {
                        for segment in segments {
                            segment_to_unmatched_root_span_ids
                                .entry(*segment)
                                .or_default()
                                .insert(root_span_id.clone());
                        }
                    }
                }

                child.increment_counter(
                    "num_matched_root_span_ids",
                    segment_to_matched_root_span_ids
                        .values()
                        .map(|s| s.len())
                        .sum::<usize>() as u64,
                );
                child.increment_counter(
                    "num_unmatched_root_span_ids",
                    segment_to_unmatched_root_span_ids
                        .values()
                        .map(|s| s.len())
                        .sum::<usize>() as u64,
                );

                Ok::<_, InterpreterError>((
                    segment_to_matched_root_span_ids,
                    segment_to_unmatched_root_span_ids,
                    realtime_only_root_span_ids,
                ))
            },
        )?;

        let handle = ctx.handle.clone();
        let dimensions = self.create_dimensions();

        let segment_buckets_results = join_all(
            (0..index_wal_reader.num_batches())
                .filter(|batch_idx| {
                    // Only run the aggregate query on batches that have segments. Technically this
                    // should only happen if all segments were eliminated
                    !index_wal_reader.segment_batches()[*batch_idx]
                        .segments
                        .is_empty()
                })
                .map(|batch_idx| {
                    let aggregator_base = aggregator_base.clone();
                    let dimensions = dimensions.clone();
                    let ctx = ctx.clone();
                    let index_wal_reader = index_wal_reader.clone();
                    let tracer = tracer.clone();
                    let projection = projection.clone();
                    let unpivot = unpivot.clone();
                    let group_by = group_by.clone();
                    let search = search.box_clone();

                    spawn_blocking_with_async_timeout(
                        &handle,
                        {
                            let span =
                                tracing::info_span!("Aggregate segment", batch_idx = batch_idx);
                            let segments = &index_wal_reader.segment_batches()[batch_idx].segments;
                            assert!(
                                segments.len() == 1,
                                "Aggregate query should only be run on single segment batches"
                            );
                            let segment = &segments[0];
                            let matched_root_span_ids = segment_to_matched_root_span_ids
                                .remove(segment)
                                .unwrap_or_default();
                            let unmatched_root_span_ids = segment_to_unmatched_root_span_ids
                                .remove(segment)
                                .unwrap_or_default();
                            move || {
                                Self::expand_and_aggregate_segment(
                                    ctx,
                                    batch_idx,
                                    span,
                                    tracer,
                                    index_wal_reader,
                                    projection,
                                    unpivot,
                                    search,
                                    dimensions,
                                    aggregator_base,
                                    group_by,
                                    matched_root_span_ids,
                                    unmatched_root_span_ids,
                                )
                            }
                        },
                        Default::default(),
                        || "tantivy aggregate".into(),
                    )
                }),
        )
        .await;

        let mut buckets = HashMap::default();
        for result in segment_buckets_results {
            let (segment_buckets, segment_root_span_ids) =
                result.context("failed to join")???;

            if buckets.is_empty() {
                buckets = segment_buckets;
            } else {
                for (dims, aggs) in segment_buckets.into_iter() {
                    update_buckets(&mut buckets, dims, aggs)?;
                }
            }

            realtime_root_span_ids.extend(segment_root_span_ids.into_iter());
        }

        trace_if(log::Level::Info, &tracer, "Realtime expansion", |child| {
            child.increment_counter(
                "num_realtime_root_span_ids",
                realtime_root_span_ids.len() as u64,
            );
            let mut matching_docs = Vec::new();
            for doc in index_wal_reader.in_memory_docs().iter() {
                if let Some(Value::String(root_span_id)) = doc.get(ROOT_SPAN_ID_FIELD) {
                    if realtime_root_span_ids.contains(root_span_id) {
                        matching_docs.push(doc.clone());
                    }
                }
            }

            let mut realtime_search_modified = realtime_search.clone();
            realtime_search_modified.filter = None;

            let realtime_segment =
                trace_if(log::Level::Info, &child, "Realtime groupby", |child| {
                    realtime_groupby(
                        child,
                        ctx.clone(),
                        &matching_docs,
                        &realtime_search_modified,
                        &dimensions_vec,
                        &pivot_vec,
                        &aggregator_base,
                    )
                })?;

            trace_if(
                log::Level::Info,
                &child,
                "Incorporate realtime buckets",
                |_child| {
                    for (dimensions, aggregators) in realtime_segment {
                        update_buckets(
                            &mut buckets,
                            dimensions,
                            aggregators
                                .into_iter()
                                .map(|agg| agg.into_value_aggregator()),
                        )?;
                    }
                    Ok::<_, InterpreterError>(())
                },
            )?;

            Ok::<_, InterpreterError>(())
        })?;

        GroupByQuery::finish(
            &ctx,
            &aggregator_base,
            buckets,
            dimensions_vec.as_ref(),
            &pivot_vec,
            &measures,
            &expr_aggs,
            tx,
        )
        .await?;

        Ok(())
    }

    fn expand_and_aggregate_segment(
        ctx: Arc<InterpreterContext>,
        batch_idx: usize,
        span: tracing::Span,
        tracer: Option<Arc<TracedNode>>,
        index_wal_reader: storage::index_wal_reader::IndexWalReader,
        projection: Vec<TantivyProjectedField>,
        unpivots: Vec<UnpivotProjectionExpr>,
        search: Box<dyn tantivy::query::Query>,
        dimensions: Arc<Vec<Alias>>,
        aggregator_base: Vec<ExprAggregator>,
        group_by: GroupByStrategy,
        mut matched_root_span_ids: FastHashSet<String>,
        mut unmatched_root_span_ids: FastHashSet<String>,
    ) -> Result<(
        HashMap<Vec<Value>, Vec<ValueAggregator>>,
        // Root span ids that we should include in the realtime search
        FastHashSet<String>,
    )> {
        let _guard = span.enter();

        let (directory, mut search_query) =
            index_wal_reader.directory_with_sort_filters(batch_idx, search, None);

        directory.load_segment_footer_blocking(tracer.clone())?;

        let (tantivy_index, tracer) =
            trace_if(log::Level::Info, &tracer, "Initialize tantivy", |_child| {
                let mut tantivy_index = tantivy::Index::open(directory.box_clone())?;
                tantivy_index.set_shared_multithread_executor(ctx.executor.clone())?;
                let tracer = tracer.clone();

                Ok::<_, tantivy::TantivyError>((tantivy_index, tracer))
            })?;

        // If there are extra root span ids that matched the realtime search, then we should
        // incorporate them into the search query.
        if !matched_root_span_ids.is_empty() {
            let root_span_id_field = tantivy_index
                .schema()
                .get_field(ROOT_SPAN_ID_FIELD)
                .context("Root span ID field not found in schema")?;
            let extra_filter = Box::new(TermSetQuery::new(
                matched_root_span_ids
                    .iter()
                    .map(|t| tantivy::Term::from_field_text(root_span_id_field, t)),
            )) as Box<dyn tantivy::query::Query>;

            search_query = Box::new(tantivy::query::BooleanQuery::new(vec![
                (tantivy::query::Occur::Should, search_query),
                (tantivy::query::Occur::Should, extra_filter),
            ]))
        }

        ctx.set_index_state(InterpreterContextState {
            index_wal_reader: index_wal_reader.clone(),
            tantivy_schema: tantivy_index.schema().clone(),
            tantivy_index: tantivy_index.clone(),
            tantivy_readers: vec![], // Unset, because downstream consumers won't use these (the operator is not streaming).
        });

        let reader = trace_if(log::Level::Info, &tracer, "Open reader", |child| {
            child.increment_counter(
                "num_chunks",
                tantivy_index.searchable_segment_metas()?.len() as u64,
            );
            tantivy_index.reader()
        })?;
        let searcher = reader.searcher();

        let search_query = if tantivy_index.searchable_segment_metas()?.len() == 1
            && unmatched_root_span_ids.is_empty()
        {
            let collector = RootSpanDocIdCollector {
                tracer: tracer.clone(),
            };
            searcher.search_with_executor(
                &search_query,
                &collector,
                &ctx.executor,
                EnableScoring::disabled_from_searcher(&searcher),
            )?
        } else {
            let root_span_id_collector = RootSpanCollector {
                tracer: tracer.clone(),
            };
            let root_span_ids = searcher.search_with_executor(
                &search_query,
                &root_span_id_collector,
                &ctx.executor,
                EnableScoring::disabled_from_searcher(&searcher),
            )?;

            let root_span_id_field = tantivy_index.schema().get_field(ROOT_SPAN_ID_FIELD)?;
            let mut terms = Vec::new();
            for root_span_id in &root_span_ids {
                // We know these are strings and don't need to waste time utf-8 validating them.
                let s = unsafe { std::str::from_utf8_unchecked(root_span_id) };

                // Include as a term in the expansion search
                terms.push(tantivy::Term::from_field_text(root_span_id_field, s));

                // If this root span is in the realtime search and not already matched via the
                // filter, then we should include it in the final result.
                if let Some(root_span_id) = unmatched_root_span_ids.take(s) {
                    matched_root_span_ids.insert(root_span_id);
                }
            }

            Box::new(TermSetQuery::new(terms)) as Box<dyn tantivy::query::Query>
        };

        let search_query = index_wal_reader.wrap_exclude_query(search_query);

        let docs = trace_if(log::Level::Info, &tracer, "Search and aggregate", |child| {
            Self::execute_search_and_aggregate(
                &searcher,
                &search_query,
                ctx.clone(),
                projection,
                unpivots,
                dimensions,
                aggregator_base,
                group_by,
                child,
            )
        })?;

        Ok::<_, InterpreterError>((docs, matched_root_span_ids))
    }

    async fn open_index_wal_reader(
        &self,
        ctx: &Arc<InterpreterContext>,
        tracer: &Option<Arc<TracedNode>>,
        partition_all: bool,
    ) -> Result<storage::index_wal_reader::IndexWalReader> {
        trace_if_async(log::Level::Info, tracer, "Open index wal reader", |child| {
            storage::index_wal_reader::IndexWalReader::new(
                IndexWalReaderInput {
                    config_with_store: &ctx.config,
                    full_schema: ctx.schema.clone(),
                    object_ids: &self.object_ids,
                    filters: &self.segment_filters,
                    sort: &None,
                    partition_all,
                    sampling: None,
                },
                IndexWalReaderOptionalInput {
                    tantivy_executor: Some(ctx.executor.clone()),
                    tracer: child,
                    ..Default::default()
                },
                IndexWalReaderOpts {
                    // The aggregate query does not know how to handle segment batches
                    initial_segment_batch_size: Some(0),
                    ..ctx.opts.index_wal_reader_opts.clone()
                },
            )
        })
        .await
        .map_err(|e| e.into())
    }

    fn create_aggregator_base(&self) -> Vec<ExprAggregator> {
        self.expr_aggs.iter().map(|(_, agg)| agg.clone()).collect()
    }

    fn create_dimensions(&self) -> Arc<Vec<Alias>> {
        Arc::new(
            self.dimensions
                .iter()
                .chain(self.pivot.iter())
                .cloned()
                .collect::<Vec<_>>(),
        )
    }

    fn execute_search_and_aggregate(
        searcher: &tantivy::Searcher,
        search_query: &dyn tantivy::query::Query,
        ctx: Arc<InterpreterContext>,
        projection: Vec<TantivyProjectedField>,
        unpivots: Vec<UnpivotProjectionExpr>,
        dimensions: Arc<Vec<Alias>>,
        aggregator_base: Vec<ExprAggregator>,
        group_by: GroupByStrategy,
        tracer: Option<Arc<TracedNode>>,
    ) -> tantivy::Result<HashMap<Vec<Value>, Vec<ValueAggregator>>> {
        match group_by {
            GroupByStrategy::Dynamic => searcher.search_with_executor(
                search_query,
                &ExprAggregateCollector {
                    ctx: ctx.clone(),
                    projection,
                    unpivots,
                    dimensions: dimensions.clone(),
                    aggregator_base,
                    tracer,
                },
                &ctx.executor,
                EnableScoring::disabled_from_searcher(&searcher),
            ),
            GroupByStrategy::NoDims { aggs } => match searcher.search_with_executor(
                search_query,
                &NoDimsCollector::new(ctx.clone(), tracer.clone(), projection.clone(), aggs),
                &ctx.executor,
                EnableScoring::disabled_from_searcher(&searcher),
            ) {
                Ok(docs) => Ok(docs),
                Err(tantivy::TantivyError::AggregationError(
                    tantivy::aggregation::AggregationError::InvalidRequest(e),
                )) => {
                    log::debug!(
                        "Failed to execute static group by, falling back to dynamic: {}",
                        e
                    );
                    // Fall back to a dynamic group by if we return an AggregationError
                    searcher.search_with_executor(
                        search_query,
                        &ExprAggregateCollector {
                            ctx: ctx.clone(),
                            projection,
                            unpivots,
                            dimensions: dimensions.clone(),
                            aggregator_base,
                            tracer,
                        },
                        &ctx.executor,
                        EnableScoring::disabled_from_searcher(&searcher),
                    )
                }
                Err(e) => Err(e),
            },
            GroupByStrategy::Dims { dims, aggs } => {
                match execute_tuple_dim_group_by(
                    &searcher,
                    search_query,
                    ctx.clone(),
                    tracer.clone(),
                    projection.clone(),
                    dims,
                    aggs,
                ) {
                    Ok(docs) => Ok(docs),
                    Err(tantivy::TantivyError::AggregationError(
                        tantivy::aggregation::AggregationError::InvalidRequest(e),
                    )) => {
                        log::debug!(
                            "Failed to execute static group by, falling back to dynamic: {}",
                            e
                        );
                        // Fall back to a dynamic group by if we return an AggregationError
                        searcher.search_with_executor(
                            search_query,
                            &ExprAggregateCollector {
                                ctx: ctx.clone(),
                                projection,
                                unpivots,
                                dimensions: dimensions.clone(),
                                aggregator_base,
                                tracer,
                            },
                            &ctx.executor,
                            EnableScoring::disabled_from_searcher(&searcher),
                        )
                    }
                    Err(e) => Err(e),
                }
            }
        }
    }
}

pub struct ExprAggregateCollector {
    ctx: Arc<InterpreterContext>,
    tracer: Option<Arc<TracedNode>>,
    projection: Vec<TantivyProjectedField>,
    unpivots: Vec<UnpivotProjectionExpr>,
    aggregator_base: Vec<ExprAggregator>,

    dimensions: Arc<Vec<Alias>>,
}

impl Collector for ExprAggregateCollector {
    type Fruit = HashMap<Vec<Value>, Vec<ValueAggregator>>;
    type Child = AggregateSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<AggregateSegmentCollector> {
        self.ctx.check_cancelled().map_err(|e| {
            tantivy::TantivyError::InternalError(format!("Aggregate query was cancelled: {}", e))
        })?;

        let (top_level_fields, json_fields, json_path_fields) =
            split_projected_fields_for_columnstore(&self.projection);

        let columnstore_readers = open_columnstore_field_readers(
            &top_level_fields,
            &json_fields,
            segment_reader,
            self.tracer.clone(),
        )?;

        let columns = columnstore_readers
            .into_iter()
            .map(|(field, path, column, is_pagination_key)| {
                (field.clone(), path, column, is_pagination_key)
            })
            .collect::<Vec<_>>();

        let exists_readers = open_columnstore_exists_path_readers(
            &json_path_fields,
            segment_reader,
            self.tracer.clone(),
        )?;

        let unpivots = trace_if(
            log::Level::Info,
            &self.tracer,
            "Open unpivot readers",
            |_child| {
                let fast_fields = segment_reader.fast_fields();
                let fast_fields_impl = get_fast_fields_impl(fast_fields);
                let columnstore_readers = &fast_fields_impl.columnar;

                let mut unpivots = vec![Vec::new(); self.unpivots.len()];
                for (
                    i,
                    UnpivotProjectionExpr {
                        base_field,
                        projected_field,
                        ..
                    },
                ) in self.unpivots.iter().enumerate()
                {
                    let mut prefix = base_field.clone();
                    if matches!(projected_field, UnpivotProjectedField::Object { .. }) {
                        prefix.push(JSON_PATH_SEGMENT_SEP as char);
                        prefix.push_str(JSON_ROOT_FIELD);
                    }

                    let columns = get_columns_matching_prefix(columnstore_readers, &prefix)?;
                    for (name, col) in columns {
                        let unpivot_expr = match projected_field {
                            UnpivotProjectedField::Array { item } => {
                                UnpivotProjectedFieldWithName::Array {
                                    item_name: item.clone(),
                                }
                            }
                            UnpivotProjectedField::Object { key, value } => {
                                let name_piece = name
                                    .split(JSON_PATH_SEGMENT_SEP as char)
                                    .next_back()
                                    .expect("at least one name piece");

                                UnpivotProjectedFieldWithName::Object {
                                    key_name: key.clone(),
                                    value_name: value.clone(),
                                    key_value: name_piece.to_string(),
                                }
                            }
                        };
                        unpivots[i].push((
                            unpivot_expr,
                            col.open()
                                .map_err(|e| tantivy::TantivyError::IoError(Arc::new(e)))?,
                        ));
                    }
                }

                Ok::<_, tantivy::TantivyError>(unpivots)
            },
        )?;

        let child = self
            .tracer
            .as_ref()
            .map(|tracer| tracer.new_child("Dynamic segment collector"));
        Ok(AggregateSegmentCollector {
            ctx: ColumnarExprContext::from_interpreter_ctx(&self.ctx),
            tracer: child,
            processed: 0,
            columns,
            exists_readers,
            dimensions: self.dimensions.clone(),
            unpivots,
            aggregator_base: self.aggregator_base.clone(),
            fruit: FastHashMap::new(),
            error: None,
            docs_and_indices: vec![],
        })
    }

    fn merge_fruits(
        &self,
        segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        trace_if(log::Level::Info, &self.tracer, "Merge buckets", |_child| {
            let mut buckets: HashMap<Vec<Value>, Vec<ValueAggregator>> = HashMap::new();
            for segment_fruit in segment_fruits {
                let segment_fruit = segment_fruit?;
                for (dimensions, aggregators) in segment_fruit {
                    update_buckets(
                        &mut buckets,
                        dimensions,
                        aggregators
                            .into_iter()
                            .map(|agg| agg.into_value_aggregator()),
                    )?;
                }
            }
            Ok::<_, tantivy::TantivyError>(buckets)
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }
}

#[inline]
fn update_buckets(
    buckets: &mut HashMap<Vec<Value>, Vec<ValueAggregator>>,
    dimensions: Vec<Value>,
    aggregators: impl IntoIterator<Item = ValueAggregator>,
) -> Result<(), TantivyError> {
    match buckets.entry(dimensions) {
        std::collections::hash_map::Entry::Occupied(mut entry) => {
            entry
                .get_mut()
                .iter_mut()
                .zip(aggregators.into_iter())
                .map(|(a, b)| a.combine(b))
                .collect::<Result<Vec<_>, _>>()
                .map_err(|e| tantivy::error::TantivyError::InternalError(e.to_string()))?;
        }
        std::collections::hash_map::Entry::Vacant(entry) => {
            entry.insert(aggregators.into_iter().collect());
        }
    }

    Ok(())
}

// This is the most dynamic form of an aggregator. We can compile more specific versions (e.g only dimensions,
// only measures, etc) to achieve better performance.
pub struct AggregateSegmentCollector {
    ctx: ColumnarExprContext,
    tracer: Option<Arc<TracedNode>>,
    processed: u64,

    // Each projected field and the corresponding column that contains the columnar values
    // for that field in this segment.
    columns: Vec<(
        TantivyProjectedField,
        Vec<String>,
        DynamicColumn,
        IsPaginationKeyField,
    )>,

    // Each exists prefix and the corresponding column that contains the columnar values
    // for that field in this segment.
    exists_readers: Vec<(DynamicColumn, Vec<(Vec<String>, ExistsPrefix)>)>,

    // Each unpivot gets expanded into multiple values
    unpivots: Vec<Vec<(UnpivotProjectedFieldWithName, DynamicColumn)>>,

    dimensions: Arc<Vec<Alias>>,
    aggregator_base: Vec<ExprAggregator>,

    fruit: FastHashMap<Vec<Value>, Vec<ExprAggregator>>,
    error: Option<TantivyError>,

    // Internal state that is reused across batches
    docs_and_indices: Vec<(u32, usize)>,
}

impl SegmentCollector for AggregateSegmentCollector {
    type Fruit = Result<FastHashMap<Vec<Value>, Vec<ExprAggregator>>, tantivy::TantivyError>;

    fn collect(&mut self, doc: tantivy::DocId, _score: tantivy::Score) {
        self.collect_block(&[doc]);
    }

    fn collect_block(&mut self, docs: &[tantivy::DocId]) {
        match &mut self.error {
            Some(_) => { /*skip executing */ }
            None => {
                match self.collect_block_fallible(docs) {
                    Ok(_) => {}
                    Err(e) => {
                        self.error = Some(e);
                    }
                };
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        self.tracer
            .increment_counter("docs_processed", self.processed);
        self.tracer
            .increment_counter("buckets", self.fruit.len() as u64);
        if let Some(tracer) = self.tracer {
            tracer.end();
        }
        match self.error {
            Some(e) => Err(e),
            None => Ok(self.fruit),
        }
    }
}

impl AggregateSegmentCollector {
    fn collect_block_fallible(&mut self, docs: &[u32]) -> Result<(), tantivy::TantivyError> {
        let mut rows = docs
            .iter()
            .map(|_| Value::Object(serde_json::Map::new()))
            .collect::<Vec<_>>();

        self.docs_and_indices.resize(docs.len(), (0, 0));
        for (idx, doc) in docs.iter().enumerate() {
            self.docs_and_indices[idx] = (*doc, idx);
        }

        for (col, fields) in self.exists_readers.iter() {
            let values = read_columnstore_values(
                &self.docs_and_indices,
                col,
                &IsPaginationKeyField(false),
                true,
            )?;

            for (idx, value) in values.iter() {
                for (path, prefix) in fields {
                    for value in value.as_array().expect("repeated field should be an array") {
                        if value
                            .as_str()
                            .expect("Expected path exists field to be a string (this is a bug)")
                            .starts_with(&prefix.0)
                        {
                            set_value_at_path(&mut rows[*idx], path, Value::Bool(true))
                                .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
                            break;
                        }
                    }
                }
            }
        }

        for (field, path, col, is_pagination_key) in self.columns.iter() {
            let values = read_columnstore_values(
                &self.docs_and_indices,
                col,
                is_pagination_key,
                field.repeated,
            )?;

            for (idx, value) in values {
                match set_value_at_path(&mut rows[idx], &path, value) {
                    Ok(_) => (),
                    Err(e) => {
                        // NOTE: We deem this as acceptable, for now, because it shows up when someone does something like
                        //  measures: COUNT(error)
                        // where all we care about is if `error` is present or not. In general, aggregates on JSON values is kind of
                        // murky territory, and we can be more rigorous about how we define the semantics.
                        log::debug!("Could not set value at path: {:?}. This is likely because the field is not invertible. Will be an incomplete row: {}", &path, e);
                    }
                }
            }
        }

        let docs = if self.unpivots.is_empty() {
            rows
        } else {
            let base_rows = rows.into_iter().map(|row| {
                Ok(match row {
                    Value::Object(row) => row,
                    Value::Null => serde_json::Map::new(),
                    o => {
                        return Err(tantivy::TantivyError::InternalError(format!(
                            "Expected an object, got {:?}",
                            o
                        )));
                    }
                })
            });

            let mut final_rows = Vec::new();
            for (row, doc) in base_rows.zip(docs.iter()) {
                let row = row?;

                // NOTE: We can probably do some kind of product iteration here to avoid so many allocations, and instead
                // recurse through each level of unpivot
                let mut rows = vec![row];
                for unpivot_set in self.unpivots.iter() {
                    let mut new_rows = Vec::new();
                    for (unpivot_expr, column_handle) in unpivot_set.iter() {
                        let value = collect_column_for_doc(column_handle, *doc);
                        let value = match value {
                            None => continue,
                            Some(value) => {
                                convert_tantivy_value(&value, &[], false).unwrap_or(Value::Null)
                            }
                        };
                        for row in rows.iter() {
                            match unpivot_expr {
                                UnpivotProjectedFieldWithName::Array { item_name } => {
                                    match &value {
                                        Value::Array(arr) => {
                                            for item in arr {
                                                let mut new_row = row.clone();
                                                new_row.insert(item_name.clone(), item.clone());
                                                new_rows.push(new_row);
                                            }
                                        }
                                        _ => {
                                            let mut new_row = row.clone();
                                            new_row.insert(item_name.clone(), value.clone());
                                            new_rows.push(new_row);
                                        }
                                    }
                                }
                                UnpivotProjectedFieldWithName::Object {
                                    key_name,
                                    value_name,
                                    key_value,
                                } => {
                                    let mut new_row = row.clone();
                                    new_row.insert(
                                        key_name.clone(),
                                        Value::String(key_value.to_string()),
                                    );
                                    new_row.insert(value_name.clone(), value.clone());
                                    new_rows.push(new_row);
                                }
                            }
                        }
                    }
                    rows = new_rows;
                }
                final_rows.extend(rows.into_iter().map(Value::Object));
            }
            final_rows
        };

        for row in docs.iter() {
            let dimensions = self
                .dimensions
                .iter()
                .map(|alias| {
                    interpret_expr(self.ctx.expr_ctx(), &alias.expr, &[Cow::Borrowed(row)])
                        .map_or_else(
                            |e| {
                                log::warn!(
                                    "Error processing dimension {:?}, returning null: {:?}",
                                    alias,
                                    e
                                );
                                Value::Null
                            },
                            |mut v| v.remove(0).into_owned(),
                        )
                })
                .collect::<Vec<_>>();

            let aggs = self
                .fruit
                .entry(dimensions)
                .or_insert_with(|| self.aggregator_base.clone());
            for agg in aggs.iter_mut() {
                agg.aggregate_value(&self.ctx, Cow::Borrowed(row))
                    .unwrap_or_else(|e| {
                        log::warn!("Error processing aggregator: {:?}", e);
                    });
            }
        }

        self.processed += docs.len() as u64;

        Ok(())
    }
}

#[derive(Debug, Clone)]
enum UnpivotProjectedFieldWithName {
    Array {
        item_name: String,
    },
    Object {
        key_name: String,
        value_name: String,
        key_value: String,
    },
}

// NOTE: This must exactly match https://github.com/quickwit-oss/tantivy/blob/17d5869ad61ea9f1072ef40546ddcada3a14b067/src/fastfield/readers.rs#L22
#[derive(Clone)]
pub struct FastFieldReadersImpl {
    #[allow(dead_code)]
    pub columnar: Arc<ColumnarReader>,
    #[allow(dead_code)]
    pub schema: tantivy::schema::Schema,
}

pub fn get_fast_fields_impl(fast_fields: &FastFieldReaders) -> &FastFieldReadersImpl {
    unsafe { std::mem::transmute::<&FastFieldReaders, &FastFieldReadersImpl>(fast_fields) }
}

fn realtime_groupby(
    tracer: Option<Arc<TracedNode>>,
    ctx: Arc<InterpreterContext>,
    docs: &[Value],
    realtime_search: &RealtimeWALSearchQuery,
    dimensions: &[Alias],
    pivot: &[Alias],
    aggregator_base: &Vec<ExprAggregator>,
) -> Result<HashMap<Vec<Value>, Vec<ValueAggregator>>> {
    let projected_realtime_rows =
        trace_if(log::Level::Info, &tracer, "Filter and project", |child| {
            filter_project_in_memory_docs(child, ctx.clone(), docs, &realtime_search, None)
        })?;

    let cow_docs = projected_realtime_rows
        .into_iter()
        .map(|d| Cow::Owned(d.0))
        .collect::<Vec<_>>();

    let mut realtime_segment = HashMap::new();
    perform_groupby(
        &ctx,
        &mut realtime_segment,
        &cow_docs,
        &dimensions,
        &pivot,
        &aggregator_base,
    )?;

    Ok(expr_buckets_to_value_buckets(realtime_segment))
}

pub fn split_projected_fields_for_columnstore(
    projected_fields: &Vec<TantivyProjectedField>,
) -> (
    HashMap<String, &TantivyProjectedField>,
    HashMap<String, &TantivyProjectedField>,
    // The String is a prefix that represents the path
    HashMap<String, Vec<(&TantivyProjectedField, String)>>,
) {
    let mut top_level_fields = HashMap::new();
    let mut json_fields = HashMap::new();
    let mut json_path_fields = HashMap::new();
    for f in projected_fields {
        match f.field_type {
            TantivyType::Json(_) => {
                let mut full_path = f.top_level_field.clone();
                full_path.push(JSON_PATH_SEGMENT_SEP as char);

                if f.only_exists {
                    full_path.push_str(JSON_PATHS_FIELD);

                    json_path_fields
                        .entry(full_path)
                        .or_insert_with(Vec::new)
                        .push((
                            f,
                            util::json::serialize_path(f.json_path.iter().map(|p| p.to_string())),
                        ));
                } else {
                    full_path.push_str(JSON_ROOT_FIELD);
                    for p in f.json_path.iter() {
                        match p {
                            PathPiece::Index(_) => break,
                            PathPiece::Key(key) => {
                                full_path.push(JSON_PATH_SEGMENT_SEP as char);
                                full_path.push_str(key);
                            }
                        }
                    }
                    json_fields.insert(full_path, f);
                }
            }
            _ => {
                top_level_fields.insert(f.top_level_field.clone(), f);
            }
        };
    }
    (top_level_fields, json_fields, json_path_fields)
}

#[derive(Debug, Clone)]
pub struct ExistsPrefix(pub String);

pub fn open_columnstore_exists_path_readers(
    json_path_fields: &HashMap<String, Vec<(&TantivyProjectedField, String)>>,
    segment_reader: &tantivy::SegmentReader,
    tracer: Option<Arc<TracedNode>>,
) -> Result<
    Vec<(
        tantivy::columnar::DynamicColumn,
        Vec<(Vec<String>, ExistsPrefix)>,
    )>,
    tantivy::TantivyError,
> {
    let fast_fields = segment_reader.fast_fields();
    let fast_fields_impl = get_fast_fields_impl(fast_fields);

    let mut path_and_fields = Vec::new();

    trace_if(
        log::Level::Info,
        &tracer,
        "Open columnstore exists path readers",
        |_child| {
            for (name, fields) in json_path_fields {
                let matching_cols = fast_fields_impl.columnar.read_columns(name)?;
                if matching_cols.is_empty() {
                    continue;
                } else if matching_cols.len() > 1 {
                    return Err(tantivy::TantivyError::InternalError(format!(
                        "Expected at most one column for path {}, got {}",
                        name,
                        matching_cols.len()
                    )));
                }

                path_and_fields.push((
                    matching_cols[0].open()?,
                    fields
                        .iter()
                        .map(|(field, prefix)| {
                            (vec![field.alias.clone()], ExistsPrefix(prefix.clone()))
                        })
                        .collect::<Vec<_>>(),
                ));
            }

            Ok::<_, tantivy::TantivyError>(())
        },
    )?;

    Ok(path_and_fields)
}

// RootSpanDocIdCollector is an optimization over RootSpanCollector when we know the brainstore
// segment contains exactly 1 tantivy segment.
//
// When that is the case, all span_ids are string dictionary encoded in the same segment. We can
// find all spans that pass the filter, and find their span_id ordinals from that.
// We can then find all spans with that span_id ordinal quickly.
//
// When there is more than one tantivy segment per brainstore segment, the ordinals are not
// comparable between segments. We simply do a term query search for all passing span_ids to
// find the relevant spans.
pub struct RootSpanCollector {
    tracer: Option<Arc<TracedNode>>,
}

impl Collector for RootSpanCollector {
    type Fruit = FastHashSet<Vec<u8>>;
    type Child = FallibleSegmentCollector<RootSpanSegmentCollector>;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let fast_fields = segment_reader.fast_fields();
        let root_span_id_column = fast_fields.str(ROOT_SPAN_ID_FIELD)?.ok_or_else(|| {
            tantivy::TantivyError::SchemaError("root_span_id field not found".to_string())
        })?;

        Ok(FallibleSegmentCollector::new(
            RootSpanSegmentCollector {
                root_span_id_column,
                ords: BTreeSet::new(),
            },
            self.tracer
                .as_ref()
                .map(|tracer| tracer.new_child("Segment collector")),
        ))
    }

    fn merge_fruits(
        &self,
        segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        let mut ret = FastHashSet::default();
        for fruit in segment_fruits {
            for value in fruit? {
                ret.insert(value);
            }
        }

        Ok(ret)
    }

    fn requires_scoring(&self) -> bool {
        false
    }
}

pub struct RootSpanSegmentCollector {
    root_span_id_column: StrColumn,
    ords: BTreeSet<u64>,
}

impl FallibleCollector for RootSpanSegmentCollector {
    type Fruit = Vec<Vec<u8>>;

    fn collect_block_fallible(&mut self, docs: &[tantivy::DocId]) -> tantivy::Result<()> {
        for doc in docs.iter() {
            let ords = self.root_span_id_column.term_ords(*doc);
            for ord in ords {
                self.ords.insert(ord);
            }
        }

        Ok(())
    }

    fn into_fruit(self) -> Self::Fruit {
        let mut ord_vec = self.ords.into_iter().collect();
        batch_ordinals_decompress(&mut ord_vec, self.root_span_id_column.dictionary())
            .expect("Failed to decompress")
    }
    fn fruit_len(fruit: &Self::Fruit) -> usize {
        fruit.len()
    }
}

pub struct RootSpanDocIdCollector {
    tracer: Option<Arc<TracedNode>>,
}

impl Collector for RootSpanDocIdCollector {
    type Fruit = Box<dyn tantivy::query::Query>;
    type Child = FallibleSegmentCollector<RootSpanDocIdSegmentCollector>;

    fn for_segment(
        &self,
        segment_ord: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let fast_fields = segment_reader.fast_fields();
        let root_span_id_column = fast_fields.str(ROOT_SPAN_ID_FIELD)?.ok_or_else(|| {
            tantivy::TantivyError::SchemaError("root_span_id field not found".to_string())
        })?;

        Ok(FallibleSegmentCollector::new(
            RootSpanDocIdSegmentCollector {
                root_span_id_column,
                ords: BTreeSet::new(),
                doc_id_range: 0u32..segment_reader.max_doc(),
                segment_ord,
            },
            self.tracer
                .as_ref()
                .map(|tracer| tracer.new_child("Segment collector")),
        ))
    }

    fn merge_fruits(
        &self,
        mut segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        Ok(Box::new(DocIdQuery::new(segment_fruits.remove(0)?.1))
            as Box<dyn tantivy::query::Query>)
    }

    fn requires_scoring(&self) -> bool {
        false
    }
}

pub struct RootSpanDocIdSegmentCollector {
    root_span_id_column: StrColumn,
    segment_ord: SegmentOrdinal,
    ords: BTreeSet<u64>,
    doc_id_range: Range<u32>,
}

impl FallibleCollector for RootSpanDocIdSegmentCollector {
    type Fruit = (SegmentOrdinal, BitSet);

    fn collect_block_fallible(&mut self, docs: &[tantivy::DocId]) -> tantivy::Result<()> {
        for doc in docs.iter() {
            let ords = self.root_span_id_column.term_ords(*doc);
            for ord in ords {
                self.ords.insert(ord);
            }
            self.doc_id_range.start = self.doc_id_range.start.min(*doc);
            self.doc_id_range.end = self.doc_id_range.end.max(*doc);
        }

        Ok(())
    }

    fn into_fruit(self) -> Self::Fruit {
        if self.ords.is_empty() {
            return (self.segment_ord, BitSet::with_max_value(1));
        }

        // Each time we get_docids_for_value_range(), we overwrite the vector
        // we pass in. So instead we keep stashing them in all_doc_ids. We know
        // we'll have at least as many documents as ordinals, so let's allocate that
        // much memory up front.
        let mut all_doc_ids = Vec::with_capacity(self.ords.len());
        let mut buf_doc_ids = Vec::new();

        let mut iter = self.ords.into_iter();
        let mut start = iter.next().unwrap();
        let mut end = start;

        for ord in iter {
            if ord == end + 1 {
                // Extend current range
                end = ord;
            } else {
                // Process current range and start new one
                buf_doc_ids.clear();
                self.root_span_id_column.ords().get_docids_for_value_range(
                    start..=end,
                    self.doc_id_range.clone(),
                    &mut buf_doc_ids,
                );
                all_doc_ids.extend_from_slice(&buf_doc_ids);
                start = ord;
                end = ord;
            }
        }
        // Process the last range
        buf_doc_ids.clear();
        self.root_span_id_column.ords().get_docids_for_value_range(
            start..=end,
            self.doc_id_range,
            &mut buf_doc_ids,
        );
        all_doc_ids.extend_from_slice(&buf_doc_ids);

        let max_doc = all_doc_ids.iter().max().copied().unwrap_or(0) + 1;
        let mut bit_set = BitSet::with_max_value(max_doc);
        for doc in all_doc_ids {
            bit_set.insert(doc);
        }

        (self.segment_ord, bit_set)
    }

    fn fruit_len(fruit: &Self::Fruit) -> usize {
        fruit.1.len()
    }
}

struct DocIdQuery {
    records: BitSet,
}

impl std::fmt::Debug for DocIdQuery {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DocIdQuery").finish()
    }
}

impl DocIdQuery {
    pub fn new(records: BitSet) -> Self {
        Self { records }
    }
}

impl tantivy::query::QueryClone for DocIdQuery {
    fn box_clone(&self) -> Box<dyn tantivy::query::Query> {
        Box::new(Self {
            records: self.records.clone(),
        })
    }
}

impl tantivy::query::Query for DocIdQuery {
    fn weight(
        &self,
        _enable_scoring: EnableScoring<'_>,
    ) -> tantivy::Result<Box<dyn tantivy::query::Weight>> {
        Ok(Box::new(DocIdWeight {
            records: self.records.clone(),
        }))
    }
}

struct DocIdWeight {
    records: BitSet,
}

impl tantivy::query::Weight for DocIdWeight {
    fn scorer(
        &self,
        _reader: &SegmentReader,
        boost: tantivy::Score,
    ) -> tantivy::Result<Box<dyn tantivy::query::Scorer>> {
        let doc_set: BitSetDocSet = self.records.clone().into();
        Ok(Box::new(ConstScorer::new(doc_set, boost)) as Box<dyn tantivy::query::Scorer>)
    }

    fn explain(
        &self,
        _reader: &SegmentReader,
        _doc: tantivy::DocId,
    ) -> tantivy::Result<tantivy::query::Explanation> {
        Ok(tantivy::query::Explanation::new_with_string(
            "DocIdWeight".to_string(),
            0.0,
        ))
    }
}
