use crate::{interpreter::columnar::hash_map::FastHashSet, optimizer::ast::TantivySampler};
use rand::prelude::*;
use rand::rngs::StdRng;
use rand::SeedableRng;
use tantivy::{
    collector::{Collector, SegmentCollector},
    columnar::StrColumn,
};

pub struct SamplingCollector<T, C>
where
    T: Send,
    C: Collector<Fruit = Vec<(T, tantivy::DocAddress)>>,
{
    pub inner: C,
    pub sample: TantivySampler,
}

impl<T, C> Collector for SamplingCollector<T, C>
where
    T: Send + 'static,
    C: Collector<Fruit = Vec<(T, tantivy::DocAddress)>>,
{
    type Fruit = C::Fruit;
    type Child = SchemaInferenceSegmentCollector<C::Child>;

    fn for_segment(
        &self,
        segment_local_id: u32,
        segment: &tantivy::SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let deduplicate_by = match &self.sample.deduplicate_by {
            Some(field) => {
                let fast_field = segment.fast_fields().str(&field.name)?;
                match fast_field {
                    Some(fast_field) => Some(fast_field),
                    None => {
                        return Err(tantivy::TantivyError::SchemaError(format!(
                            "Field '{}' is not a string, cannot sample by it",
                            field.name
                        )));
                    }
                }
            }
            None => None,
        };

        let num_docs = match &deduplicate_by {
            Some(col) => col.ords().max_value() as usize,
            None => segment.max_doc() as usize,
        };

        let effective_sample_rate = if num_docs == 0 {
            0.0
        } else {
            match self.sample.method {
                btql::binder::ast::SampleMethod::Rate { value } => value.clamp(0.0, 1.0),
                btql::binder::ast::SampleMethod::Count { value } => {
                    // For count sampling, use a reasonable rate to avoid collecting all documents
                    // Estimate that we want roughly the target count spread across segments
                    // Use a higher rate to account for deduplication and variance across segments
                    let target_count = value as f64;
                    let estimated_rate = (target_count * 2.0) / (num_docs as f64).max(1.0);
                    estimated_rate.min(1.0)
                }
            }
        };

        Ok(SchemaInferenceSegmentCollector {
            inner: self.inner.for_segment(segment_local_id, segment)?,
            effective_sample_rate,
            sample_method: self.sample.method.clone(),
            deduplicate_by,
            seen_ords: FastHashSet::default(),
            filtered_docs: Vec::new(),
            rng: match self.sample.seed {
                Some(seed) => StdRng::seed_from_u64(seed),
                None => StdRng::from_rng(thread_rng()).unwrap(),
            },
        })
    }

    fn merge_fruits(
        &self,
        children: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        let merged = self.inner.merge_fruits(children)?;
        Ok(merged)
    }

    fn requires_scoring(&self) -> bool {
        self.inner.requires_scoring()
    }
}

pub struct SchemaInferenceSegmentCollector<TSegmentCollector: SegmentCollector> {
    pub inner: TSegmentCollector,
    pub effective_sample_rate: f64,
    pub sample_method: btql::binder::ast::SampleMethod,
    pub deduplicate_by: Option<StrColumn>,
    pub rng: StdRng,

    pub seen_ords: FastHashSet<u64>,
    pub filtered_docs: Vec<tantivy::DocId>,
}

impl<TSegmentCollector: SegmentCollector> SegmentCollector
    for SchemaInferenceSegmentCollector<TSegmentCollector>
{
    type Fruit = TSegmentCollector::Fruit;

    fn collect(&mut self, doc: tantivy::DocId, _score: f32) {
        self.collect_block(&[doc]);
    }

    fn collect_block(&mut self, docs: &[tantivy::DocId]) {
        for doc in docs.iter() {
            let should_collect = if let Some(deduplicate_by) = &self.deduplicate_by {
                let mut ords = deduplicate_by.term_ords(*doc);
                let first_ord = ords.next().unwrap_or(u64::MAX);
                // If this is a new ordinal, then use this as the representative document for it.
                self.seen_ords.insert(first_ord)
            } else {
                true // Collect all for non-dedup case
            };

            if should_collect {
                self.filtered_docs.push(*doc);
            }
        }
    }

    fn harvest(mut self) -> TSegmentCollector::Fruit {
        // Now apply sampling to the unique documents
        let target_count = match &self.sample_method {
            btql::binder::ast::SampleMethod::Rate { .. } => {
                // For rate sampling, use probabilistic approach
                if self.effective_sample_rate >= 1.0 {
                    self.filtered_docs.len()
                } else {
                    let exact_target = self.filtered_docs.len() as f64 * self.effective_sample_rate;
                    let base_count = exact_target.floor() as usize;
                    let fractional_part = exact_target - exact_target.floor();

                    // With probability equal to the fractional part, add one more document
                    if fractional_part > 0.0 && self.rng.gen::<f64>() < fractional_part {
                        base_count + 1
                    } else {
                        base_count
                    }
                }
            }
            btql::binder::ast::SampleMethod::Count { value } => {
                // For count sampling, ensure we get at least the requested count
                // But don't exceed available documents
                (*value as usize).min(self.filtered_docs.len())
            }
        };

        if target_count > 0 && !self.filtered_docs.is_empty() {
            // Shuffle and take exactly the number we need
            self.filtered_docs.shuffle(&mut self.rng);
            self.filtered_docs.truncate(target_count);
        } else if target_count == 0 {
            self.filtered_docs.clear();
        }

        // Collect the filtered documents
        if !self.filtered_docs.is_empty() {
            self.inner.collect_block(&self.filtered_docs);
        }

        self.inner.harvest()
    }
}
