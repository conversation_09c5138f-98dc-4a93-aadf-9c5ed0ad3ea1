use base64::prelude::*;
use lazy_static::lazy_static;
use std::{
    borrow::Cow,
    collections::{HashMap, VecDeque},
    sync::Arc,
};

use async_util::spawn_blocking_util::spawn_blocking_with_async_timeout;
use btql::{
    typesystem::CastInto,
    util::{Cursor, CursorDirection, CursorValue},
};
use storage::{
    index_wal_reader::{IndexWalReaderInput, IndexWalReaderOptionalInput},
    process_wal::{PAGINATION_KEY_FIELD, XACT_ID_FIELD},
    segment_batches::SamplingMode,
    tantivy_index::{make_tantivy_schema, pagination_key_from_raw_tantivy, JSON_ROOT_FIELD},
};
use tantivy::{collector::Collector, directory::DirectoryClone, query::EnableScoring, DocAddress};
use tokio::{runtime::Handle, sync::mpsc};
use util::{
    anyhow::{anyhow, Context},
    json::{set_value_at_path, PathPiece},
    schema::{TantivyFastValueType, TantivyType},
    tracer::{trace_if, trace_if_async, EnterTraceGuard, TracedNode},
    xact::PaginationKey,
    Value,
};

use crate::{
    interpreter::{
        context::{InterpreterContextState, EMPTY_CURSOR_VALUE},
        limiter::{make_key_fn, project_batch, SortValueDir},
        local::perform_filter,
        op::{send_row, ShouldAbort},
        tantivy::{aggregate::get_fast_fields_impl, columnstore::collect_column_for_doc_batch},
        unpivot::unpivot_batch,
    },
    optimizer::{
        ast::{
            CursorField, CursorFilter, RealtimeWALSearchQuery, TantivyProjectedField,
            TantivySampler,
        },
        tantivy::make_field_name,
    },
    planner::{ast::TantivySearchQuery, JSON_PATH_SEGMENT_SEP},
};

use crate::interpreter::{
    context::InterpreterContext,
    error::{InterpreterError, Result},
    op::{Operator, StreamValue},
};

lazy_static! {
    pub static ref PAGINATION_KEY_FLAT: String =
        make_field_name(&[PathPiece::Key(PAGINATION_KEY_FIELD.to_string())]);
    pub static ref XACT_ID_FLAT: String =
        make_field_name(&[PathPiece::Key(XACT_ID_FIELD.to_string())]);
}

#[async_trait::async_trait]
impl Operator for TantivySearchQuery {
    fn name(&self) -> &'static str {
        "Index search"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let span = tracing::Span::current();

        let index_wal_reader = trace_if_async(
            log::Level::Info,
            &tracer,
            "Open index wal reader",
            |child| {
                storage::index_wal_reader::IndexWalReader::new(
                    IndexWalReaderInput {
                        config_with_store: &ctx.config,
                        full_schema: ctx.schema.clone(),
                        object_ids: &self.object_ids,
                        filters: &self.segment_filters,
                        sort: &self.segment_sort,
                        partition_all: false,
                        sampling: self.sample.as_ref().map(|s| {
                            if let Some(seed) = s.seed {
                                SamplingMode::Seed(seed)
                            } else {
                                SamplingMode::NoSeed
                            }
                        }),
                    },
                    IndexWalReaderOptionalInput {
                        tantivy_executor: Some(ctx.executor.clone()),
                        tracer: child,
                        ..Default::default()
                    },
                    ctx.opts.index_wal_reader_opts.clone(),
                )
            },
        )
        .await?;

        let is_columnar = self.columnar_projection;
        let search_query = index_wal_reader.wrap_exclude_query(self.search);

        let schema = make_tantivy_schema(&index_wal_reader.schema())
            .context("Failed to create tantivy schema from index wal reader schema")?
            .schema;
        let handle = ctx.handle.clone();

        spawn_blocking_with_async_timeout(
            &handle,
            move || {
                {
                    let _tracer_guard = tracer.as_ref().map(|tracer| tracer.enter());
                    let _guard = span.enter();

                    let effective_limit = match &self.limit {
                        Some(limit) => *limit as i64,
                        None => i64::MAX,
                    };

                    let mut remaining_docs = effective_limit;
                    let mut last_cursor_value = EMPTY_CURSOR_VALUE;

                    let mut realtime_docs = trace_if(
                        log::Level::Info,
                        &tracer,
                        "Process real-time docs",
                        |child| {
                            let realtime_docs = index_wal_reader.in_memory_docs();
                            child.increment_counter("num_input", realtime_docs.len() as u64);

                            let realtime_rows = VecDeque::from(filter_project_in_memory_docs(
                                child.clone(),
                                ctx.clone(),
                                index_wal_reader.in_memory_docs(),
                                &self.realtime_search,
                                self.cursor.clone(),
                            )?);

                            Ok::<_, InterpreterError>(realtime_rows)
                        },
                    )?;

                    let key_fn = if self.realtime_search.sort.len() > 0 {
                        Some(make_key_fn(ctx.clone(), &self.realtime_search.sort))
                    } else {
                        None
                    };

                    let mut tantivy_readers = vec![];
                    let num_segment_batches = index_wal_reader.num_batches();
                    for segment_batch_idx in 0..num_segment_batches {
                        if remaining_docs <= 0 {
                            break;
                        }
                        ctx.check_cancelled()?;

                        let (directory, search_query) = index_wal_reader
                            .directory_with_sort_filters(
                                segment_batch_idx,
                                search_query.box_clone(),
                                self.sort.as_ref(),
                            );
                        let mut tantivy_index = tantivy::Index::open(directory.box_clone())?;
                        tantivy_index.set_shared_multithread_executor(ctx.executor.clone())?;

                        directory.load_segment_footer_blocking(tracer.clone())?;

                        let reader = trace_if(log::Level::Info, &tracer, "Open reader", |child| {
                            child.increment_counter(
                                "num_chunks",
                                tantivy_index.searchable_segment_metas()?.len() as u64,
                            );
                            let reader_builder = tantivy_index.reader_builder();
                            reader_builder.try_into()
                        })?;
                        let searcher = reader.searcher();

                        tantivy_readers.push(reader);

                        ctx.set_index_state(InterpreterContextState {
                            index_wal_reader: index_wal_reader.clone(),
                            tantivy_schema: tantivy_index.schema().clone(),
                            tantivy_index: tantivy_index.clone(),
                            tantivy_readers: tantivy_readers.clone(),
                        });

                        for batch_idx in 0.. {
                            let batch_size = std::cmp::min(
                                effective_limit as usize,
                                self.batch_size.unwrap_or(ctx.opts.batch_size()),
                            );

                            let tracer = tracer
                                .as_ref()
                                .map(|tracer| tracer.new_child("Search batch"));
                            let _guard = tracer.as_ref().map(|tracer| tracer.enter());

                            let mut collector = tantivy::collector::TopDocs::with_limit(batch_size);
                            if batch_idx > 0 {
                                collector = collector.and_offset(batch_size * batch_idx);
                            }

                            let search_args = FinishSearchArgs {
                                tracer: tracer.clone(),
                                executor: &ctx.executor,
                                searcher: &searcher,
                                query: &search_query,
                                projected_fields: &self.projected_fields,
                                schema: &schema,
                                is_columnar,
                                cursor_filter: self.cursor.clone(),
                                sample: self.sample.as_ref(),
                            };

                            let final_rows = match &self.sort {
                                Some((ref dir, ref field, ref value_type)) => match value_type {
                                    TantivyFastValueType::Bool => {
                                        let sorted_collector = collector
                                            .order_by_fast_field::<bool>(field, dir.clone());
                                        finish_tantivy_search(search_args, sorted_collector)
                                    }
                                    TantivyFastValueType::U64 => {
                                        let sorted_collector = collector
                                            .order_by_fast_field::<u64>(field, dir.clone());
                                        finish_tantivy_search(search_args, sorted_collector)
                                    }
                                    TantivyFastValueType::I64 => {
                                        let sorted_collector = collector
                                            .order_by_fast_field::<i64>(field, dir.clone());
                                        finish_tantivy_search(search_args, sorted_collector)
                                    }
                                    TantivyFastValueType::F64 => {
                                        let sorted_collector = collector
                                            .order_by_fast_field::<f64>(field, dir.clone());
                                        finish_tantivy_search(search_args, sorted_collector)
                                    }
                                    TantivyFastValueType::DateTime => {
                                        let sorted_collector = collector
                                            .order_by_fast_field::<tantivy::DateTime>(
                                                field,
                                                dir.clone(),
                                            );
                                        finish_tantivy_search(search_args, sorted_collector)
                                    }
                                },
                                None => finish_tantivy_search(search_args, collector),
                            }?;

                            let mut final_rows = VecDeque::from(final_rows);
                            tracer.increment_counter("num_output", final_rows.len() as u64);

                            let num_rows = final_rows.len();
                            let num_realtime_docs = realtime_docs.len();

                            match Handle::current().block_on(async {
                                while final_rows.len() > 0
                                    || (
                                        // If there are no indexed rows left, then we should pretty much ALWAYS break,
                                        // because we might have more indexed rows. The only case is when we've exhausted
                                        // all batches and there are still real-time rows left.
                                        segment_batch_idx == num_segment_batches - 1
                                            && num_rows == 0
                                            && realtime_docs.len() > 0
                                    )
                                {
                                    let (mut row, cursor_value) =
                                        pop_min_row(&mut final_rows, &mut realtime_docs, &key_fn);

                                    if remaining_docs <= 0
                                        && Some(last_cursor_value) != cursor_value
                                    {
                                        return ShouldAbort::Abort;
                                    }

                                    match (&self.cursor, cursor_value) {
                                        (
                                            Some(CursorFilter {
                                                dir, collect: true, ..
                                            }),
                                            Some(cursor_value),
                                        ) => {
                                            let cursor = Cursor::new(cursor_value);
                                            ctx.update_cursor(&cursor, *dir);
                                        }
                                        _ => {}
                                    }

                                    if self.include_reader_batch {
                                        row.as_object_mut().expect("row is not an object").insert(
                                            READER_BATCH_FIELD.to_string(),
                                            Value::Number(segment_batch_idx.into()),
                                        );
                                    }

                                    match send_row(&tx, row).await {
                                        ShouldAbort::Continue => {}
                                        ShouldAbort::Abort => {
                                            return ShouldAbort::Abort;
                                        }
                                    }

                                    // If there are duplicate values with the same cursor, we need to keep going until we exhaust all ties.
                                    last_cursor_value = cursor_value.unwrap_or(EMPTY_CURSOR_VALUE);
                                    remaining_docs = remaining_docs.saturating_sub(1);
                                }
                                ShouldAbort::Continue
                            }) {
                                ShouldAbort::Continue => {}
                                ShouldAbort::Abort => {
                                    // TODO: The better thing to do here is to check a cancelled signal to make sure
                                    // the query actually ended, rather than some error case.
                                    log::debug!(
                                    "Aborting tantivy search since the parent stream was cancelled"
                                );
                                    return Ok(());
                                }
                            }

                            if num_rows < batch_size
                                && (segment_batch_idx < num_segment_batches - 1
                                    || num_realtime_docs == 0)
                            {
                                break;
                            }
                        }
                    }
                    Ok(())
                }
            },
            Default::default(),
            || "execute tantivy search".into(),
        )
        .await
        .context("Failed to join")??
    }
}

struct FinishSearchArgs<'a> {
    pub tracer: Option<Arc<TracedNode>>,
    pub executor: &'a tantivy::Executor,
    pub searcher: &'a tantivy::Searcher,
    pub query: &'a dyn tantivy::query::Query,
    pub projected_fields: &'a Vec<TantivyProjectedField>,
    pub schema: &'a tantivy::schema::Schema,
    pub is_columnar: bool,
    pub cursor_filter: Option<CursorFilter>,
    pub sample: Option<&'a TantivySampler>,
}

fn finish_tantivy_search<'a, T, C>(
    args: FinishSearchArgs<'a>,
    collector: C,
) -> Result<Vec<(Value, Option<CursorValue>)>>
where
    T: Send + 'static,
    C: Collector<Fruit = Vec<(T, tantivy::DocAddress)>>,
{
    let FinishSearchArgs {
        tracer,
        executor,
        searcher,
        query,
        projected_fields,
        schema,
        is_columnar,
        cursor_filter,
        sample,
    } = args;

    let stats_provider = StatisticsProvider(searcher, executor);

    let enabled_scoring = if collector.requires_scoring() {
        EnableScoring::enabled_from_statistics_provider(&stats_provider, searcher)
    } else {
        EnableScoring::disabled_from_searcher(searcher)
    };

    let docs = trace_if(log::Level::Info, &tracer, "Search", |_child| match sample {
        Some(sampler) => searcher.search_with_executor(
            query,
            &SamplingCollector {
                inner: collector,
                sample: sampler.clone(),
            },
            executor,
            enabled_scoring,
        ),

        None => searcher.search_with_executor(query, &collector, executor, enabled_scoring),
    })?;

    if docs.is_empty() {
        return Ok(Vec::new());
    }

    let mut cursor_values = Vec::new();
    if let Some(CursorFilter {
        field,
        collect: true,
        ..
    }) = cursor_filter
    {
        trace_if(log::Level::Info, &tracer, "Collect cursor", |_child| {
            cursor_values =
                collect_cursor_values(docs.iter().map(|(_score, doc)| *doc), searcher, field)?;

            Ok::<_, InterpreterError>(())
        })?;
    } else {
        cursor_values = vec![None; docs.len()];
    };

    let rows = if is_columnar {
        trace_if(
            log::Level::Info,
            &tracer,
            "Retrieve columnar documents",
            |child| {
                extract_docs_into_rows_columnar(
                    child,
                    executor,
                    projected_fields,
                    docs.into_iter().map(|(_score, doc)| doc),
                    &searcher,
                )
            },
        )?
    } else {
        trace_if(
            log::Level::Info,
            &tracer,
            "Retrieve full documents",
            |child| {
                extract_docs_into_rows(
                    child,
                    executor,
                    projected_fields,
                    &schema,
                    docs.into_iter().map(|(_score, doc)| doc),
                    &searcher,
                )
            },
        )?
    };

    Ok(rows.into_iter().zip(cursor_values).collect())
}

#[derive(Debug, Clone)]
pub struct IsPaginationKeyField(pub bool);

pub fn extract_docs_into_rows<I: IntoIterator<Item = tantivy::DocAddress>>(
    tracer: Option<Arc<TracedNode>>,
    executor: &tantivy::Executor,
    projected_fields: &Vec<TantivyProjectedField>,
    schema: &tantivy::schema::Schema,
    docs: I,
    searcher: &tantivy::Searcher,
) -> Result<Vec<Value>> {
    let fields = projected_fields
        .iter()
        .map(
            |TantivyProjectedField {
                 top_level_field, ..
             }| schema.get_field(&top_level_field),
        )
        .collect::<Result<Vec<_>, tantivy::TantivyError>>()?;

    let field_entries: Vec<_> = fields.iter().map(|f| schema.get_field_entry(*f)).collect();
    let is_pagination_key = field_entries
        .iter()
        .map(|f| IsPaginationKeyField(f.name() == "_pagination_key"))
        .collect::<Vec<_>>();

    let span = tracing::Span::current();
    let rows = executor.map(
        |doc_address| {
            let _guard = span.enter();

            let doc: tantivy::TantivyDocument =
                trace_if(log::Level::Debug, &tracer, "Retrieve document", |_child| {
                    searcher.doc(doc_address.clone())
                })?;

            let mut row: serde_json::Map<String, Value> = serde_json::Map::new();
            for (
                (
                    (
                        TantivyProjectedField {
                            alias,
                            repeated: is_array,
                            json_path,
                            ..
                        },
                        storage_field,
                    ),
                    field_entry,
                ),
                IsPaginationKeyField(is_pagination_key),
            ) in projected_fields
                .iter()
                .zip(fields.iter())
                .zip(field_entries.iter())
                .zip(is_pagination_key.iter())
            {
                let mut values = doc
                    .get_all(*storage_field)
                    .map(|orig_v| {
                        let alt_v = match is_pagination_key {
                            true => Some(pagination_key_from_raw_tantivy(orig_v).map_err(|e| {
                                tantivy::TantivyError::InternalError(format!("{e}"))
                            })?),
                            false => None,
                        };
                        let v = alt_v.as_ref().unwrap_or_else(|| orig_v);
                        match convert_tantivy_value(
                            &v,
                            &json_path,
                            matches!(
                                field_entry.field_type(),
                                tantivy::schema::FieldType::JsonObject(_)
                            ),
                        ) {
                            Ok(v) => Ok(v),
                            Err(e) => Err(tantivy::TantivyError::InternalError(format!("{e}"))),
                        }
                    })
                    .collect::<Result<Vec<_>, tantivy::TantivyError>>()?;

                let value = if values.len() == 0 {
                    Value::Null
                } else if values.len() == 1 && !is_array {
                    values.remove(0)
                } else {
                    Value::Array(values)
                };

                row.insert(alias.clone(), value);
            }
            Ok(Value::Object(row))
        },
        docs.into_iter(),
    )?;

    Ok(rows)
}

pub fn collect_cursor_values(
    docs: impl IntoIterator<Item = tantivy::DocAddress>,
    searcher: &tantivy::Searcher,
    cursor_field: CursorField,
) -> Result<Vec<Option<CursorValue>>> {
    let mut segment_to_docs = HashMap::new();
    for doc in docs {
        segment_to_docs
            .entry(doc.segment_ord)
            .or_insert_with(Vec::new)
            .push(doc.doc_id);
    }

    let mut cursor_values = Vec::new();
    for (segment_ord, docs) in segment_to_docs {
        let segment_reader = searcher.segment_reader(segment_ord);
        let cursor_column = segment_reader
            .fast_fields()
            .u64(cursor_field.get_field_name())?;
        for doc in docs {
            let cursor_value = cursor_column.first(doc);
            cursor_values.push(cursor_value);
        }
    }

    Ok(cursor_values)
}

pub fn convert_tantivy_value(
    v: &tantivy::schema::OwnedValue,
    path: &[PathPiece],
    has_root: bool,
) -> Result<Value> {
    Ok(match (v, path, has_root) {
        (tantivy::schema::OwnedValue::Object(o), _, true) => match o.get(JSON_ROOT_FIELD) {
            Some(v) => convert_tantivy_value(v, path, false)?,
            None => {
                return Err(InterpreterError::InternalError(format!(
                    "expected json object '{JSON_ROOT_FIELD}' at the top-level of the field"
                )));
            }
        },
        (_, _, true) => {
            return Err(InterpreterError::InternalError(
                "expected json object at the top-level of the field".to_string(),
            ))
        }

        (tantivy::schema::OwnedValue::Object(o), [first, rest @ ..], false) => match first {
            PathPiece::Key(key) => match o.get(key) {
                Some(v) => convert_tantivy_value(v, rest, false)?,
                None => Value::Null,
            },
            PathPiece::Index(_) => Value::Null, // Array index not valid for objects
        },
        (tantivy::schema::OwnedValue::Array(arr), [first, rest @ ..], false) => match first {
            PathPiece::Index(idx) => {
                let idx = *idx;
                let len = arr.len() as i64;

                if idx > len || idx < -len {
                    return Ok(Value::Null);
                }

                let wrapped_idx = if idx >= 0 {
                    idx as usize
                } else {
                    (len + idx) as usize
                };
                match arr.get(wrapped_idx) {
                    Some(v) => convert_tantivy_value(v, rest, false)?,
                    None => Value::Null,
                }
            }
            PathPiece::Key(_) => Value::Null,
        },
        (tantivy::schema::OwnedValue::Array(arr), [], _) => Value::Array(
            arr.iter()
                .map(|v| convert_tantivy_value(v, path, false))
                .collect::<Result<Vec<_>>>()?,
        ),
        (_, [_first, _rest @ ..], false) => {
            log::debug!(
                "Path {:?} not supported for non-object/array {:?}. Returning NULL.",
                path,
                v
            );
            Value::Null
        }
        (tantivy::schema::OwnedValue::Null, [], _) => Value::Null,
        (tantivy::schema::OwnedValue::Str(s), [], _) => Value::String(s.clone()),
        (tantivy::schema::OwnedValue::PreTokStr(s), [], _) => Value::String(s.text.to_string()),
        (tantivy::schema::OwnedValue::I64(i), [], _) => Value::Number((*i).into()),
        (tantivy::schema::OwnedValue::U64(u), [], _) => Value::Number((*u).into()),
        (tantivy::schema::OwnedValue::F64(f), [], _) => Value::Number(
            serde_json::Number::from_f64(*f)
                .map_or_else(|| Err(anyhow!("Invalid f64 value")), |n| Ok(n))?,
        ),
        (tantivy::schema::OwnedValue::Bool(b), [], _) => Value::Bool(*b),
        (tantivy::schema::OwnedValue::Date(d), [], _) => convert_tantivy_date(d)?,
        (tantivy::schema::OwnedValue::Facet(f), [], _) => Value::String(f.to_string()),
        (tantivy::schema::OwnedValue::Bytes(b), [], _) => Value::String(BASE64_STANDARD.encode(b)),
        (tantivy::schema::OwnedValue::IpAddr(ip), [], _) => Value::String(ip.to_string()),
        (tantivy::schema::OwnedValue::Object(obj), [], _) => Value::Object(
            obj.iter()
                .map(|(k, v)| Ok((k.clone(), convert_tantivy_value(v, path, false)?)))
                .collect::<Result<serde_json::Map<_, _>>>()?,
        ),
    })
}

fn convert_tantivy_date(d: &tantivy::DateTime) -> Result<Value> {
    let nanos = d.into_timestamp_nanos();
    let datetime = time::OffsetDateTime::from_unix_timestamp_nanos(nanos as i128)
        .map_err(|e| InterpreterError::InternalError(format!("Invalid timestamp: {}", e)))?;

    let formatted = datetime
        .format(&time::format_description::well_known::Rfc3339)
        .map_err(|e| {
            InterpreterError::InternalError(format!("Failed to format timestamp: {}", e))
        })?;

    Ok(Value::String(formatted))
}

// This uses the columnar reader to project values, which is much faster than reading the full document.
// It's important for the following use cases:
// * When doing span/trace expansion, we can just project the `id` and `root_span_id` fields, rather than retrieving
//   the full document.
// * In the "preview" optimization, we do `is_root ? <full field> : null` for a lot of fields. Each of the `is_root` fields
//   happens to be a fast field (e.g. `id`, `scores`, `span_attributes`, etc), so we can avoid loading the full document for
//   subspans (and only for root spans).
pub fn extract_docs_into_rows_columnar<I: IntoIterator<Item = tantivy::DocAddress>>(
    tracer: Option<Arc<TracedNode>>,
    executor: &tantivy::Executor,
    projected_fields: &Vec<TantivyProjectedField>,
    docs: I,
    searcher: &tantivy::Searcher,
) -> Result<Vec<Value>> {
    let mut segment_to_docs: HashMap<u32, Vec<(DocAddress, usize)>> = HashMap::new();
    let mut num_docs = 0;
    for doc in docs {
        segment_to_docs
            .entry(doc.segment_ord)
            .or_insert_with(Vec::new)
            .push((doc, num_docs));
        num_docs += 1;
    }

    let mut top_level_fields = HashMap::new();
    let mut json_fields = HashMap::new();
    for f in projected_fields {
        match f.field_type {
            TantivyType::Json(_) => {
                let mut full_path = f.top_level_field.clone();
                full_path.push(JSON_PATH_SEGMENT_SEP as char);
                full_path.push_str(JSON_ROOT_FIELD);
                for p in f.json_path.iter() {
                    match p {
                        PathPiece::Index(_) => break,
                        PathPiece::Key(key) => {
                            full_path.push(JSON_PATH_SEGMENT_SEP as char);
                            full_path.push_str(&key);
                        }
                    }
                }
                json_fields.insert(full_path, f);
            }
            _ => {
                top_level_fields.insert(f.top_level_field.clone(), f);
            }
        };
    }

    let rows = std::sync::Mutex::new(vec![Value::Object(serde_json::Map::new()); num_docs]);
    let span = tracing::Span::current();
    executor
        .map(
            |(segment_ord, docs)| {
                let _guard = span.enter();

                let segment_reader = searcher.segment_reader(segment_ord);
                let path_and_fields = open_columnstore_field_readers(
                    &top_level_fields,
                    &json_fields,
                    segment_reader,
                    tracer.clone(),
                )?;

                trace_if(log::Level::Info, &tracer, "Retrieve documents", |_child| {
                    executor
                        .map(
                            |(field, path, col, is_pagination_key)| {
                                let docs_and_indices = docs
                                    .iter()
                                    .map(|(doc, idx)| (doc.doc_id, *idx))
                                    .collect::<Vec<_>>();
                                let values = read_columnstore_values(
                                    &docs_and_indices,
                                    &col,
                                    &is_pagination_key,
                                    field.repeated,
                                )?;

                                Ok::<_, tantivy::TantivyError>((path.clone(), values))
                            },
                            path_and_fields.into_iter(),
                        )?
                        .into_iter()
                        .try_for_each(|(path, values)| {
                            let mut rows = rows.lock().unwrap();
                            for (idx, value) in values {
                                set_value_at_path(&mut rows[idx], &path, value).map_err(|e| {
                                    tantivy::TantivyError::InternalError(e.to_string())
                                })?;
                            }
                            Ok::<_, tantivy::TantivyError>(())
                        })?;
                    Ok::<_, tantivy::TantivyError>(())
                })?;

                Ok(())
            },
            segment_to_docs.into_iter(),
        )?
        .len();

    let rows = std::mem::take(&mut *rows.lock().unwrap());
    Ok(rows)
}

pub fn open_columnstore_field_readers<'a>(
    top_level_fields: &HashMap<String, &'a TantivyProjectedField>,
    json_fields: &HashMap<String, &'a TantivyProjectedField>,
    segment_reader: &tantivy::SegmentReader,
    tracer: Option<Arc<TracedNode>>,
) -> Result<
    Vec<(
        &'a TantivyProjectedField,
        Vec<String>,
        tantivy::columnar::DynamicColumn,
        IsPaginationKeyField,
    )>,
    tantivy::TantivyError,
> {
    let fast_fields = segment_reader.fast_fields();
    let fast_fields_impl = get_fast_fields_impl(fast_fields);

    let mut path_and_fields = Vec::new();

    trace_if(
        log::Level::Info,
        &tracer,
        "Open columnstore readers",
        |_child| {
            for (name, field) in top_level_fields {
                let matching_cols = fast_fields_impl.columnar.read_columns(name)?;
                for col in matching_cols {
                    path_and_fields.push((
                        *field,
                        vec![field.alias.clone()],
                        col.open()?,
                        IsPaginationKeyField(field.top_level_field == "_pagination_key"),
                    ));
                }
            }
            for (path, field) in json_fields {
                let matching_cols = get_columns_matching_prefix(&fast_fields_impl.columnar, path)?;
                for (name, col) in matching_cols {
                    let remaining_path = name.strip_prefix(path).ok_or_else(|| {
                        tantivy::TantivyError::InternalError(format!(
                            "Expected path {} to start with {}",
                            name, path
                        ))
                    })?;

                    path_and_fields.push((
                        *field,
                        vec![field.alias.clone()]
                            .into_iter()
                            .chain(
                                remaining_path
                                    .split(JSON_PATH_SEGMENT_SEP as char)
                                    .skip(1)
                                    .map(|s| s.to_string()),
                            )
                            .collect(),
                        col.open()?,
                        IsPaginationKeyField(false),
                    ));
                }
            }

            Ok::<_, tantivy::TantivyError>(())
        },
    )?;

    Ok(path_and_fields)
}

pub fn read_columnstore_values(
    docs: &[(tantivy::DocId, usize)],
    col: &tantivy::columnar::DynamicColumn,
    is_pagination_key: &IsPaginationKeyField,
    is_repeated: bool,
) -> Result<Vec<(usize, Value)>, tantivy::TantivyError> {
    let IsPaginationKeyField(is_pagination_key) = is_pagination_key;

    let doc_values = docs.iter().map(|(doc, _)| *doc).collect::<Vec<_>>();
    let tantivy_values = collect_column_for_doc_batch(&col, &doc_values)?;

    let mut values: Vec<(usize, Value)> = Vec::new();

    for ((_, idx), value) in docs.iter().zip(tantivy_values.into_iter()) {
        let value = if let Some(value) = value {
            value
        } else {
            continue;
        };

        let value = if *is_pagination_key {
            pagination_key_from_raw_tantivy(&value)
                .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?
        } else {
            value
        };

        let value = convert_tantivy_value(&value, &[], false)
            .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
        let value = match (is_repeated, value) {
            (true, Value::Array(arr)) => Value::Array(arr),
            (true, v) => Value::Array(vec![v]),
            (false, v) => v,
        };
        values.push((*idx, value));
    }

    Ok(values)
}

// Re-implemented this so we can parallelize statistics collection
pub struct StatisticsProvider<'s, 'e>(pub &'s tantivy::Searcher, pub &'e tantivy::Executor);

use tantivy::schema::Field;

use super::{
    columnstore::get_columns_matching_prefix, expand::READER_BATCH_FIELD, sample::SamplingCollector,
};

impl<'s, 'e> tantivy::query::Bm25StatisticsProvider for StatisticsProvider<'s, 'e> {
    fn total_num_tokens(&self, field: Field) -> tantivy::Result<u64> {
        Ok(self
            .1
            .map(
                |(_segment_ord, segment_reader)| {
                    let inverted_index = segment_reader.inverted_index(field)?;
                    Ok(inverted_index.total_num_tokens())
                },
                self.0.segment_readers().iter().enumerate(),
            )?
            .into_iter()
            .sum())
    }

    fn total_num_docs(&self) -> tantivy::Result<u64> {
        Ok(self
            .1
            .map(
                |(_segment_ord, segment_reader)| Ok(u64::from(segment_reader.max_doc())),
                self.0.segment_readers().iter().enumerate(),
            )?
            .into_iter()
            .sum())
    }

    fn doc_freq(&self, term: &tantivy::Term) -> tantivy::Result<u64> {
        self.0.doc_freq(term)
    }
}

pub fn filter_project_in_memory_docs<'a>(
    child: Option<Arc<TracedNode>>,
    ctx: Arc<InterpreterContext>,
    docs: &'a [Value],
    realtime_search: &RealtimeWALSearchQuery,
    cursor_filter: Option<CursorFilter>,
) -> Result<Vec<(Value, Option<CursorValue>)>> {
    let rows = docs.iter().map(|d| Cow::Borrowed(d)).collect::<Vec<_>>();
    let filtered_rows = trace_if(log::Level::Info, &child, "Filter", |_child| {
        perform_filter(&ctx.expr_ctx, rows, &realtime_search.filter)
    })?;

    let filtered_rows = match &cursor_filter {
        Some(CursorFilter {
            value: Some(value),
            dir,
            field,
            ..
        }) => filtered_rows
            .into_iter()
            .filter(|row| {
                let row_cursor_value = extract_cursor_value_from_row(row, *field, false).unwrap();
                match dir {
                    CursorDirection::Max => row_cursor_value > value.cursor_value,
                    CursorDirection::Min => row_cursor_value < value.cursor_value,
                }
            })
            .collect(),
        _ => filtered_rows,
    };

    let unpivoted_rows = if realtime_search.unpivot.len() > 0 {
        trace_if(log::Level::Info, &child, "Unpivot", |_child| {
            unpivot_batch(filtered_rows, &realtime_search.unpivot)
        })?
    } else {
        filtered_rows
    };

    let cursor_values = if let Some(CursorFilter {
        field,
        collect: true,
        ..
    }) = &cursor_filter
    {
        if realtime_search.unpivot.len() > 0 {
            return Err(InterpreterError::InternalError(
                "Cannot collect cursor when unpivoting".to_string(),
            ));
        }
        unpivoted_rows
            .iter()
            .map(|row| {
                let cursor_value: CursorValue = row
                    .get(field.get_field_name())
                    .and_then(|v| match &field {
                        CursorField::PaginationKey => v
                            .as_str()
                            .and_then(|s| s.parse::<PaginationKey>().ok())
                            .map(|v| v.0),
                        CursorField::TransactionId => v.as_u64(),
                    })
                    .ok_or_else(|| {
                        InterpreterError::InternalError(format!(
                            "Missing cursor field: {}",
                            field.get_field_name()
                        ))
                    })?;
                Ok(Some(cursor_value))
            })
            .collect::<Result<Vec<_>>>()?
    } else {
        vec![None; unpivoted_rows.len()]
    };

    let mut projected_rows = trace_if(log::Level::Info, &child, "Project", |_child| {
        Ok::<_, InterpreterError>(
            project_batch(&ctx.expr_ctx, &realtime_search.projection, unpivoted_rows)?
                .into_iter()
                .zip(cursor_values.into_iter())
                .map(|(row, cursor_value)| (row, cursor_value))
                .collect::<Vec<_>>(),
        )
    })?;

    if realtime_search.sort.len() > 0 {
        let key_fn = make_key_fn(ctx, &realtime_search.sort);
        let wrapped_key_fn = |row: &(Value, Option<u64>)| key_fn(&row.0);
        projected_rows.sort_by_cached_key(wrapped_key_fn);
    }

    Ok(projected_rows)
}

// This effectively implements a merge-sort
#[inline]
fn pop_min_row(
    a: &mut VecDeque<(Value, Option<CursorValue>)>,
    b: &mut VecDeque<(Value, Option<CursorValue>)>,
    key_fn: &Option<impl Fn(&Value) -> Vec<SortValueDir>>,
) -> (Value, Option<CursorValue>) {
    assert!(a.len() > 0 || b.len() > 0);
    if a.len() == 0 {
        b.pop_front().unwrap()
    } else if b.len() == 0 {
        a.pop_front().unwrap()
    } else if let Some(key_fn) = key_fn {
        let a_key = key_fn(&a.front().unwrap().0);
        let b_key = key_fn(&b.front().unwrap().0);
        if a_key < b_key {
            a.pop_front().unwrap()
        } else {
            b.pop_front().unwrap()
        }
    } else {
        a.pop_front().unwrap()
    }
}

pub fn extract_cursor_value_from_row<'a>(
    row: &'a Cow<'a, Value>,
    field: CursorField,
    flatten: bool,
) -> Result<CursorValue> {
    let v = row
        .get(get_cursor_field_name(field, flatten))
        .ok_or_else(|| {
            InterpreterError::InternalError(format!(
                "Missing cursor field: {}",
                get_cursor_field_name(field, flatten)
            ))
        })?;

    let cursor_value = match &field {
        CursorField::PaginationKey => {
            v.as_str()
                .ok_or_else(|| {
                    InterpreterError::InternalError(format!(
                        "Pagination key is not a string: {}",
                        v
                    ))
                })?
                .parse::<PaginationKey>()
                .map_err(|e| {
                    InterpreterError::InternalError(format!(
                        "Pagination key is not a valid pagination key: {}",
                        e
                    ))
                })?
                .0
        }
        CursorField::TransactionId => CastInto::cast(v).map_err(|e| {
            InterpreterError::InternalError(format!("Transaction ID is not a u64: {}", e))
        })?,
    };

    Ok(cursor_value)
}

fn get_cursor_field_name(field: CursorField, flatten: bool) -> &'static str {
    if flatten {
        match field {
            CursorField::PaginationKey => &*PAGINATION_KEY_FLAT,
            CursorField::TransactionId => &*XACT_ID_FLAT,
        }
    } else {
        match field {
            CursorField::PaginationKey => &*PAGINATION_KEY_FIELD,
            CursorField::TransactionId => &*XACT_ID_FIELD,
        }
    }
}
