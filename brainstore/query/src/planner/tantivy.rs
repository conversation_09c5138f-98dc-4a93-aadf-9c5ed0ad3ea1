use std::{ops::Bound, sync::Arc};

use btql::{binder::ast::SortDirection, util::CursorDirection};
use storage::tantivy_index::{JSON_PATHS_FIELD, JSON_ROOT_FIELD};

use tantivy::{
    columnar::{HasAssociatedColumnType, MonotonicallyMappableToU128, MonotonicallyMappableToU64},
    schema::JSON_END_OF_PATH,
    DateTime,
};
use util::{
    anyhow::Context,
    schema::{TantivyField, TantivyType},
    Value,
};

use super::{
    ast::{
        GroupByStrategy, PlannedExpansionProjection, TantivyAggregateQuery,
        TantivyExpandTracesQuery, TantivyInferenceField, TantivySchemaInferenceQuery,
    },
    context::PlanContext,
    error::{PlannerError, Result},
    groupby::plan_aggregate_function,
    plan,
};
use crate::{
    interpreter::aggregator::ExprAggregator,
    optimizer::{
        ast::{
            CursorFilter, ExpansionProjection, OptimizedTantivyAggregatePlan,
            OptimizedTantivyExpandTracesPlan, OptimizedTantivySchemaInferencePlan,
            OptimizedTantivySearch, OptimizedTantivySearchPlan, TantivyProjectedField,
        },
        tantivy::schema_type_to_tantivy_type,
    },
    planner::ast::TantivySearchQuery,
};
use btql::typesystem::CastInto;

pub fn plan_tantivy_search_query(
    ctx: &PlanContext,
    query: &OptimizedTantivySearchPlan,
) -> Result<TantivySearchQuery> {
    let search = plan_tantivy_search(ctx, &query.search)?;

    let search = match &query.cursor {
        Some(CursorFilter {
            value: Some(value),
            field,
            dir,
            ..
        }) => Box::new(tantivy::query::BooleanQuery::new(vec![
            (tantivy::query::Occur::Must, search),
            (
                tantivy::query::Occur::Must,
                Box::new(tantivy::query::FastFieldRangeWeight::new(
                    field.get_field_name().to_string(),
                    match dir {
                        CursorDirection::Max => Bound::Excluded(value.cursor_value),
                        CursorDirection::Min => Bound::Unbounded,
                    },
                    match dir {
                        CursorDirection::Max => Bound::Unbounded,
                        CursorDirection::Min => Bound::Excluded(value.cursor_value),
                    },
                )),
            ),
        ])),
        _ => search,
    };

    Ok(TantivySearchQuery {
        object_ids: query.object_ids.clone(),
        search,
        realtime_search: query.realtime_search.clone(),
        projected_fields: query.projection.clone(),
        columnar_projection: is_columnar_projection(&query.projection),
        sort: match &query.sort {
            Some((dir, field)) => Some((
                match dir {
                    SortDirection::Asc => tantivy::Order::Asc,
                    SortDirection::Desc => tantivy::Order::Desc,
                },
                field.name.clone(),
                match field.field_type.fast_field_value_type() {
                    Some(t) => t,
                    None => {
                        return Err(PlannerError::InvalidOptimizedExpr {
                            msg: format!(
                                "Cannot sort on tantivy field of type: {:?}",
                                &field.field_type
                            ),
                        });
                    }
                },
            )),
            None => None,
        },
        limit: query.limit,
        batch_size: query.batch_size,
        cursor: query.cursor.clone(),
        include_reader_batch: query.include_reader_batch,
        sample: query.sample.clone(),
        segment_filters: query.segment_filters.clone(),
        segment_sort: query.segment_sort.clone(),
    })
}

pub fn plan_tantivy_schema_inference_query(
    ctx: &PlanContext,
    query: &OptimizedTantivySchemaInferencePlan,
) -> Result<TantivySchemaInferenceQuery> {
    let search = plan_tantivy_search(ctx, &query.search)?;

    let mut infer = Vec::new();
    for field in query.infer.iter() {
        if !field.json_path.is_empty() {
            return Err(PlannerError::InvalidOptimizedExpr {
                msg: "inference is only supported on top-level fields".to_string(),
            });
        }

        infer.push(TantivyInferenceField {
            field: field.top_level_field.clone(),
            alias: field.alias.clone(),
            field_type: field.field_type.clone(),
            is_columnar: field.field_type.fast(),
        });
    }

    Ok(TantivySchemaInferenceQuery {
        object_ids: query.object_ids.clone(),
        search,
        realtime_search: query.realtime_search.clone(),
        infer: Arc::new(infer),
        budget: query.budget,
        sort: match &query.sort {
            Some((dir, field)) => Some((
                match dir {
                    SortDirection::Asc => tantivy::Order::Asc,
                    SortDirection::Desc => tantivy::Order::Desc,
                },
                field.name.clone(),
                match field.field_type.fast_field_value_type() {
                    Some(t) => t,
                    None => {
                        return Err(PlannerError::InvalidOptimizedExpr {
                            msg: format!(
                                "Cannot sort on tantivy field of type: {:?}",
                                &field.field_type
                            ),
                        });
                    }
                },
            )),
            None => None,
        },
        limit: query.limit,
        batch_size: query.batch_size,
        cursor: query.cursor.clone(),
        segment_filters: query.segment_filters.clone(),
        segment_sort: query.segment_sort.clone(),
    })
}

fn plan_tantivy_search(
    ctx: &PlanContext,
    filter: &OptimizedTantivySearch,
) -> Result<Box<dyn tantivy::query::Query>> {
    use OptimizedTantivySearch::*;
    Ok(match filter {
        AllQuery => Box::new(tantivy::query::AllQuery) as Box<dyn tantivy::query::Query>,
        EmptyQuery => Box::new(tantivy::query::EmptyQuery) as Box<dyn tantivy::query::Query>,
        BooleanQuery(ops) => {
            let mut queries = Vec::new();
            for (op, query) in ops.iter() {
                let query = plan_tantivy_search(ctx, query)?;
                queries.push((op.into(), query));
            }
            Box::new(tantivy::query::BooleanQuery::new(queries)) as Box<dyn tantivy::query::Query>
        }
        ExistsQuery { field } => Box::new(tantivy::query::ExistsQuery::new_exists_query(
            field.name.clone(),
        )) as Box<dyn tantivy::query::Query>,
        RegexQuery {
            field,
            json_path,
            pattern,
            case_insensitive,
        } => Box::new(
            crate::interpreter::tantivy::query::RegexQuery::new(
                ctx.get_tantivy_field(&field.name)?,
                pattern,
                *case_insensitive,
                json_path,
                matches!(field.field_type, util::schema::TantivyType::Json(_)),
            )
            .map_err(|e| PlannerError::InvalidOptimizedExpr {
                msg: format!("RegexQuery creation failed: {}", e),
            })?,
        ),
        TermQuery(query) => match build_term_query(ctx, query, false) {
            Ok(term_query) => term_query,
            Err(PlannerError::Typesystem(..)) => Box::new(tantivy::query::EmptyQuery),
            Err(e) => {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: format!("invalid term query: {}", e),
                });
            }
        },
        JSONExistsQuery { field, json_path } => build_term_query(
            ctx,
            &crate::optimizer::ast::TermQuery {
                field: field.clone(),
                json_path: vec![JSON_PATHS_FIELD.to_string()],
                tantivy_type: tantivy::schema::Type::Str,
                value: Value::String(util::json::serialize_path(json_path)),
            },
            true,
        )?,
        RangeQuery {
            field,
            tantivy_type,
            json_path,
            lower,
            upper,
            columnar,
        } => {
            if *columnar {
                match tantivy_type {
                    tantivy::schema::Type::U64 => {
                        build_fast_field_range_query::<u64>(field, json_path, lower, upper)?
                    }
                    tantivy::schema::Type::I64 => {
                        build_fast_field_range_query::<i64>(field, json_path, lower, upper)?
                    }
                    tantivy::schema::Type::F64 => {
                        build_fast_field_range_query::<f64>(field, json_path, lower, upper)?
                    }
                    tantivy::schema::Type::Bool => {
                        build_fast_field_range_query::<bool>(field, json_path, lower, upper)?
                    }
                    tantivy::schema::Type::Date => {
                        build_fast_field_range_query::<DateTime>(field, json_path, lower, upper)?
                    }
                    _ => unreachable!(),
                }
            } else {
                let lower_term = match lower {
                    Bound::Unbounded => Bound::Unbounded,
                    Bound::Included(v) => Bound::Included(build_single_term(
                        ctx,
                        field,
                        json_path,
                        tantivy_type,
                        v,
                        false,
                    )?),
                    Bound::Excluded(v) => Bound::Excluded(build_single_term(
                        ctx,
                        field,
                        json_path,
                        tantivy_type,
                        v,
                        false,
                    )?),
                };
                let upper_term = match upper {
                    Bound::Unbounded => match field.field_type {
                        TantivyType::Json(_) => {
                            // If this is a JSON field, then we need to exclude the 'p' (paths) entries,
                            // because they match a lot of documents. 'p' occurs after 'o', so this only
                            // applies to > queries.
                            let storage_field = ctx.get_tantivy_field(&field.name)?;
                            let mut term: tantivy::Term =
                                tantivy::Term::from_field_text(storage_field.clone(), "");
                            term.append_path(JSON_PATHS_FIELD.as_bytes());
                            Bound::Excluded(term)
                        }
                        _ => Bound::Unbounded,
                    },
                    Bound::Included(v) => Bound::Included(build_single_term(
                        ctx,
                        field,
                        json_path,
                        tantivy_type,
                        v,
                        false,
                    )?),
                    Bound::Excluded(v) => Bound::Excluded(build_single_term(
                        ctx,
                        field,
                        json_path,
                        tantivy_type,
                        v,
                        false,
                    )?),
                };

                Box::new(tantivy::query::RangeQuery::new_term_bounds(
                    field.name.clone(),
                    tantivy_type_to_lib_type(&field.field_type),
                    &lower_term,
                    &upper_term,
                ))
            }
        }
    })
}

fn build_numeric_term<T: MonotonicallyMappableToU64>(term: &mut tantivy::Term, value: T) {
    // https://github.com/quickwit-oss/tantivy/blob/0.22.0/src/core/json_utils.rs#L456
    term.append_bytes(value.to_u64().to_be_bytes().as_slice());
}

fn build_single_term(
    ctx: &PlanContext,
    field: &TantivyField,
    json_path: &[String],
    tantivy_type: &tantivy::schema::Type,
    value: &Value,
    skip_root_path: bool,
) -> Result<tantivy::Term> {
    let mut terms = build_term(ctx, field, json_path, tantivy_type, value, skip_root_path)?;
    if terms.len() != 1 {
        return Err(PlannerError::InvalidOptimizedExpr {
            msg: "single term query should have resulted in a single term (this is an optimizer issue)".to_string(),
        });
    }
    Ok(terms.remove(0))
}

fn build_term(
    ctx: &PlanContext,
    field: &TantivyField,
    json_path: &[String],
    tantivy_type: &tantivy::schema::Type,
    value: &Value,
    skip_root_path: bool,
) -> Result<Vec<tantivy::Term>> {
    // This is a sort of generic way of initializing a term, which is:
    // * 4 bytes of field id
    // * 1 byte of field type
    // * optionally, the json path
    // * 1 byte of the value type
    // * the value
    let storage_field = ctx.get_tantivy_field(&field.name)?;
    let mut term: tantivy::Term = tantivy::Term::from_field_text(storage_field.clone(), "");
    let tantivy_field_type = schema_type_to_tantivy_type(&field.field_type);
    term.clear_with_type(tantivy_field_type);

    if matches!(tantivy_field_type, tantivy::schema::Type::Json) {
        // If it's a json field, it has a special root field name
        if !skip_root_path {
            term.append_path(JSON_ROOT_FIELD.as_bytes());
            term.add_json_path_separator();
        }

        for part in json_path.iter() {
            term.append_path(part.as_bytes());
            term.add_json_path_separator();
        }
        term.set_json_path_end();

        // Set the value type
        term.append_bytes(&[*tantivy_type as u8]);
    }

    match tantivy_type {
        tantivy::schema::Type::Str => {
            let literal_value: String = value.cast()?;
            let mut terms = Vec::new();
            match ctx.get_tokenizer(storage_field.clone())? {
                Some(mut tokenizer) => {
                    let mut token_stream = tokenizer.token_stream(&literal_value);
                    token_stream.process(&mut |token| {
                        let mut tok_term = term.clone();
                        tok_term.append_bytes(token.text.as_bytes());
                        terms.push(tok_term);
                    });
                }
                None => {
                    term.append_bytes(literal_value.as_bytes());
                    terms.push(term);
                }
            }

            if terms.len() == 0 {
                return Ok(vec![]);
            } else if terms.len() > 1 {
                return Ok(terms);
            } else {
                term = terms.remove(0);
            }
        }
        tantivy::schema::Type::U64 => {
            let numeric_value: u64 = value.cast()?;
            build_numeric_term(&mut term, numeric_value);
        }
        tantivy::schema::Type::I64 => {
            let numeric_value: i64 = value.cast()?;
            build_numeric_term(&mut term, numeric_value);
        }
        tantivy::schema::Type::F64 => {
            let numeric_value: f64 = value.cast()?;
            build_numeric_term(&mut term, numeric_value);
        }
        tantivy::schema::Type::Bool => {
            let numeric_value: bool = value.cast()?;
            build_numeric_term(&mut term, numeric_value);
        }
        tantivy::schema::Type::Date => {
            let numeric_value: DateTime = value.cast()?;
            build_numeric_term(&mut term, numeric_value);
        }
        tantivy::schema::Type::Facet | tantivy::schema::Type::Bytes => {
            let literal_value: String = value.cast()?;
            term.append_bytes(literal_value.as_bytes());
        }
        tantivy::schema::Type::IpAddr => {
            let ip_addr: std::string::String = value.cast()?;
            let ip_addr: std::net::Ipv6Addr = ip_addr.parse().context("invalid ip address")?;

            term.append_bytes(ip_addr.to_u128().to_be_bytes().as_ref());
        }
        tantivy::schema::Type::Json => {
            return Err(PlannerError::InvalidOptimizedExpr {
                msg: "json value should have been optimized away".to_string(),
            })
        }
    };

    Ok(vec![term])
}

pub fn build_term_query(
    ctx: &PlanContext,
    field: &crate::optimizer::ast::TermQuery,
    skip_root_path: bool,
) -> Result<Box<dyn tantivy::query::Query>> {
    let crate::optimizer::ast::TermQuery {
        field,
        json_path,
        tantivy_type,
        value,
    } = field;

    let mut terms = build_term(ctx, field, json_path, tantivy_type, value, skip_root_path)?;
    if terms.len() == 0 {
        return Ok(Box::new(tantivy::query::EmptyQuery));
    } else if terms.len() > 1 {
        return Ok(Box::new(tantivy::query::PhraseQuery::new(terms)));
    } else {
        return Ok(Box::new(tantivy::query::TermQuery::new(
            terms.remove(0),
            tantivy::schema::IndexRecordOption::Basic,
        )));
    }
}

pub fn make_json_path_name(root_field: &str, json_path: &[String], include_end: bool) -> String {
    let mut full_name = root_field.to_string();
    let mut first = true;
    for part in json_path.iter() {
        if first {
            full_name.push(JSON_PATH_SEGMENT_SEP as char);
            full_name.push_str(JSON_ROOT_FIELD);
            first = false;
        }
        full_name.push(JSON_PATH_SEGMENT_SEP as char);
        full_name.push_str(part);
    }
    if include_end && !json_path.is_empty() {
        full_name.push(JSON_END_OF_PATH as char);
    }
    full_name
}

pub fn plan_tantivy_aggregate_query(
    ctx: &PlanContext,
    query: &OptimizedTantivyAggregatePlan,
) -> Result<TantivyAggregateQuery> {
    let search = plan_tantivy_search(ctx, &query.search)?;

    let aggregates = query
        .aggregates
        .iter()
        .map(|(alias, func)| Ok((alias.clone(), plan_aggregate_function(func)?)))
        .collect::<Result<Vec<_>>>()?;

    let group_by = GroupByStrategy::from_dims_and_aggs(
        &query.dimensions,
        &query.pivot,
        &query.unpivot,
        &aggregates,
        &query.projection,
    );

    Ok(TantivyAggregateQuery {
        object_ids: query.object_ids.clone(),
        search,
        realtime_search: query.realtime_search.clone(),
        projection: query.projection.clone(),
        pivot: Arc::new(query.pivot.clone()),
        measures: query.measures.clone(),
        segment_filters: query.segment_filters.clone(),

        unpivot: query.unpivot.clone(),
        dimensions: Arc::new(query.dimensions.clone()),
        expr_aggs: aggregates
            .into_iter()
            .map(|(alias, (agg, expr))| (alias, ExprAggregator::new(agg, expr)))
            .collect(),

        group_by,
        expand_traces: query.expand_traces,
    })
}

pub fn plan_tantivy_expand_traces_query(
    ctx: &PlanContext,
    query: &OptimizedTantivyExpandTracesPlan,
) -> Result<TantivyExpandTracesQuery> {
    let from = plan(&ctx, &query.from)?;

    Ok(TantivyExpandTracesQuery {
        from,
        projection: match &query.projection {
            ExpansionProjection::Spans {
                is_root_projection,
                root_projection,
                span_projection,
                realtime_projection,
            } => PlannedExpansionProjection::Spans {
                is_root_projection: is_root_projection.clone(),
                root_projection: root_projection.clone(),
                root_is_columnar: is_columnar_projection(&root_projection),
                span_projection: span_projection.clone(),
                span_is_columnar: is_columnar_projection(&span_projection),
                realtime_projection: realtime_projection.clone(),
            },
            ExpansionProjection::Summary {
                root_projection,
                comparison_key,
                weighted_scores,
                custom_columns,
                post_aggregation_filter,
                preview_length,
            } => PlannedExpansionProjection::Summary {
                root_projection: root_projection.clone(),
                comparison_key: comparison_key.clone(),
                weighted_scores: weighted_scores.clone(),
                custom_columns: custom_columns.clone(),
                post_aggregation_filter: post_aggregation_filter.clone(),
                preview_length: preview_length.clone(),
            },
        },
        limit: query.limit,
        cursor: query.cursor.clone(),
        is_top_level_limiter: query.is_top_level_limiter,
    })
}

// Must match https://github.com/quickwit-oss/tantivy/blob/17d5869ad61ea9f1072ef40546ddcada3a14b067/src/schema/term.rs#L14
pub const JSON_PATH_SEGMENT_SEP: u8 = 1u8;

fn is_columnar_projection(projection: &[TantivyProjectedField]) -> bool {
    projection
        .iter()
        .all(|field| field.can_project_fast_field())
}

fn build_fast_field_range_query<T>(
    field: &TantivyField,
    json_path: &[String],
    lower: &Bound<Value>,
    upper: &Bound<Value>,
) -> Result<Box<dyn tantivy::query::Query>>
where
    T: HasAssociatedColumnType + MonotonicallyMappableToU64,
    Value: CastInto<T>,
{
    Ok(Box::new(tantivy::query::FastFieldRangeWeight::new(
        make_field_name(field, json_path),
        cast_bound::<T>(lower)?,
        cast_bound::<T>(upper)?,
    )))
}

fn make_field_name(field: &TantivyField, json_path: &[String]) -> String {
    if json_path.is_empty() {
        field.name.to_string()
    } else {
        util::json::serialize_path(
            [field.name.as_str(), JSON_ROOT_FIELD]
                .into_iter()
                .chain(json_path.iter().map(|s| s.as_str())),
        )
    }
}

fn cast_bound<T>(bound: &Bound<Value>) -> Result<Bound<T>>
where
    Value: CastInto<T>,
{
    Ok(match bound {
        Bound::Included(s) => Bound::Included(CastInto::<T>::cast(s)?),
        Bound::Excluded(s) => Bound::Excluded(CastInto::<T>::cast(s)?),
        Bound::Unbounded => Bound::Unbounded,
    })
}

fn tantivy_type_to_lib_type(tantivy_type: &TantivyType) -> tantivy::schema::Type {
    match tantivy_type {
        TantivyType::U64(_) => tantivy::schema::Type::U64,
        TantivyType::I64(_) => tantivy::schema::Type::I64,
        TantivyType::F64(_) => tantivy::schema::Type::F64,
        TantivyType::Bool(_) => tantivy::schema::Type::Bool,
        TantivyType::Date(_) => tantivy::schema::Type::Date,
        TantivyType::Bytes(_) => tantivy::schema::Type::Bytes,
        TantivyType::IpAddr(_) => tantivy::schema::Type::IpAddr,
        TantivyType::Facet(_) => tantivy::schema::Type::Facet,
        TantivyType::Str(_) => tantivy::schema::Type::Str,
        TantivyType::Json(_) => tantivy::schema::Type::Json,
    }
}
