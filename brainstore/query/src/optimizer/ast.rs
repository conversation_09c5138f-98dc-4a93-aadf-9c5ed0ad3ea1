use btql::{
    binder::ast::{<PERSON><PERSON>, Expr, Function, SampleMethod, SortDirection},
    util::{<PERSON>ursor, CursorDirection},
};
use serde::Serialize;
use std::ops::Bound;
use storage::segment_batches::{SegmentBatchingSortSpec, SegmentEliminationFilterSpec};
use util::{
    json::PathPiece,
    ptree::{MakePTree, TreeBuilder},
    schema::{TantivyField, TantivyType},
    system_types::FullObjectIdOwned,
};

use super::optimization::segment_elimination::{
    make_segment_batching_sort_spec, make_segment_elimination_filter_spec,
};

#[derive(Debug, Clone, MakePTree, Serialize)]
pub enum OptimizedPlan {
    Noop(NoopPlan),
    Filter(FilterPlan),
    GroupBy(GroupByPlan),
    Project(ProjectPlan),
    Unpivot(UnpivotPlan),
    TantivySearch(OptimizedTantivySearchPlan),
    TantivyAggregate(OptimizedTantivyAggregatePlan),
    TantivyExpandTraces(OptimizedTantivyExpandTracesPlan),
    TantivySchemaInference(OptimizedTantivySchemaInferencePlan),
}

#[derive(Debug, Clone, MakePTree, Serialize)]
pub struct NoopPlan {}

#[derive(Debug, Clone, MakePTree, Serialize)]
pub struct FilterPlan {
    pub from: Box<OptimizedPlan>,
    pub filter: Option<Box<Expr>>,
}

#[derive(Debug, Clone, MakePTree, Serialize)]
pub struct GroupByPlan {
    pub from: Box<OptimizedPlan>,
    pub dimensions: Vec<Alias>,
    pub pivot: Vec<Alias>,
    pub aggregates: Vec<(String, Function)>,
    pub measures: Vec<Alias>,
}

#[derive(Debug, Clone, MakePTree, Serialize)]
pub struct ProjectPlan {
    pub from: Box<OptimizedPlan>,
    pub sort: Vec<OptimizedSortItem>,
    pub projection: Option<Vec<Alias>>,
    pub limit: Option<usize>,
    pub cursor_field: Option<CursorField>,
    // TODO: We should be able to remove this once we rip the cursor out of the interpreter
    // ctx in favor of just passing cursor values up with the rows.
    pub is_top_level_limiter: bool,
}

#[derive(Debug, Clone, MakePTree, Serialize)]
pub struct UnpivotPlan {
    pub from: Box<OptimizedPlan>,
    pub unpivot: Vec<UnpivotProjectionExpr>,
}

#[derive(Debug, Clone, Serialize)]
pub struct UnpivotProjectionExpr {
    pub base_field: String,
    pub projected_field: UnpivotProjectedField,
}

#[derive(Debug, Clone, Serialize)]
pub enum UnpivotProjectedField {
    Array { item: String },
    Object { key: String, value: String },
}

#[derive(Debug, Clone, Serialize)]
pub struct TantivyProjectedField {
    pub alias: String,
    pub top_level_field: String,
    pub json_path: Vec<PathPiece>,
    pub repeated: bool,
    pub field_type: TantivyType,
    pub lossy_fast_field: bool,
    pub only_exists: bool,
}

impl TantivyProjectedField {
    pub fn can_project_fast_field(&self) -> bool {
        self.field_type.fast()
        // Either in the tantivy schema, or the logical schema, we must know this is a scalar value.
        && (match self.field_type {
            TantivyType::Json(_) => !self.lossy_fast_field,
            _ => true,
        })
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Serialize, MakePTree)]
pub enum CursorField {
    PaginationKey,
    TransactionId,
}

#[derive(Debug, Clone, PartialEq, Serialize, MakePTree)]
pub struct CursorFilter {
    pub field: CursorField,
    pub dir: CursorDirection,
    pub value: Option<Cursor>,
    pub collect: bool,
}

#[derive(Debug, Clone, PartialEq, Serialize, MakePTree)]
pub struct TantivySampler {
    pub method: SampleMethod,
    pub deduplicate_by: Option<TantivyField>,
    pub seed: Option<u64>,
}

#[derive(Debug, Clone, MakePTree, Serialize)]
#[ptree("Index search")]
pub struct OptimizedTantivySearchPlan {
    pub object_ids: Vec<FullObjectIdOwned>,
    pub search: Box<OptimizedTantivySearch>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub projection: Vec<TantivyProjectedField>,
    pub sort: Option<(SortDirection, TantivyField)>,
    pub limit: Option<usize>,
    pub batch_size: Option<usize>,
    pub cursor: Option<CursorFilter>,
    pub include_reader_batch: bool,
    pub sample: Option<TantivySampler>,
    pub segment_filters: Vec<SegmentEliminationFilterSpec>,
    pub segment_sort: Option<SegmentBatchingSortSpec>,
}

pub struct OptimizedTantivySearchPlanArgs {
    pub object_ids: Vec<FullObjectIdOwned>,
    pub search: Box<OptimizedTantivySearch>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub projection: Vec<TantivyProjectedField>,
    pub sort: Option<(SortDirection, TantivyField)>,
    pub limit: Option<usize>,
    pub batch_size: Option<usize>,
    pub cursor: Option<CursorFilter>,
    pub include_reader_batch: bool,
    pub sample: Option<TantivySampler>,
}

impl OptimizedTantivySearchPlan {
    pub fn new(args: OptimizedTantivySearchPlanArgs) -> Self {
        let segment_filters = make_segment_elimination_filter_spec(&args.search);
        let segment_sort = make_segment_batching_sort_spec(&args.sort);
        Self {
            object_ids: args.object_ids,
            search: args.search,
            realtime_search: args.realtime_search,
            projection: args.projection,
            sort: args.sort,
            limit: args.limit,
            batch_size: args.batch_size,
            cursor: args.cursor,
            include_reader_batch: args.include_reader_batch,
            sample: args.sample,
            segment_filters,
            segment_sort,
        }
    }
}

#[derive(Debug, Clone, MakePTree, Serialize)]
#[ptree("Schema inference")]
pub struct OptimizedTantivySchemaInferencePlan {
    pub object_ids: Vec<FullObjectIdOwned>,
    pub search: Box<OptimizedTantivySearch>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub infer: Vec<TantivyProjectedField>,
    pub budget: Option<u64>,
    pub sort: Option<(SortDirection, TantivyField)>,
    pub limit: Option<usize>,
    pub batch_size: Option<usize>,
    pub cursor: Option<Cursor>,
    pub segment_filters: Vec<SegmentEliminationFilterSpec>,
    pub segment_sort: Option<SegmentBatchingSortSpec>,
}

pub struct OptimizedTantivySchemaInferencePlanArgs {
    pub object_ids: Vec<FullObjectIdOwned>,
    pub search: Box<OptimizedTantivySearch>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub infer: Vec<TantivyProjectedField>,
    pub budget: Option<u64>,
    pub sort: Option<(SortDirection, TantivyField)>,
    pub limit: Option<usize>,
    pub batch_size: Option<usize>,
    pub cursor: Option<Cursor>,
}

impl OptimizedTantivySchemaInferencePlan {
    pub fn new(args: OptimizedTantivySchemaInferencePlanArgs) -> Self {
        let segment_filters = make_segment_elimination_filter_spec(&args.search);
        let segment_sort = make_segment_batching_sort_spec(&args.sort);
        Self {
            object_ids: args.object_ids,
            search: args.search,
            realtime_search: args.realtime_search,
            infer: args.infer,
            budget: args.budget,
            sort: args.sort,
            limit: args.limit,
            batch_size: args.batch_size,
            cursor: args.cursor,
            segment_filters,
            segment_sort,
        }
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct OptimizedSortItem {
    pub dir: SortDirection,
    pub expr: Box<Expr>,
}

#[derive(Debug, Clone, MakePTree, Serialize)]
#[ptree("Index aggregate")]
pub struct OptimizedTantivyAggregatePlan {
    pub object_ids: Vec<FullObjectIdOwned>,
    pub search: Box<OptimizedTantivySearch>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub projection: Vec<TantivyProjectedField>,
    pub unpivot: Vec<UnpivotProjectionExpr>,

    pub aggregates: Vec<(String, Function)>,

    // These are the logical expressions corresponding to each section. The measures have been
    // rewritten in terms of the aggregates.
    pub dimensions: Vec<Alias>,
    pub pivot: Vec<Alias>,
    pub measures: Vec<Alias>,

    pub segment_filters: Vec<SegmentEliminationFilterSpec>,
    pub expand_traces: bool,
}

pub struct OptimizedTantivyAggregatePlanArgs {
    pub object_ids: Vec<FullObjectIdOwned>,
    pub search: Box<OptimizedTantivySearch>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub projection: Vec<TantivyProjectedField>,
    pub unpivot: Vec<UnpivotProjectionExpr>,
    pub aggregates: Vec<(String, Function)>,
    pub dimensions: Vec<Alias>,
    pub pivot: Vec<Alias>,
    pub measures: Vec<Alias>,
    pub expand_traces: bool,
}

impl OptimizedTantivyAggregatePlan {
    pub fn new(args: OptimizedTantivyAggregatePlanArgs) -> Self {
        let segment_filters = make_segment_elimination_filter_spec(&args.search);
        Self {
            object_ids: args.object_ids,
            search: args.search,
            realtime_search: args.realtime_search,
            projection: args.projection,
            unpivot: args.unpivot,
            aggregates: args.aggregates,
            dimensions: args.dimensions,
            pivot: args.pivot,
            measures: args.measures,
            segment_filters,
            expand_traces: args.expand_traces,
        }
    }
}

#[derive(Debug, Clone, MakePTree, Serialize)]
#[ptree("Projection")]
pub enum ExpansionProjection {
    Spans {
        is_root_projection: Vec<TantivyProjectedField>,
        root_projection: Vec<TantivyProjectedField>,
        span_projection: Vec<TantivyProjectedField>,
        realtime_projection: Vec<Alias>,
    },
    Summary {
        root_projection: Vec<TantivyProjectedField>,
        comparison_key: Box<Expr>,
        weighted_scores: Vec<Alias>,
        custom_columns: Vec<Alias>,
        post_aggregation_filter: Option<Box<Expr>>,
        preview_length: Option<usize>,
    },
}

#[derive(Debug, Clone, MakePTree, Serialize)]
#[ptree("Expand traces")]
pub struct OptimizedTantivyExpandTracesPlan {
    pub from: Box<OptimizedPlan>,
    pub projection: ExpansionProjection,
    pub limit: Option<usize>,
    pub cursor: Option<CursorFilter>,
    // TODO: We should be able to remove this once we rip the cursor out of the interpreter
    // ctx in favor of just passing cursor values up with the rows.
    pub is_top_level_limiter: bool,
}

#[derive(Debug, Clone, Copy, Serialize, PartialEq, Eq)]
pub enum BooleanQueryOp {
    Must,
    MustNot,
    Should,
}

impl Into<tantivy::query::Occur> for &BooleanQueryOp {
    fn into(self) -> tantivy::query::Occur {
        match self {
            BooleanQueryOp::Must => tantivy::query::Occur::Must,
            BooleanQueryOp::MustNot => tantivy::query::Occur::MustNot,
            BooleanQueryOp::Should => tantivy::query::Occur::Should,
        }
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct TermQuery {
    pub field: TantivyField,
    pub json_path: Vec<String>,
    #[serde(skip)]
    pub tantivy_type: tantivy::schema::Type,
    pub value: util::serde_json::Value,
}

#[derive(Debug, Clone, Serialize)]
pub enum OptimizedTantivySearch {
    AllQuery,
    EmptyQuery,
    BooleanQuery(Vec<(BooleanQueryOp, OptimizedTantivySearch)>),
    ExistsQuery {
        field: TantivyField,
    },
    JSONExistsQuery {
        field: TantivyField,
        json_path: Vec<String>,
    },
    RegexQuery {
        field: TantivyField,
        json_path: Vec<String>,
        pattern: String,
        case_insensitive: bool,
    },
    TermQuery(TermQuery),
    RangeQuery {
        field: TantivyField,
        json_path: Vec<String>,
        #[serde(skip)]
        tantivy_type: tantivy::schema::Type,
        lower: Bound<util::serde_json::Value>,
        upper: Bound<util::serde_json::Value>,
        columnar: bool,
    },
}

#[derive(Debug, Clone, MakePTree, Serialize)]
pub struct RealtimeWALSearchQuery {
    pub filter: Option<Box<Expr>>,
    pub unpivot: Vec<UnpivotProjectionExpr>,
    pub projection: Vec<Alias>,
    pub sort: Vec<OptimizedSortItem>,
}

impl MakePTree for TantivyProjectedField {
    fn label(&self) -> String {
        format!("Alias \"{}\"", self.alias)
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        let field_name = vec![self.top_level_field.clone()]
            .into_iter()
            .chain(self.json_path.iter().map(|p| match p {
                PathPiece::Key(s) => s.clone(),
                PathPiece::Index(i) => i.to_string(),
            }))
            .collect::<Vec<_>>()
            .join(".");
        let field_name = if self.repeated {
            format!("[{}]", field_name)
        } else {
            field_name
        };
        builder.begin_child(format!(
            "{} \"{}\"",
            if self.only_exists { "Exists" } else { "Field" },
            field_name
        ));
        builder.add_empty_child(format!("Type: {}", self.field_type.label()));
        builder.add_empty_child(format!("Columnar: {}", self.can_project_fast_field()));
        builder.end_child();
    }
}

impl MakePTree for OptimizedTantivySearch {
    fn label(&self) -> String {
        "Search".to_string()
    }
    fn passthrough(&self) -> bool {
        true
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        match self {
            OptimizedTantivySearch::AllQuery => {
                builder.add_empty_child("All".to_string());
            }
            OptimizedTantivySearch::EmptyQuery => {
                builder.add_empty_child("Empty".to_string());
            }
            OptimizedTantivySearch::BooleanQuery(query) => {
                builder.begin_child("Boolean".to_string());
                for (op, search) in query {
                    builder.begin_child(format!("{:?}", op));
                    self.add_child(builder, search);
                    builder.end_child();
                }
                builder.end_child();
            }
            OptimizedTantivySearch::ExistsQuery { field } => {
                builder.begin_child("Exists".to_string());
                self.add_child(builder, field);
                builder.end_child();
            }
            OptimizedTantivySearch::JSONExistsQuery { field, json_path } => {
                builder.begin_child("JSONExists".to_string());
                self.add_child(builder, field);
                builder.add_empty_child(format!(
                    "JSON Path: {}",
                    util::json::serialize_path(json_path)
                ));
                builder.end_child();
            }
            OptimizedTantivySearch::RegexQuery {
                field,
                json_path,
                pattern,
                case_insensitive,
            } => {
                builder.begin_child("Regex".to_string());
                self.add_child(builder, field);
                builder.add_empty_child(format!("Regex: {}", pattern));
                if !json_path.is_empty() {
                    builder.add_empty_child(format!(
                        "JSON Path: {}",
                        util::json::serialize_path(json_path)
                    ));
                }
                builder.add_empty_child(if *case_insensitive {
                    "Case insensitive".to_string()
                } else {
                    "Case sensitive".to_string()
                });
                builder.end_child();
            }
            OptimizedTantivySearch::TermQuery(query) => {
                builder.begin_child("Term".to_string());
                self.add_child(builder, &query.field);
                builder
                    .add_empty_child(format!("Match: {} ({:?})", query.value, query.tantivy_type));
                builder.end_child();
            }
            OptimizedTantivySearch::RangeQuery {
                field,
                json_path,
                tantivy_type,
                lower,
                upper,
                columnar,
            } => {
                builder.begin_child(format!(
                    "Range{}",
                    if *columnar { " (columnar)" } else { "" }
                ));
                builder.add_empty_child(format!(
                    "Field {} ({:?})",
                    make_field_path(&field, &json_path),
                    tantivy_type
                ));

                builder.begin_child("Lower".to_string());
                self.add_child(builder, lower);
                builder.end_child();
                builder.begin_child("Upper".to_string());
                self.add_child(builder, upper);
                builder.end_child();
            }
        }
    }
}

impl MakePTree for TermQuery {
    fn label(&self) -> String {
        "Term".to_string()
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        builder.add_empty_child(format!(
            "Field {} ({:?})",
            make_field_path(&self.field, &self.json_path),
            self.tantivy_type
        ));
        builder.add_empty_child(format!("Matches: {}", self.value));
    }
}

fn make_field_path(field: &TantivyField, json_path: &[String]) -> String {
    vec![field.name.clone()]
        .into_iter()
        .chain(json_path.iter().map(|s| s.clone()))
        .collect::<Vec<_>>()
        .join(".")
}

impl MakePTree for OptimizedSortItem {
    fn label(&self) -> String {
        format!("{:?}", self.dir)
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, &self.expr);
    }
}

impl MakePTree for UnpivotProjectionExpr {
    fn label(&self) -> String {
        match &self.projected_field {
            UnpivotProjectedField::Array { item } => format!("{} -> [{}]", self.base_field, item),
            UnpivotProjectedField::Object { key, value } => {
                format!("{} -> [{}, {}]", self.base_field, key, value)
            }
        }
    }
    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}
