use std::{
    collections::{HashMap, HashSet},
    ops::Bound,
};

use rand::prelude::*;
use rand::rngs::StdRng;
use rand::{thread_rng, SeedableRng};
use serde::Serialize;
use util::{itertools::Itertools, ptree::MakePTree, uuid::Uuid};

use crate::global_store::SegmentFieldStatistics;

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Eq, Serialize)]
pub enum SamplingMode {
    NoSeed,
    Seed(u64),
}

#[derive(Debug, <PERSON>lone, MakePTree, Serialize)]
pub struct SegmentBatchingSortSpec {
    pub field: String,
    pub descending: bool,
}

#[derive(Debug, <PERSON>lone, MakePTree, Serialize)]
pub enum SegmentEliminationFilterSpec {
    Range {
        field: String,
        min: Bound<u64>,
        max: Bound<u64>,
    },
    IdFilter {
        ids: HashSet<String>,
        root_span_ids: HashSet<String>,
    },
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ComputeSegmentBatchInput<'a> {
    pub segment_ids: &'a [Uuid],
    pub statistics: &'a HashMap<String /* field */, HashMap<Uuid, SegmentFieldStatistics>>,
    pub id_to_segment_id: &'a HashMap<String, HashSet<Uuid>>,
    pub root_span_id_to_segment_id: &'a HashMap<String, HashSet<Uuid>>,
    pub sort: &'a Option<SegmentBatchingSortSpec>,
    pub filters: &'a [SegmentEliminationFilterSpec],
    pub partition_all: bool,
    pub sampling: Option<SamplingMode>,
}

#[derive(Debug, Clone)]
pub struct ComputeSegmentBatchOpts {
    pub initial_segment_batch_size: usize,
}

// Each segment has 100,000 docs, and we can read about 1m efficiently at a time. To make the initial
// search very snappy, we read the first 4 segments, then the next 32 segments, then the next 256 segments, etc.
// See process_wal::default_max_rows_per_segment for more info on the 100,000 number.
const SEGMENT_BRANCH_FACTOR: usize = 8;
const MAX_SEGMENT_BATCH_SIZE: usize = 256;
const SAMPLING_BATCH_SIZE: usize = 100;

#[derive(Debug, Clone)]
pub struct SegmentBatch {
    pub segments: Vec<Uuid>,
    pub filter: Option<SegmentBatchRange>,
}

#[derive(Debug, Clone, Eq, PartialEq)]
pub struct SegmentBatchRange {
    pub lower: Bound<u64>,
    pub upper: Bound<u64>,
}

pub fn compute_segment_batches(
    input: ComputeSegmentBatchInput,
    opts: ComputeSegmentBatchOpts,
) -> Vec<SegmentBatch> {
    let segment_ids = filter_segments(
        input.segment_ids,
        input.statistics,
        input.id_to_segment_id,
        input.root_span_id_to_segment_id,
        input.filters,
    );

    let segment_id_partitions = match (input.partition_all, &input.sampling, input.sort.as_ref()) {
        (true, _, _) => segment_ids
            // Partition all - each segment gets its own batch
            .into_iter()
            .map(|id| SegmentBatch {
                segments: vec![id],
                filter: None,
            })
            .collect(),

        (false, Some(sampling_mode), None) => {
            // Sampling without sort - shuffle segments then use fixed 100-segment batches
            let mut segment_ids = segment_ids;
            match sampling_mode {
                SamplingMode::Seed(seed) => {
                    let mut rng = StdRng::seed_from_u64(*seed);
                    segment_ids.shuffle(&mut rng);
                }
                SamplingMode::NoSeed => {
                    let mut rng = thread_rng();
                    segment_ids.shuffle(&mut rng);
                }
            }

            let batch_size = 100;
            segment_ids
                .chunks(batch_size)
                .map(|chunk| SegmentBatch {
                    segments: chunk.to_vec(),
                    filter: None,
                })
                .collect()
        }
        (false, Some(_), Some(sort)) if input.statistics.get(&sort.field).is_none() => {
            // Sampling with sort but no stats - batches of size 100
            segment_ids
                .chunks(SAMPLING_BATCH_SIZE)
                .map(|chunk| SegmentBatch {
                    segments: chunk.to_vec(),
                    filter: None,
                })
                .collect()
        }
        (false, Some(_), Some(sort)) => {
            // Sampling with sort and stats - use sort-based partitioning but with fixed batch sizes
            let field_stats = input.statistics.get(&sort.field).unwrap();
            partition_segments_by_sort_key(
                &segment_ids,
                field_stats,
                sort.descending,
                SAMPLING_BATCH_SIZE, // Fixed batch size for sampling
                SEGMENT_BRANCH_FACTOR,
                SAMPLING_BATCH_SIZE,
            )
        }

        // No sampling, no sort - use original exponential batching
        (false, None, None) => compute_partition_batches(
            segment_ids.into_iter().map(|id| {
                (
                    id,
                    SegmentFieldStatistics::new(u64::MIN, u64::MAX).unwrap(),
                    None,
                )
            }),
            false,
            opts.initial_segment_batch_size,
            SEGMENT_BRANCH_FACTOR,
            MAX_SEGMENT_BATCH_SIZE,
        ),

        // No sampling, sort but no stats - single batch with all segments
        (false, None, Some(sort)) if input.statistics.get(&sort.field).is_none() => {
            vec![SegmentBatch {
                segments: segment_ids.to_vec(),
                filter: None,
            }]
        }

        // No sampling, sort with stats - use original sort-based partitioning
        (false, None, Some(sort)) => {
            let field_stats = input.statistics.get(&sort.field).unwrap();
            partition_segments_by_sort_key(
                &segment_ids,
                field_stats,
                sort.descending,
                opts.initial_segment_batch_size,
                SEGMENT_BRANCH_FACTOR,
                MAX_SEGMENT_BATCH_SIZE,
            )
        }
    };

    // We're always guaranteed to have at least one batch, even if it's empty.
    if segment_id_partitions.is_empty() {
        vec![SegmentBatch {
            segments: vec![],
            filter: None,
        }]
    } else {
        segment_id_partitions
    }
}

fn filter_segments<'a>(
    segment_ids: &'a [Uuid],
    statistics: &'a HashMap<String /* field */, HashMap<Uuid, SegmentFieldStatistics>>,
    id_to_segment_id: &'a HashMap<String, HashSet<Uuid>>,
    root_span_id_to_segment_id: &'a HashMap<String, HashSet<Uuid>>,
    filters: &'a [SegmentEliminationFilterSpec],
) -> Vec<Uuid> {
    segment_ids
        .iter()
        .filter(|segment_id| {
            filters.iter().all(|filter| {
                match filter {
                    SegmentEliminationFilterSpec::Range { field, min, max } => {
                        let field_stats = match statistics.get(field.as_str()) {
                            None => return true, // Cannot filter it out
                            Some(field_stats) => field_stats,
                        };
                        let segment_stats = match field_stats.get(segment_id) {
                            None => return true, // Cannot filter it out
                            Some(segment_stats) => segment_stats,
                        };

                        // Check if the segment overlaps with the filter range by checking that both starts
                        // are less than the other's end.
                        segment_stats_passes_filter(segment_stats, *min, *max)
                    }
                    SegmentEliminationFilterSpec::IdFilter { ids, root_span_ids } => {
                        if ids.is_empty() && root_span_ids.is_empty() {
                            // This specific IdFilter is "empty" (no specific IDs to filter by),
                            // so it's considered non-restrictive for this segment.
                            return true;
                        }
                        for id in ids {
                            if let Some(segment_ids) = id_to_segment_id.get(id.as_str()) {
                                if segment_ids.contains(segment_id) {
                                    return true;
                                }
                            }
                        }
                        for root_span_id in root_span_ids {
                            if let Some(segment_ids) =
                                root_span_id_to_segment_id.get(root_span_id.as_str())
                            {
                                if segment_ids.contains(segment_id) {
                                    return true;
                                }
                            }
                        }

                        false
                    }
                }
            })
        })
        .cloned()
        .collect()
}

fn segment_stats_passes_filter(
    stats: &SegmentFieldStatistics,
    min: Bound<u64>,
    max: Bound<u64>,
) -> bool {
    (match min {
        Bound::Included(min) => min <= stats.max(),
        Bound::Excluded(min) => min < stats.max(),
        Bound::Unbounded => true,
    }) && (match max {
        Bound::Included(max) => stats.min() <= max,
        Bound::Excluded(max) => stats.min() < max,
        Bound::Unbounded => true,
    })
}

// Each partition must be considered at once, because it may have overlapping values for the sort key.
fn partition_segments_by_sort_key<'a>(
    segment_ids: &'a [Uuid],
    statistics: &'a HashMap<Uuid, SegmentFieldStatistics>,
    descending: bool,
    initial_segment_batch_size: usize,
    branch_factor: usize,
    max_segment_batch_size: usize,
) -> Vec<SegmentBatch> {
    let segment_ids: HashSet<Uuid> = segment_ids.iter().cloned().collect();

    // Sort the statistics. If it's descending, then sort by the maximums descending, and if it's ascending,
    // sort by the minimums ascending.
    let sorted_stats: Vec<_> = statistics
        .iter()
        .filter(|(segment_id, _)| segment_ids.contains(segment_id))
        .sorted_by(|a, b| {
            if descending {
                b.1.max().cmp(&a.1.max())
            } else {
                a.1.min().cmp(&b.1.min())
            }
        })
        .collect();

    if sorted_stats.len() != segment_ids.len() {
        return vec![SegmentBatch {
            segments: segment_ids.iter().cloned().collect(),
            filter: None,
        }];
    }

    // segment_bounds[i] is a bound that will ignore any potentially overlapping values in sorted_stats[i+1:]
    // If ascending, this will turn into an `upper` bound and if descending, should be a `lower` bound
    // (i.e. should be repeatedly `max`'d).
    //
    // For example, if you have segments like this following:
    // [0, 3], [2, 5], [4, 6], [7, 9], [8, 10]
    //
    // If ascending, the bounds will be:
    // [<2],   [<4],   [<7],   [<8],   [<= MAX]
    // And the final filters will be
    // [x<2],  [2<=x<4],  [4<=x<7],  [7<=x<8],  [x>=8]
    //
    // If descending, the sort order will be:
    // [8, 10], [7, 9], [4, 6], [2, 5], [0, 3]
    // and the bounds will be:
    // [x>9],   [6<x<=9],  [5<x<=6],  [3<x<=5],  [x> MIN]
    let segment_bounds: Vec<Option<u64>> = sorted_stats
        .iter()
        .skip(1)
        .map(|(_, stats)| Some(if descending { stats.max() } else { stats.min() }))
        .chain(std::iter::once(None))
        .collect();

    compute_partition_batches(
        sorted_stats
            .into_iter()
            .zip(segment_bounds)
            .map(|((segment_id, stats), bound)| (segment_id.clone(), stats.clone(), bound)),
        descending,
        initial_segment_batch_size,
        branch_factor,
        max_segment_batch_size,
    )
}

fn compute_partition_batches(
    bounded_segments: impl IntoIterator<Item = (Uuid, SegmentFieldStatistics, Option<u64>)>,
    descending: bool,
    initial_segment_batch_size: usize,
    branch_factor: usize,
    max_segment_batch_size: usize,
) -> Vec<SegmentBatch> {
    let mut overlapping_segments: Vec<(Uuid, SegmentFieldStatistics)> = vec![];
    let mut partitions = vec![];
    let mut curr_partition = vec![];
    let mut current_batch_size = initial_segment_batch_size;
    for (segment_id, stats, bound) in bounded_segments.into_iter() {
        curr_partition.push((segment_id, stats));

        if curr_partition.len() >= current_batch_size && current_batch_size > 0 {
            push_partition(
                &mut partitions,
                descending,
                bound,
                &mut overlapping_segments,
                curr_partition,
            );
            current_batch_size = (current_batch_size * branch_factor).min(max_segment_batch_size);
            curr_partition = vec![];
        }
    }
    if !curr_partition.is_empty() {
        push_partition(
            &mut partitions,
            descending,
            None,
            &mut overlapping_segments,
            curr_partition,
        );
    }

    partitions.retain(|batch| batch.segments.len() > 0);

    partitions
}

fn push_partition(
    partitions: &mut Vec<SegmentBatch>,
    descending: bool,
    bound: Option<u64>,
    overlapping_segments: &mut Vec<(Uuid, SegmentFieldStatistics)>,
    curr_partition: Vec<(Uuid, SegmentFieldStatistics)>,
) {
    let filter = if descending {
        SegmentBatchRange {
            lower: bound.map_or(Bound::Unbounded, Bound::Excluded),
            upper: partitions
                .last()
                .and_then(|batch: &SegmentBatch| batch.filter.as_ref())
                .and_then(|filter| match filter.lower {
                    Bound::Included(_) => panic!("Included bound"),
                    Bound::Excluded(lower) => Some(Bound::Included(lower)),
                    Bound::Unbounded => None,
                })
                .unwrap_or_else(|| {
                    overlapping_segments
                        .iter()
                        .map(|(_, stats)| stats.min())
                        .min()
                        .map_or(Bound::Unbounded, Bound::Excluded)
                }),
        }
    } else {
        SegmentBatchRange {
            lower: partitions
                .last()
                .and_then(|batch: &SegmentBatch| batch.filter.as_ref())
                .and_then(|filter| match filter.upper {
                    Bound::Included(_) => panic!("Included bound"),
                    Bound::Excluded(upper) => Some(Bound::Included(upper)),
                    Bound::Unbounded => None,
                })
                .unwrap_or_else(|| {
                    overlapping_segments
                        .iter()
                        .map(|(_, stats)| stats.max())
                        .max()
                        .map_or(Bound::Unbounded, Bound::Excluded)
                }),
            upper: bound.map_or(Bound::Unbounded, Bound::Excluded),
        }
    };

    overlapping_segments
        .retain(|(_, stats)| segment_stats_passes_filter(stats, filter.lower, filter.upper));
    overlapping_segments.extend(curr_partition);

    let overlapping_min = overlapping_segments
        .iter()
        .map(|(_, stats)| stats.min())
        .min()
        .unwrap_or(u64::MIN);
    let overlapping_max = overlapping_segments
        .iter()
        .map(|(_, stats)| stats.max())
        .max()
        .unwrap_or(u64::MAX);

    let filter = SegmentBatchRange {
        lower: if match filter.lower {
            Bound::Included(lower) => lower <= overlapping_min,
            Bound::Excluded(lower) => lower < overlapping_min,
            Bound::Unbounded => true,
        } {
            Bound::Unbounded
        } else {
            filter.lower
        },
        upper: if match filter.upper {
            Bound::Included(upper) => upper >= overlapping_max,
            Bound::Excluded(upper) => upper > overlapping_max,
            Bound::Unbounded => true,
        } {
            Bound::Unbounded
        } else {
            filter.upper
        },
    };

    let partition_segments = overlapping_segments
        .iter()
        .filter(|(_, stats)| segment_stats_passes_filter(stats, filter.lower, filter.upper))
        .map(|(id, _)| id.clone())
        .collect::<Vec<_>>();
    partitions.push(SegmentBatch {
        segments: partition_segments,
        filter: if filter.lower == Bound::Unbounded && filter.upper == Bound::Unbounded {
            None
        } else {
            Some(filter)
        },
    });

    // This is quite subtle, but is essentially saying, if we do not have any bounds, then there's
    // no need to carry over these segments to the next iteration. This is set when we don't have any
    // sort key.
    if matches!(bound, None) {
        overlapping_segments.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_segment_batching_edge_cases() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4()];
        let input = ComputeSegmentBatchInput {
            segment_ids: &segment_ids,
            statistics: &HashMap::new(),
            id_to_segment_id: &HashMap::new(),
            root_span_id_to_segment_id: &HashMap::new(),
            sort: &None,
            filters: &[],
            partition_all: false,
            sampling: None,
        };
        let batches = compute_segment_batches(
            input.clone(),
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 0,
            },
        );
        assert_eq!(batches.len(), 1);
        assert_eq!(batches[0].segments.len(), 2);

        let batches = compute_segment_batches(
            input.clone(),
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 1,
            },
        );
        // Without sampling, should use original batching logic
        assert_eq!(batches.len(), 2);
        for batch in batches {
            assert_eq!(batch.segments.len(), 1);
        }

        let batches = compute_segment_batches(
            input.clone(),
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 2,
            },
        );
        assert_eq!(batches.len(), 1);
        assert_eq!(batches[0].segments.len(), 2);

        let batches = compute_segment_batches(
            input.clone(),
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 3,
            },
        );
        assert_eq!(batches.len(), 1);
        assert_eq!(batches[0].segments.len(), 2);

        let batches = compute_segment_batches(
            input.clone(),
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 0,
            },
        );
        assert_eq!(batches.len(), 1);

        let batches = compute_segment_batches(
            input.clone(),
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 1,
            },
        );
        // Without sampling, should use original batching logic
        assert_eq!(batches.len(), 2);
        assert_eq!(batches[0].segments.len(), 1);
        assert_eq!(batches[1].segments.len(), 1);
    }

    fn verify_partitions(
        stats: &[(Uuid, SegmentFieldStatistics)],
        expected_partition_indices: &[(Vec<usize>, Option<SegmentBatchRange>)],
        descending: bool,
    ) {
        eprintln!(
            "stats: {:?}",
            stats
                .iter()
                .map(|(id, stat)| (id, stat.min(), stat.max()))
                .collect::<Vec<_>>()
        );
        let segment_ids: Vec<Uuid> = stats.iter().map(|(id, _)| id).cloned().collect();
        let stats_map = stats.iter().cloned().collect();
        let partitions = partition_segments_by_sort_key(
            &segment_ids,
            &stats_map,
            descending,
            1,
            SEGMENT_BRANCH_FACTOR,
            MAX_SEGMENT_BATCH_SIZE,
        );
        eprintln!("partitions: {:?}", partitions);
        eprintln!(
            "expected_partition_indices: {:?}",
            expected_partition_indices
        );
        assert_eq!(partitions.len(), expected_partition_indices.len());

        for (partition_idx, (expected_indices, expected_filter)) in
            expected_partition_indices.iter().enumerate()
        {
            assert_eq!(
                partitions[partition_idx].segments.len(),
                expected_indices.len(),
                "Partition {} has wrong size",
                partition_idx
            );

            // Convert both sets of UUIDs to HashSets to compare membership without caring about order
            let partition_set: std::collections::HashSet<_> =
                partitions[partition_idx].segments.iter().collect();
            let expected_set: std::collections::HashSet<_> =
                expected_indices.iter().map(|idx| &stats[*idx].0).collect();

            assert_eq!(
                partition_set, expected_set,
                "Partition {} has wrong segments",
                partition_idx
            );

            assert_eq!(&partitions[partition_idx].filter, expected_filter);
        }
    }

    #[test]
    fn test_partition_segments_empty_and_single() {
        // Empty input
        let stats = HashMap::new();
        for descending in [false, true] {
            let partitions = partition_segments_by_sort_key(
                &[],
                &stats,
                descending,
                1,
                SEGMENT_BRANCH_FACTOR,
                MAX_SEGMENT_BATCH_SIZE,
            );
            assert_eq!(partitions.len(), 0);
        }

        // Single segment
        let stats = vec![(Uuid::new_v4(), SegmentFieldStatistics::new(0, 100).unwrap())];
        for descending in [false, true] {
            verify_partitions(&stats, &[(vec![0], None)], descending);
        }

        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4()];
        let stats: HashMap<Uuid, SegmentFieldStatistics> =
            vec![(segment_ids[0], SegmentFieldStatistics::new(0, 100).unwrap())]
                .into_iter()
                .collect();
        for descending in [false, true] {
            let partitions = partition_segments_by_sort_key(
                &segment_ids,
                &stats,
                descending,
                1,
                SEGMENT_BRANCH_FACTOR,
                MAX_SEGMENT_BATCH_SIZE,
            );
            assert_eq!(partitions.len(), 1);
            assert_eq!(partitions[0].segments.len(), 2);
        }
    }

    #[test]
    fn test_partition_segments_non_overlapping() {
        let stats = vec![
            (Uuid::new_v4(), SegmentFieldStatistics::new(0, 99).unwrap()),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(100, 199).unwrap(),
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(200, 300).unwrap(),
            ),
        ];

        // Ascending: should maintain original order
        verify_partitions(&stats, &[(vec![0], None), (vec![1, 2], None)], false);

        // Descending: should reverse the order
        verify_partitions(&stats, &[(vec![2], None), (vec![1, 0], None)], true);
    }

    #[test]
    fn test_partition_segments_partial_overlap() {
        let stats = vec![
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(0, 150).unwrap(), // overlaps with 1
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(50, 200).unwrap(), // overlaps with 0 and 2
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(175, 300).unwrap(), // overlaps with 2
            ),
        ];

        // Ascending
        verify_partitions(
            &stats,
            &[
                (
                    vec![0],
                    Some(SegmentBatchRange {
                        lower: Bound::Unbounded,
                        upper: Bound::Excluded(50),
                    }),
                ),
                (
                    vec![0, 1, 2],
                    Some(SegmentBatchRange {
                        lower: Bound::Included(50),
                        upper: Bound::Unbounded,
                    }),
                ),
            ],
            false,
        );

        // Descending
        verify_partitions(
            &stats,
            &[
                (
                    vec![2],
                    Some(SegmentBatchRange {
                        lower: Bound::Excluded(200),
                        upper: Bound::Unbounded,
                    }),
                ),
                (
                    vec![1, 0, 2],
                    Some(SegmentBatchRange {
                        lower: Bound::Unbounded,
                        upper: Bound::Included(200),
                    }),
                ),
            ],
            true,
        );
    }

    #[test]
    fn test_partition_segments_all_overlapping() {
        let stats = vec![
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(0, 1000).unwrap(),
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(100, 500).unwrap(),
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(200, 800).unwrap(),
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(400, 600).unwrap(),
            ),
        ];

        // Both ascending and descending should result in a single partition
        // but with different ordering
        verify_partitions(
            &stats,
            &[
                (
                    vec![0],
                    Some(SegmentBatchRange {
                        lower: Bound::Unbounded,
                        upper: Bound::Excluded(100),
                    }),
                ),
                (
                    vec![0, 1, 2, 3],
                    Some(SegmentBatchRange {
                        lower: Bound::Included(100),
                        upper: Bound::Unbounded,
                    }),
                ),
            ],
            false,
        );
        verify_partitions(
            &stats,
            &[
                (
                    vec![0],
                    Some(SegmentBatchRange {
                        lower: Bound::Excluded(800),
                        upper: Bound::Unbounded,
                    }),
                ),
                (
                    vec![0, 1, 2, 3],
                    Some(SegmentBatchRange {
                        lower: Bound::Unbounded,
                        upper: Bound::Included(800),
                    }),
                ),
            ],
            true,
        );
    }

    #[test]
    fn test_partition_segments_same_values() {
        let stats = vec![
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(100, 100).unwrap(),
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(100, 100).unwrap(),
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(200, 200).unwrap(),
            ),
        ];

        // Ascending: segments with same value should be in same partition
        verify_partitions(
            &stats,
            &[
                // We pruned the first partition (100-100) because it doesn't overlap with the filter.
                (vec![0, 1, 2], None),
            ],
            false,
        );

        // Descending: higher values first, then segments with same value together
        verify_partitions(&stats, &[(vec![2], None), (vec![0, 1], None)], true);
    }

    #[test]
    fn test_partition_segments_edge_overlaps() {
        let stats = vec![
            (Uuid::new_v4(), SegmentFieldStatistics::new(0, 100).unwrap()),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(100, 199).unwrap(), // touches first
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(200, 300).unwrap(),
            ),
        ];

        // Ascending: exact boundary matches should be considered overlapping
        verify_partitions(
            &stats,
            &[
                (
                    vec![0],
                    Some(SegmentBatchRange {
                        lower: Bound::Unbounded,
                        upper: Bound::Excluded(100),
                    }),
                ),
                (
                    vec![0, 1, 2],
                    Some(SegmentBatchRange {
                        lower: Bound::Included(100),
                        upper: Bound::Unbounded,
                    }),
                ),
            ],
            false,
        );

        // Descending
        verify_partitions(
            &stats,
            &[
                (vec![2], None),
                (
                    // Partition 2 gets filtered out because it doesn't match the range.
                    vec![0, 1],
                    None,
                ),
            ],
            true,
        );
    }

    #[test]
    fn test_partition_segments_mixed_ranges() {
        let stats = vec![
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(0, 350).unwrap(), // wide range
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(100, 100).unwrap(), // point value
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(200, 300).unwrap(), // medium range
            ),
            (
                Uuid::new_v4(),
                SegmentFieldStatistics::new(400, 400).unwrap(), // another point value
            ),
        ];

        // Ascending: wide range captures everything up to first gap
        verify_partitions(
            &stats,
            &[
                (
                    vec![0],
                    Some(SegmentBatchRange {
                        lower: Bound::Unbounded,
                        upper: Bound::Excluded(100),
                    }),
                ),
                (
                    vec![0, 1, 2, 3],
                    Some(SegmentBatchRange {
                        lower: Bound::Included(100),
                        upper: Bound::Unbounded,
                    }),
                ),
            ],
            false,
        );

        // Descending: segments ordered by max value
        verify_partitions(&stats, &[(vec![3], None), (vec![2, 1, 0], None)], true);
    }

    #[test]
    fn test_partition_segments_no_overlap_with_gaps() {
        let stats = vec![
            (Uuid::new_v4(), SegmentFieldStatistics::new(0, 10).unwrap()),
            (Uuid::new_v4(), SegmentFieldStatistics::new(20, 30).unwrap()),
            (Uuid::new_v4(), SegmentFieldStatistics::new(50, 60).unwrap()),
        ];

        // Ascending: should maintain original order, with each segment in its own partition
        verify_partitions(&stats, &[(vec![0], None), (vec![1, 2], None)], false);

        // Descending: should reverse the order, with each segment in its own partition
        verify_partitions(&stats, &[(vec![2], None), (vec![1, 0], None)], true);
    }

    #[test]
    fn test_filter_segments_basic() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
        let mut statistics = HashMap::new();
        let mut field_stats = HashMap::new();

        // Segment 0: 0-100
        // Segment 1: 50-150
        // Segment 2: 200-300
        field_stats.insert(segment_ids[0], SegmentFieldStatistics::new(0, 100).unwrap());
        field_stats.insert(
            segment_ids[1],
            SegmentFieldStatistics::new(50, 150).unwrap(),
        );
        field_stats.insert(
            segment_ids[2],
            SegmentFieldStatistics::new(200, 300).unwrap(),
        );
        statistics.insert("field1".to_string(), field_stats);

        // Test no filters - should return all segments
        let filters = vec![];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 3);

        // Test single filter that matches some segments
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Included(40),
            max: Bound::Included(160),
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
        assert!(result.contains(&segment_ids[0]));
        assert!(result.contains(&segment_ids[1]));
    }

    #[test]
    fn test_filter_segments_missing_stats() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
        let mut statistics = HashMap::new();
        let mut field_stats = HashMap::new();

        // Only include stats for segment 0
        field_stats.insert(segment_ids[0], SegmentFieldStatistics::new(0, 100).unwrap());
        statistics.insert("field1".to_string(), field_stats);

        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Included(40),
            max: Bound::Included(160),
        }];

        // Should include segments with missing stats
        eprintln!("segment_ids: {:?}", segment_ids);
        eprintln!("statistics: {:?}", statistics);
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        eprintln!("result: {:?}", result);
        assert_eq!(result.len(), 3);
        assert!(result.contains(&segment_ids[0]));
        assert!(result.contains(&segment_ids[1]));
        assert!(result.contains(&segment_ids[2]));
    }

    #[test]
    fn test_filter_segments_missing_field() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4()];
        let mut statistics = HashMap::new();
        let mut field_stats = HashMap::new();

        field_stats.insert(segment_ids[0], SegmentFieldStatistics::new(0, 100).unwrap());
        field_stats.insert(
            segment_ids[1],
            SegmentFieldStatistics::new(50, 150).unwrap(),
        );
        statistics.insert("field1".to_string(), field_stats);

        // Filter on non-existent field
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "non_existent_field".to_string(),
            min: Bound::Included(0),
            max: Bound::Included(50),
        }];

        // Should include all segments when field is missing
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
    }

    #[test]
    fn test_filter_segments_multiple_fields() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
        let mut statistics = HashMap::new();

        // Set up field1 statistics
        let mut field1_stats = HashMap::new();
        field1_stats.insert(segment_ids[0], SegmentFieldStatistics::new(0, 100).unwrap());
        field1_stats.insert(
            segment_ids[1],
            SegmentFieldStatistics::new(50, 150).unwrap(),
        );
        field1_stats.insert(
            segment_ids[2],
            SegmentFieldStatistics::new(200, 300).unwrap(),
        );
        statistics.insert("field1".to_string(), field1_stats);

        // Set up field2 statistics
        let mut field2_stats = HashMap::new();
        field2_stats.insert(segment_ids[0], SegmentFieldStatistics::new(0, 50).unwrap());
        field2_stats.insert(segment_ids[1], SegmentFieldStatistics::new(25, 75).unwrap());
        field2_stats.insert(
            segment_ids[2],
            SegmentFieldStatistics::new(60, 100).unwrap(),
        );
        statistics.insert("field2".to_string(), field2_stats);

        // Test multiple fields with overlap
        let filters = vec![
            SegmentEliminationFilterSpec::Range {
                field: "field1".to_string(),
                min: Bound::Included(40),
                max: Bound::Included(160),
            },
            SegmentEliminationFilterSpec::Range {
                field: "field2".to_string(),
                min: Bound::Included(20),
                max: Bound::Included(80),
            },
        ];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
        assert!(result.contains(&segment_ids[0]));
        assert!(result.contains(&segment_ids[1]));

        // Test multiple fields with no overlap
        let filters = vec![
            SegmentEliminationFilterSpec::Range {
                field: "field1".to_string(),
                min: Bound::Included(0),
                max: Bound::Included(100),
            },
            SegmentEliminationFilterSpec::Range {
                field: "field2".to_string(),
                min: Bound::Included(80),
                max: Bound::Included(100),
            },
        ];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 0);
    }

    #[test]
    fn test_filter_segments_no_stats() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4()];
        let statistics = HashMap::new();

        // Test with empty statistics
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Included(0),
            max: Bound::Included(50),
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);

        // Test with empty statistics and multiple filters
        let filters = vec![
            SegmentEliminationFilterSpec::Range {
                field: "field1".to_string(),
                min: Bound::Included(0),
                max: Bound::Included(50),
            },
            SegmentEliminationFilterSpec::Range {
                field: "field2".to_string(),
                min: Bound::Included(20),
                max: Bound::Included(80),
            },
        ];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
    }

    #[test]
    fn test_filter_segments_bound_types() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
        let mut statistics = HashMap::new();
        let mut field_stats = HashMap::new();

        // Segment 0: 0-100
        // Segment 1: 100-200
        // Segment 2: 200-300
        field_stats.insert(segment_ids[0], SegmentFieldStatistics::new(0, 100).unwrap());
        field_stats.insert(
            segment_ids[1],
            SegmentFieldStatistics::new(100, 200).unwrap(),
        );
        field_stats.insert(
            segment_ids[2],
            SegmentFieldStatistics::new(200, 300).unwrap(),
        );
        statistics.insert("field1".to_string(), field_stats);

        // Test inclusive bounds
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Included(100),
            max: Bound::Included(200),
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 3);
        assert!(result.contains(&segment_ids[0]));
        assert!(result.contains(&segment_ids[1]));
        assert!(result.contains(&segment_ids[2]));

        // Test exclusive bounds
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Excluded(100),
            max: Bound::Excluded(200),
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 1);
        assert!(result.contains(&segment_ids[1]));

        // Test mixed bounds
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Included(100),
            max: Bound::Excluded(200),
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
        assert!(result.contains(&segment_ids[0]));
        assert!(result.contains(&segment_ids[1]));

        // Test unbounded minimum
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Unbounded,
            max: Bound::Included(150),
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
        assert!(result.contains(&segment_ids[0]));
        assert!(result.contains(&segment_ids[1]));

        // Test unbounded maximum
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Included(150),
            max: Bound::Unbounded,
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
        assert!(result.contains(&segment_ids[1]));
        assert!(result.contains(&segment_ids[2]));

        // Test fully unbounded
        let filters = vec![SegmentEliminationFilterSpec::Range {
            field: "field1".to_string(),
            min: Bound::Unbounded,
            max: Bound::Unbounded,
        }];
        let result = filter_segments(
            &segment_ids,
            &statistics,
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 3);
    }

    #[test]
    fn test_segment_id_filter() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
        // First, test that if the id filter exists and it's not in the segment map, all segments are eliminated
        let filters = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: ["id1".to_string()].into(),
            root_span_ids: HashSet::new(),
        }];
        let result = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &HashMap::new(),
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 0);

        // Now, test that if the id filter exists, and it's in the segment map, the segment is eliminated
        let id_to_segment_id = HashMap::from([
            (
                "id1".to_string(),
                HashSet::from([segment_ids[0], segment_ids[1]]),
            ),
            ("id2".to_string(), HashSet::from([segment_ids[1]])),
            ("id3".to_string(), HashSet::from([segment_ids[2]])),
        ]);
        let result = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &HashMap::new(),
            &filters,
        );
        assert_eq!(result.len(), 2);
        assert!(result.contains(&segment_ids[0]));
        assert!(result.contains(&segment_ids[1]));
    }

    #[test]
    fn test_segment_id_and_root_span_id_filter() {
        let segment_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
        let segment0 = segment_ids[0];
        let segment1 = segment_ids[1];
        let segment2 = segment_ids[2];

        let id_to_segment_id = HashMap::from([
            ("id1".to_string(), HashSet::from([segment0])),
            ("id2".to_string(), HashSet::from([segment1])),
        ]);

        let root_span_id_to_segment_id = HashMap::from([
            ("rsid1".to_string(), HashSet::from([segment0])), // Corresponds to id1
            ("rsid2".to_string(), HashSet::from([segment1])), // Corresponds to id2
            ("rsid3".to_string(), HashSet::from([segment2])), // Independent root_span_id
        ]);

        // Scenario 1: id1 OR rsid2
        let filters1 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::from(["id1".to_string()]),
            root_span_ids: HashSet::from(["rsid2".to_string()]),
        }];
        let result1 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters1,
        );
        assert_eq!(result1.len(), 2);
        assert!(result1.contains(&segment0));
        assert!(result1.contains(&segment1));

        // Scenario 2: id1 OR rsid1 (its own root span id)
        let filters2 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::from(["id1".to_string()]),
            root_span_ids: HashSet::from(["rsid1".to_string()]),
        }];
        let result2 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters2,
        );
        assert_eq!(result2.len(), 1);
        assert!(result2.contains(&segment0));

        // Scenario 3: id1 OR rsid3
        let filters3 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::from(["id1".to_string()]),
            root_span_ids: HashSet::from(["rsid3".to_string()]),
        }];
        let result3 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters3,
        );
        assert_eq!(result3.len(), 2);
        assert!(result3.contains(&segment0));
        assert!(result3.contains(&segment2));

        // Scenario 4: non_existent_id OR rsid1
        let filters4 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::from(["non_existent_id".to_string()]),
            root_span_ids: HashSet::from(["rsid1".to_string()]),
        }];
        let result4 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters4,
        );
        assert_eq!(result4.len(), 1);
        assert!(result4.contains(&segment0));

        // Scenario 5: id1 OR non_existent_rsid
        let filters5 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::from(["id1".to_string()]),
            root_span_ids: HashSet::from(["non_existent_rsid".to_string()]),
        }];
        let result5 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters5,
        );
        assert_eq!(result5.len(), 1);
        assert!(result5.contains(&segment0));

        // Scenario 6: non_existent_id OR non_existent_rsid
        let filters6 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::from(["non_existent_id".to_string()]),
            root_span_ids: HashSet::from(["non_existent_rsid".to_string()]),
        }];
        let result6 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters6,
        );
        assert_eq!(result6.len(), 0);

        // Scenario 7: Empty ids and root_span_ids (should return all segments as it's not a restrictive filter)
        let filters7 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::new(),
            root_span_ids: HashSet::new(),
        }];
        let result7 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters7,
        );
        // This behavior might depend on how empty IdFilter is interpreted. If it means "match nothing specific by ID",
        // then it shouldn't filter out segments unless combined with other restrictive filters.
        // Given the current logic of filter_segments, an IdFilter with empty lists will result in an empty union of segment sets initially,
        // which, if it's the *only* filter, leads to all segments being returned as there's no restriction.
        assert_eq!(result7.len(), 3);

        // Scenario 8: Single ID, empty root_span_ids
        let filters8 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::from(["id1".to_string()]),
            root_span_ids: HashSet::new(),
        }];
        let result8 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters8,
        );
        assert_eq!(result8.len(), 1);
        assert!(result8.contains(&segment0));

        // Scenario 9: Empty ids, single root_span_id
        let filters9 = vec![SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::new(),
            root_span_ids: HashSet::from(["rsid1".to_string()]),
        }];
        let result9 = filter_segments(
            &segment_ids,
            &HashMap::new(),
            &id_to_segment_id,
            &root_span_id_to_segment_id,
            &filters9,
        );
        assert_eq!(result9.len(), 1);
        assert!(result9.contains(&segment0));
    }

    #[test]
    fn test_max_segment_batch_size_limit() {
        // Create 200 segments to test that batch size stays at MAX_SEGMENT_BATCH_SIZE
        let num_segments: usize = 200;
        let mut segment_ids = Vec::new();
        let mut statistics = HashMap::new();
        let mut field_stats = HashMap::new();

        for i in 0..num_segments {
            let id = Uuid::new_v4();
            segment_ids.push(id);
            // Non-overlapping segments
            field_stats.insert(
                id,
                SegmentFieldStatistics::new((i * 10) as u64, (i * 10 + 5) as u64).unwrap(),
            );
        }
        statistics.insert("field".to_string(), field_stats);

        // Start with initial_segment_batch_size=1, branch_factor=8
        // This should create batches of size: 1, 8, 64, 64, 64, 9 (remaining)
        let partitions = partition_segments_by_sort_key(
            &segment_ids,
            &statistics.get("field").unwrap(),
            false, // ascending
            1,
            8,
            MAX_SEGMENT_BATCH_SIZE,
        );

        // Verify no batch exceeds MAX_SEGMENT_BATCH_SIZE and after reaching it, all batches are that size (except possibly the last)
        for (i, batch) in partitions.iter().enumerate() {
            assert!(batch.segments.len() <= MAX_SEGMENT_BATCH_SIZE);

            match i {
                0 => assert_eq!(batch.segments.len(), 1),
                1 => assert_eq!(batch.segments.len(), 8),
                2 => assert_eq!(batch.segments.len(), 64),
                _ => {
                    // After the third batch, sizes should be MAX_SEGMENT_BATCH_SIZE except possibly the final remainder
                    if i < partitions.len() - 1 {
                        assert_eq!(batch.segments.len(), MAX_SEGMENT_BATCH_SIZE);
                    }
                }
            }
        }

        // After processing the first 1 and 8 sized batches (total 9) and the 64-sized batch (total 73),
        // the remaining segments are grouped into batches of MAX_SEGMENT_BATCH_SIZE.
        let expected_partitions =
            3 + ((num_segments - 73 + MAX_SEGMENT_BATCH_SIZE - 1) / MAX_SEGMENT_BATCH_SIZE);
        assert_eq!(partitions.len(), expected_partitions);
    }

    #[test]
    fn test_sampling_batching_behavior() {
        let segment_ids = (0..250).map(|_| Uuid::new_v4()).collect::<Vec<_>>();

        // Test without sampling - should use original batching logic
        let input_no_sampling = ComputeSegmentBatchInput {
            segment_ids: &segment_ids,
            statistics: &HashMap::new(),
            id_to_segment_id: &HashMap::new(),
            root_span_id_to_segment_id: &HashMap::new(),
            sort: &None,
            filters: &[],
            partition_all: false,
            sampling: None,
        };

        let batches_no_sampling = compute_segment_batches(
            input_no_sampling,
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 4,
            },
        );

        // With no sampling, should use exponential batching (4, 32, 214 remaining)
        assert!(batches_no_sampling.len() >= 3);

        // Test with sampling - should batch in fixed sizes of 100
        let input_with_sampling = ComputeSegmentBatchInput {
            segment_ids: &segment_ids,
            statistics: &HashMap::new(),
            id_to_segment_id: &HashMap::new(),
            root_span_id_to_segment_id: &HashMap::new(),
            sort: &None,
            filters: &[],
            partition_all: false,
            sampling: Some(SamplingMode::Seed(12345)),
        };

        let batches_with_sampling = compute_segment_batches(
            input_with_sampling,
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 4, // This should be ignored
            },
        );

        // With sampling, should batch in sizes of 100: 100, 100, 50
        assert_eq!(batches_with_sampling.len(), 3);
        assert_eq!(batches_with_sampling[0].segments.len(), 100);
        assert_eq!(batches_with_sampling[1].segments.len(), 100);
        assert_eq!(batches_with_sampling[2].segments.len(), 50);

        // Verify segments are shuffled deterministically with seed
        let batches_with_sampling_2 = compute_segment_batches(
            ComputeSegmentBatchInput {
                segment_ids: &segment_ids,
                statistics: &HashMap::new(),
                id_to_segment_id: &HashMap::new(),
                root_span_id_to_segment_id: &HashMap::new(),
                sort: &None,
                filters: &[],
                partition_all: false,
                sampling: Some(SamplingMode::Seed(12345)), // Same seed
            },
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 4,
            },
        );

        // Should produce the same batches with same seed
        assert_eq!(
            batches_with_sampling[0].segments,
            batches_with_sampling_2[0].segments
        );

        // Test sampling without seed - should still use fixed batch sizes but with random shuffling
        let input_sampling_no_seed = ComputeSegmentBatchInput {
            segment_ids: &segment_ids,
            statistics: &HashMap::new(),
            id_to_segment_id: &HashMap::new(),
            root_span_id_to_segment_id: &HashMap::new(),
            sort: &None,
            filters: &[],
            partition_all: false,
            sampling: Some(SamplingMode::NoSeed),
        };

        let batches_sampling_no_seed = compute_segment_batches(
            input_sampling_no_seed,
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 4,
            },
        );

        // Should still batch in sizes of 100 even without seed
        assert_eq!(batches_sampling_no_seed.len(), 3);
        assert_eq!(batches_sampling_no_seed[0].segments.len(), 100);
        assert_eq!(batches_sampling_no_seed[1].segments.len(), 100);
        assert_eq!(batches_sampling_no_seed[2].segments.len(), 50);
    }

    #[test]
    fn test_sampling_with_sort_behavior() {
        // Create segments with statistics for sorting
        let segment_ids = (0..250).map(|_| Uuid::new_v4()).collect::<Vec<_>>();
        let mut statistics = HashMap::new();
        let mut field_stats = HashMap::new();

        // Give each segment different stats so they can be sorted
        for (i, id) in segment_ids.iter().enumerate() {
            field_stats.insert(
                *id,
                SegmentFieldStatistics::new((i * 10) as u64, ((i + 1) * 10) as u64).unwrap(),
            );
        }
        statistics.insert("sort_field".to_string(), field_stats);

        let sort_spec = SegmentBatchingSortSpec {
            field: "sort_field".to_string(),
            descending: false,
        };

        // Test sampling with sort - should use sort-based partitioning but with 100-segment batches
        let input_sampling_with_sort = ComputeSegmentBatchInput {
            segment_ids: &segment_ids,
            statistics: &statistics,
            id_to_segment_id: &HashMap::new(),
            root_span_id_to_segment_id: &HashMap::new(),
            sort: &Some(sort_spec),
            filters: &[],
            partition_all: false,
            sampling: Some(SamplingMode::Seed(12345)),
        };

        let batches_sampling_with_sort = compute_segment_batches(
            input_sampling_with_sort,
            ComputeSegmentBatchOpts {
                initial_segment_batch_size: 4, // This should be ignored due to sampling
            },
        );

        // With sampling, we should get more batches due to the smaller initial batch size (100)
        // The sort partitioning should still work, but start with smaller batches
        // Note: The partition_segments_by_sort_key function may create larger batches when
        // segments have overlapping ranges, but it should start with our smaller initial size

        // Verify that we're using the smaller batch sizing by checking we get more batches
        // than we would with the original larger initial batch size
        assert!(batches_sampling_with_sort.len() >= 3);

        // At least some batches should be reasonably sized (though overlapping segments
        // might cause some batches to be larger)
        let small_batches = batches_sampling_with_sort
            .iter()
            .filter(|batch| batch.segments.len() <= 100)
            .count();
        assert!(small_batches > 0);
    }
}
