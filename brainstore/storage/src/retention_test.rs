use once_cell::sync::Lazy;
use serde_json::json;
use std::{
    collections::{HashMap, HashSet},
    future::Future,
    ops::RangeInclusive,
    path::Path,
    sync::Arc,
};
use tantivy::columnar::MonotonicallyMappableToU64;

use async_util::await_spawn_blocking;
use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Duration, Utc},
    itertools::iproduct,
    schema::{BaseOptions, Field, Schema, TantivyField, TantivyType, TextOptions},
    system_types::{FullObjectId, FullObjectIdOwned, FullRowIdOwned, ObjectId, ObjectType},
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    basic_test_fixture::{BasicTestFixture, ValidateVacuumArgs},
    clear_compacted_index::{clear_compacted_index, ClearCompactedIndexInput},
    config_with_store::{url_to_global_store, ConfigWithStore, ConfigWithStoreOpts},
    directory::cached_directory::FileCacheOpts,
    global_store::{
        DeleteFromSegmentStats, SegmentFieldStatistics, SegmentMetadataUpdate,
        SegmentWalEntriesXactIdStatistic, TestingOnlyBackfillBrainstoreObjectAtom,
    },
    global_store_test::{get_postgres_global_store_migration, POSTGRES_EXTERNAL_MIGRATION},
    index_document::{make_full_schema, IndexDocument},
    index_wal_reader::{IndexWalReader, IndexWalReaderInput},
    index_wal_reader_test_util::get_reader_full_docs,
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    postgres_wal::{PostgresWAL, PostgresWalStreamBounded, PostgresWalStreamOpts},
    process_wal::{
        compact_segment_wal, process_object_wal, CompactSegmentWalInput,
        CompactSegmentWalOptionalInput, ProcessObjectWalInput, FIELD_STATISTICS_FIELD_NAMES,
        XACT_ID_FIELD,
    },
    retention::{
        delete_from_segments_up_to_xact_id, DeleteFromSegmentsInput,
        DeleteFromSegmentsOptionalInput, DeleteFromSegmentsOptions,
    },
    tantivy_index::TantivyIndexScope,
    tantivy_index_test_util::read_all_documents,
    tantivy_index_wrapper::{ReadWriteTantivyIndexWrapper, ReadonlyTantivyIndexWrapper},
    tantivy_index_wrapper_test_util::make_readonly_tantivy_index_wrapper,
    test_util::{collect_wal_stream, make_compacted_wal_entries, PostgresContainer},
    vacuum_test_util::{
        default_vacuum_index_full_opts_for_testing,
        default_vacuum_segment_wal_full_opts_for_testing, force_vacuum_then_validate_index_wal,
        vacuum_segment_wal_for_testing, VacuumSegmentWalForTestingInput,
        VacuumThenValidateIndexWalArgs,
    },
    wal::{
        wal_stream, DeleteUpToXactIdInput, DeleteUpToXactIdOptions, WALScope, Wal,
        WalMetadataStreamOptionalInput,
    },
    wal_entry::WalEntry,
    wal_test_util::insert_object_atoms_into_wal,
};

pub struct TestFixture {
    pub basic_fixture: BasicTestFixture,
    pub _container: Option<PostgresContainer>,
}

impl TestFixture {
    pub async fn new(use_postgres_global_store: bool) -> Self {
        let mut basic_fixture = BasicTestFixture::new();

        let container = if use_postgres_global_store {
            let container = PostgresContainer::new().await;
            container
                .run_migration(POSTGRES_EXTERNAL_MIGRATION)
                .await
                .unwrap();
            container
                .run_migration(&get_postgres_global_store_migration())
                .await
                .unwrap();
            Some(container)
        } else {
            None
        };

        if use_postgres_global_store {
            let connection_url = container.as_ref().unwrap().connection_url.clone();
            basic_fixture.tmp_dir_config.storage_config.metadata_uri = connection_url.clone();
            let (global_store, _) = url_to_global_store(
                &connection_url,
                FileCacheOpts::default(),
                basic_fixture.tmp_dir_config.config.locks_manager.clone(),
            )
            .unwrap();
            basic_fixture.tmp_dir_config.config.global_store = global_store;
        }

        Self {
            basic_fixture,
            _container: container,
        }
    }

    /// Sets the WAL in the config (and store) to be a PostgresWalStreamBounded.
    pub async fn new_with_postgres_wal(use_logs2: bool) -> Self {
        // Set up the postgres container
        let mut fixture = Self::new(true).await;
        let container = fixture._container.as_ref().unwrap();
        let postgres_wal_uri = container.connection_url.clone();

        // Mutate the storage config to use the postgres WAL
        let mut storage_config = fixture.basic_fixture.tmp_dir_config.storage_config.clone();
        storage_config.wal_uri = postgres_wal_uri.clone();

        // HACK: recreate the ConfigWithStore with updated storage config
        let mut config = ConfigWithStore::from_config(
            storage_config.clone(),
            ConfigWithStoreOpts {
                file_cache_opts: FileCacheOpts::default(),
                ..Default::default()
            },
        )
        .unwrap();

        // Wrap the PostgresWAL with PostgresWalStreamBounded to make reads more convenient
        if config.wal.as_ref().downcast_ref::<PostgresWAL>().is_none() {
            panic!("Expected PostgresWAL but got a different WAL type");
        }

        config.wal = Arc::new(PostgresWalStreamBounded::new(
            config.wal,
            PostgresWalStreamOpts {
                read_logs2: Some(use_logs2),
                ..Default::default()
            },
        ));
        fixture.basic_fixture.tmp_dir_config.config = config;

        fixture
    }

    pub fn make_full_schema(&self) -> Schema {
        make_full_schema(&basic_schema()).unwrap()
    }

    pub fn config(&self) -> &ConfigWithStore {
        &self.basic_fixture.tmp_dir_config.config
    }

    fn index_prefix(&self) -> &Path {
        self.config().index.prefix.as_path()
    }

    pub fn compact_wal_input(&self, segment_id: Uuid) -> CompactSegmentWalInput {
        CompactSegmentWalInput {
            segment_id,
            index_store: self.config().index.clone(),
            schema: self.make_full_schema(),
            global_store: self.config().global_store.clone(),
            locks_manager: self.config().locks_manager.clone(),
        }
    }

    pub fn make_segment_wal(&self) -> ObjectAndGlobalStoreWal {
        ObjectAndGlobalStoreWal {
            object_store: self.config().index.store.clone(),
            global_store: self.config().global_store.clone(),
            directory: self.config().index.directory.clone(),
            store_prefix: self.index_prefix().to_path_buf(),
            store_type: self.config().index.store_type,
        }
    }

    pub async fn read_segment_wal_entries(
        &self,
        segment_id: Uuid,
    ) -> Vec<(TransactionId, Vec<WalEntry>)> {
        let segment_wal = self.make_segment_wal();
        collect_wal_stream(wal_stream(
            segment_wal
                .wal_metadata_stream(WALScope::Segment(segment_id), Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap()
    }

    pub async fn write_wal_to_segment(&self, segment_id: Uuid, entries: Vec<WalEntry>) {
        let segment_wal = self.make_segment_wal();
        segment_wal
            .insert(WALScope::Segment(segment_id), entries)
            .await
            .unwrap();
    }

    pub async fn read_segment_docs(
        &self,
        segment_id: Uuid,
    ) -> HashMap<FullRowIdOwned, IndexDocument> {
        let readonly_tantivy_index_wrapper = make_readonly_tantivy_index_wrapper(
            self.config().global_store.clone(),
            &self.config().index,
            self.make_full_schema(),
            &[segment_id],
        )
        .await
        .unwrap();

        self.read_segment_docs_with_index_wrapper(readonly_tantivy_index_wrapper)
            .await
    }

    pub async fn read_segment_docs_with_index_wrapper(
        &self,
        tantivy_index_wrapper: ReadonlyTantivyIndexWrapper,
    ) -> HashMap<FullRowIdOwned, IndexDocument> {
        await_spawn_blocking!(move || {
            let reader = tantivy_index_wrapper.make_reader_blocking().unwrap();
            let tantivy_docs = read_all_documents(&reader);
            let writable_tantivy_schema = tantivy_index_wrapper.writable_schema;
            let schema_invert_fields = &writable_tantivy_schema.invert_fields;
            let mut docs: HashMap<FullRowIdOwned, IndexDocument> = HashMap::new();
            for (_, doc) in tantivy_docs {
                let index_doc = IndexDocument::from_tantivy_document(&doc, schema_invert_fields)
                    .unwrap()
                    .to_sanitized()
                    .unwrap();
                let row_id = index_doc.wal_entry.full_row_id().to_owned();
                if let Some(dup) = docs.insert(row_id, index_doc) {
                    panic!("Duplicate row id {:?}", dup.wal_entry.full_row_id());
                }
            }
            docs
        })
        .unwrap()
        .unwrap()
    }

    pub async fn initialize_segment_metadata(&self, segment_id: Uuid) {
        self.initialize_segment_metadata_in_object(FullObjectId::default(), segment_id)
            .await;
    }

    pub async fn initialize_segment_metadata_in_object<'a>(
        &self,
        object_id: FullObjectId<'a>,
        segment_id: Uuid,
    ) {
        self.config()
            .global_store
            .update_segment_ids(&[(object_id, &[segment_id], &[])])
            .await
            .unwrap();
        self.config()
            .global_store
            .upsert_segment_metadatas(
                vec![(segment_id, SegmentMetadataUpdate::default())]
                    .into_iter()
                    .collect(),
            )
            .await
            .unwrap();
    }

    pub async fn read_index_wal_entries(
        &self,
        object_ids: &[FullObjectIdOwned],
    ) -> HashMap<String, IndexDocument> {
        let reader = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: &self.basic_fixture.tmp_dir_config.config,
                full_schema: self.make_full_schema(),
                object_ids,
                filters: &[],
                sort: &None,
                partition_all: false,
                sampling: None,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        let entries = get_reader_full_docs(&reader)
            .await
            .into_iter()
            .map(|(key, doc)| (key, doc.to_sanitized().unwrap()))
            .collect();
        entries
    }

    /// Delete entries up to the given xact_id for a segment from both WAL and compacted index
    pub async fn delete_up_to_xact_id(&self, segment_id: Uuid, xact_id: TransactionId) -> u64 {
        let stats = delete_from_segments_up_to_xact_id(
            DeleteFromSegmentsInput {
                segment_ids: &[segment_id],
                min_retained_xact_id: xact_id,
                index_store: &self.config().index,
                schema: &self.make_full_schema(),
                global_store: self.config().global_store.clone(),
                locks_manager: self.config().locks_manager.clone(),
                dry_run: false,
            },
            DeleteFromSegmentsOptionalInput::default(),
            DeleteFromSegmentsOptions::default(),
        )
        .await
        .unwrap();

        stats.wal_stats.num_deleted_wal_entries
    }
}

async fn delete_from_segments_for_testing<'a>(
    input: DeleteFromSegmentsInput<'a>,
) -> DeleteFromSegmentStats {
    if input.dry_run {
        return delete_from_segments_up_to_xact_id(
            input,
            DeleteFromSegmentsOptionalInput::default(),
            DeleteFromSegmentsOptions::default(),
        )
        .await
        .unwrap();
    }

    let mut dry_run_input = input.clone();
    dry_run_input.dry_run = true;
    let dry_run_output = delete_from_segments_up_to_xact_id(
        dry_run_input,
        DeleteFromSegmentsOptionalInput::default(),
        DeleteFromSegmentsOptions::default(),
    )
    .await
    .unwrap();

    let output = delete_from_segments_up_to_xact_id(
        input,
        DeleteFromSegmentsOptionalInput::default(),
        DeleteFromSegmentsOptions::default(),
    )
    .await
    .unwrap();

    assert_eq!(
        dry_run_output.wal_stats.planned_num_deleted_wal_entries,
        output.wal_stats.planned_num_deleted_wal_entries,
        "planned_num_deleted_wal_entries mismatch between dry run and actual run"
    );
    assert_eq!(
        dry_run_output.index_stats.planned_num_deleted_index_docs,
        output.index_stats.planned_num_deleted_index_docs,
        "planned_num_deleted_index_docs mismatch between dry run and actual run"
    );
    assert_eq!(
        dry_run_output.wal_stats.num_deleted_wal_entries, 0,
        "dry run's num_deleted_wal_entries should be 0"
    );
    assert_eq!(
        dry_run_output.index_stats.num_deleted_index_docs, 0,
        "dry run's num_deleted_index_docs should be 0"
    );
    assert_eq!(
        dry_run_output.index_stats.num_write_locks, 0,
        "dry run should not take any write locks"
    );
    assert_eq!(
        output.wal_stats.planned_num_deleted_wal_entries, output.wal_stats.num_deleted_wal_entries,
        "mismatch between planned and actual number of deleted WAL entries"
    );
    assert_eq!(
        output.index_stats.planned_num_deleted_index_docs,
        output.index_stats.num_deleted_index_docs,
        "mismatch between planned and actual number of deleted index docs"
    );

    output
}

fn basic_schema() -> Schema {
    let text_options = TextOptions {
        stored: true,
        fast: false,
        tokenize: false,
    };
    let base_options = BaseOptions {
        stored: true,
        fast: false,
    };

    Schema::new(
        "test".to_string(),
        vec![
            Field {
                name: "field1".to_string(),
                tantivy: vec![TantivyField {
                    name: "field1".to_string(),
                    field_ts: 1,
                    field_type: TantivyType::Str(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field2".to_string(),
                tantivy: vec![TantivyField {
                    name: "field2".to_string(),
                    field_ts: 2,
                    field_type: TantivyType::I64(base_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field3".to_string(),
                tantivy: vec![TantivyField {
                    name: "field3".to_string(),
                    field_ts: 3,
                    field_type: TantivyType::Json(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
        ],
        Default::default(),
    )
    .unwrap()
}

fn default_delete_up_to_xact_id_options_for_testing() -> DeleteUpToXactIdOptions {
    DeleteUpToXactIdOptions {
        batch_size: 100,
        max_num_rows: 1000,
        deletion_log_batch_size: 1000,
    }
}

pub async fn run_test_with_global_stores<F, Fut>(test_fn: F) -> Result<()>
where
    F: Fn(bool) -> Fut + Copy,
    Fut: Future<Output = Result<()>>,
{
    for use_postgres_global_store in [false, true] {
        test_fn(use_postgres_global_store).await?;
    }
    Ok(())
}

#[tokio::test]
async fn test_global_store_delete_from_segment_wal() -> Result<()> {
    run_test_with_global_stores(test_global_store_delete_from_segment_wal_inner).await
}

async fn test_global_store_delete_from_segment_wal_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    let global_store = fixture.config().global_store.clone();

    // Delete WAL entries prior to xact_id 1.
    global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(1))
        .await?;

    // Verify only entries with xact_id >= 1 remain.
    let stats = global_store
        .query_segment_wal_entries_xact_id_statistic(
            &[segment_id],
            SegmentWalEntriesXactIdStatistic::Min,
            None,
        )
        .await?;

    assert_eq!(stats[0], Some(TransactionId(1)));

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segment_wal() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segment_wal_inner).await
}

async fn test_delete_from_segment_wal_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    // Write some test WAL entries with different transaction IDs.
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row3".to_string(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    // Delete entries with xact_id < 2.
    delete_from_segments_up_to_xact_id(
        DeleteFromSegmentsInput {
            segment_ids: &[segment_id],
            index_store: &fixture.config().index,
            schema: &fixture.make_full_schema(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: fixture.config().locks_manager.clone(),
            min_retained_xact_id: TransactionId(2),
            dry_run: false,
        },
        DeleteFromSegmentsOptionalInput::default(),
        DeleteFromSegmentsOptions::default(),
    )
    .await?;

    // Verify that only entries with xact_id >= 2 remain.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(2));

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_global_store() -> Result<()> {
    run_test_with_global_stores(test_delete_from_global_store_inner).await
}

async fn test_delete_from_global_store_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    let global_store = fixture.config().global_store.clone();

    // Delete entries prior to xact_id 1.
    global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(1))
        .await?;

    // Verify only entries with xact_id >= 1 remain.
    let stats = global_store
        .query_segment_wal_entries_xact_id_statistic(
            &[segment_id],
            SegmentWalEntriesXactIdStatistic::Min,
            None,
        )
        .await?;

    assert_eq!(stats[0], Some(TransactionId(1)));

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_basic() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_basic_inner).await
}

async fn test_delete_from_segments_basic_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("a"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field3".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("c"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field3".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(2),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("c"))].into_iter().collect(),
            ..Default::default()
        },
    ]);

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Verify WAL deletion.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 2);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row2");
    assert_eq!(remaining_entries[0].1[0].data["field3"], json!("b"));
    assert_eq!(remaining_entries[1].0, TransactionId(2));
    assert_eq!(remaining_entries[1].1.len(), 1);
    assert_eq!(remaining_entries[1].1[0].id, "row1");
    assert_eq!(remaining_entries[1].1[0].data["field1"], json!("c"));

    // Verify index deletion.
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_with_dry_run() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_with_dry_run_inner).await
}

async fn test_delete_from_segments_with_dry_run_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("a"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("a"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
    ]);

    // Verify that nothing is deleted if we do a dry run.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: true,
    })
    .await;
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 2);
    assert_eq!(remaining_entries[0].0, TransactionId(0));
    assert_eq!(remaining_entries[0].1[0].id, "row1");
    assert_eq!(remaining_entries[1].0, TransactionId(1));
    assert_eq!(remaining_entries[1].1[0].id, "row2");
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Verify only entries with xact_id >= 1 remain in both WAL and index.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row2");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("b"));
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(
        docs.values().next().unwrap().wal_entry._xact_id,
        TransactionId(1)
    );

    // Run another compaction immediately after deletion and check again.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row2");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("b"));
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(
        docs.values().next().unwrap().wal_entry._xact_id,
        TransactionId(1)
    );

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: None,
            schema: Some(fixture.make_full_schema()),
        })
        .await;
    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_with_interleaved_updates() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_with_interleaved_updates_inner).await
}

async fn test_delete_from_segments_with_interleaved_updates_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![WalEntry {
        _xact_id: TransactionId(0),
        id: "row1".to_string(),
        data: [("field1".to_string(), json!("v1"))].into_iter().collect(),
        ..Default::default()
    }];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("v2"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("v1"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Run compaction again.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Verify that the first row is deleted entirely since it was created
    // in a deleted transaction. The second row should persist.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 2);
    assert_eq!(remaining_entries[0].1[0].id, "row1");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("v2"));
    assert_eq!(remaining_entries[0].1[1].id, "row2");
    assert_eq!(remaining_entries[0].1[1].data["field1"], json!("v1"));

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("v2"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("v1"))].into_iter().collect(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(&docs, &expected_docs);

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_with_multiple_inserts() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_with_multiple_inserts_inner).await
}

async fn test_delete_from_segments_with_multiple_inserts_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    // Write row1 and row2 in the first insert.
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            // data: [("value".to_string(), json!("v1"))].into_iter().collect(),
            data: json!({
                "field1": "foo",
                "field2": 42,
                "field3": json!({ "input": "kiwi" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row2".to_string(),
            // data: [("value".to_string(), json!("v1"))].into_iter().collect(),
            data: json!({ "field1": "b" }).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    // Update row1 and add row3 in the second insert.
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            _is_merge: Some(true),
            data: json!({
                "field1": "bar",
                "field3": json!({ "output": "orange" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row3".to_string(),
            _is_merge: Some(true),
            data: json!({ "field1": "d" }).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2);
    assert_eq!(wal_entries[0].0, TransactionId(0));
    assert_eq!(wal_entries[0].1.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row1");
    assert_eq!(wal_entries[0].1[1].id, "row2");
    assert_eq!(wal_entries[1].0, TransactionId(1));
    assert_eq!(wal_entries[1].1.len(), 2);
    assert_eq!(wal_entries[1].1[0].id, "row1");
    assert_eq!(wal_entries[1].1[1].id, "row3");

    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 3);
    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [
                ("field1".to_string(), json!("bar")),
                ("field2".to_string(), json!(42)),
                (
                    "field3".to_string(),
                    json!({ "input": "kiwi", "output": "orange" }),
                ),
            ]
            .into_iter()
            .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row3".to_string(),
            data: [("field1".to_string(), json!("d"))].into_iter().collect(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    // Verify only row1 remains, since row2 only existed in xact_id 0.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 1);
    assert_eq!(wal_entries[0].0, TransactionId(1));
    assert_eq!(wal_entries[0].1.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row1");
    assert_eq!(wal_entries[0].1[1].id, "row3");

    // row2 should be gone, but row1 was compacted after the deletion point
    // so it should still exist in the index.
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [
                ("field1".to_string(), json!("bar")),
                ("field2".to_string(), json!(42)),
                (
                    "field3".to_string(),
                    json!({ "input": "kiwi", "output": "orange" }),
                ),
            ]
            .into_iter()
            .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row3".to_string(),
            data: [("field1".to_string(), json!("d"))].into_iter().collect(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(&docs, &expected_docs);

    // Rerun compaction and verify row1 is still gone.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete WAL entries up to xact_id 2.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(2),
        dry_run: false,
    })
    .await;

    // Verify that the WAL and index are both empty.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 0);
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 0);

    // Rerun compaction and verify that the index is still empty.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 0);

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_then_reprocess_object_wal() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_then_reprocess_object_wal_inner).await
}

async fn test_delete_from_segments_then_reprocess_object_wal_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("zoo"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("pie"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Verify both rows are present in WAL and index before deletion.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row1");
    assert_eq!(wal_entries[0].1[0].data["field1"], json!("zoo"));
    assert_eq!(wal_entries[1].1[0].id, "row2");
    assert_eq!(wal_entries[1].1[0].data["field1"], json!("pie"));

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert!(docs.values().any(|doc| doc.wal_entry.id == "row1"));
    assert!(docs.values().any(|doc| doc.wal_entry.id == "row2"));

    // Run deletes up to xact_id 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Verify only row2 remains after deletion and compaction.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 1);
    assert_eq!(wal_entries[0].1[0].id, "row2");

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(docs.values().next().unwrap().wal_entry.id, "row2");

    // Reprocess the WAL. Since processing starts at last_processed_xact_id by
    // default, we won't reintroduce the entries we just deleted.
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Re-compact and verify row1 is still gone.
    // We are testing here that the tantivy meta was updated correctly during the
    // delete operation, since if it wasn't, re-compaction would flash the index
    // to the previous snapshot, clobbering the delete we just ran.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            start_xact_id: Some(TransactionId(0)),
            ..Default::default()
        },
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 1);
    assert_eq!(wal_entries[0].1[0].id, "row2");

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(docs.values().next().unwrap().wal_entry.id, "row2");

    Ok(())
}

static FULL_OBJECT_ID0: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj0").unwrap(),
});

static FULL_OBJECT_ID1: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj1").unwrap(),
});

static FULL_OBJECT_ID2: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj2").unwrap(),
});

fn basic_wal_entries(full_object_id: FullObjectId) -> Vec<WalEntry> {
    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(0),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            _is_merge: Some(true),
            id: "row0".to_string(),
            data: json!({
                "field1": "gop",
                "field2": 42,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(0),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: "row1".to_string(),
            data: json!({
                "field1": "foo",
                "field3": json!({ "input": "bar" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(3000),
            _is_merge: Some(true),
            id: "row1".to_string(),
            data: json!({
                "field1": "grue",
                "field2": 99,
                "field3": json!({ "output": "baz" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            _is_merge: Some(true),
            id: "row2".to_string(),
            root_span_id: "r2".to_string(),
            data: json!({
                "field3": json!({ "metadata": "yes" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

fn basic_wal_entries_compacted(
    object_id: FullObjectId,
    deletion_state: &DeletionState,
) -> HashMap<FullRowIdOwned, IndexDocument> {
    let docs = match deletion_state {
        DeletionState::NotDeleted => vec![
            WalEntry {
                _pagination_key: PaginationKey(0),
                _xact_id: TransactionId(0),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(1000),
                id: "row0".to_string(),
                data: json!({
                    "field1": "gop",
                    "field2": 42,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(1),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(2000),
                id: "row1".to_string(),
                data: json!({
                    "field1": "grue",
                    "field2": 99,
                    "field3": json!({ "input": "bar", "output": "baz" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row2".to_string(),
                root_span_id: "r2".to_string(),
                data: json!({
                    "field3": json!({ "metadata": "yes" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
        DeletionState::DeletedBeforeCompaction => vec![
            WalEntry {
                _pagination_key: PaginationKey(2),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(3000),
                id: "row1".to_string(),
                data: json!({
                    "field1": "grue",
                    "field2": 99,
                    "field3": json!({ "output": "baz" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row2".to_string(),
                root_span_id: "r2".to_string(),
                data: json!({
                    "field3": json!({ "metadata": "yes" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
        DeletionState::DeletedAfterCompaction => vec![
            WalEntry {
                _pagination_key: PaginationKey(1),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(2000),
                id: "row1".to_string(),
                data: json!({
                    "field1": "grue",
                    "field2": 99,
                    "field3": json!({ "input": "bar", "output": "baz" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row2".to_string(),
                root_span_id: "r2".to_string(),
                data: json!({
                    "field3": json!({ "metadata": "yes" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
    };
    make_compacted_wal_entries(docs)
}

#[derive(Clone)]
struct SegmentArgs<'a> {
    fixture: &'a TestFixture,
    object_id: FullObjectId<'a>,
    segment_id: Uuid,
}

#[derive(Clone)]
struct CheckSegmentWalArgs<'a> {
    segment_args: &'a SegmentArgs<'a>,
    deleted: bool,
    compacted: bool,
}

enum DeletionState {
    NotDeleted,
    DeletedBeforeCompaction,
    DeletedAfterCompaction,
}

#[derive(Clone)]
struct CheckSegmentIndexArgs<'a> {
    segment_args: &'a SegmentArgs<'a>,
    deletion_state: &'a DeletionState,
    xact_id_range: RangeInclusive<TransactionId>,
}

async fn check_segment_wal(args: &CheckSegmentWalArgs<'_>) {
    let fixture = args.segment_args.fixture;
    let segment_id = args.segment_args.segment_id;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;

    if !args.deleted {
        assert_eq!(wal_entries.len(), 2);

        assert_eq!(wal_entries[0].0, TransactionId(0));
        assert_eq!(wal_entries[0].1.len(), 2);
        assert_eq!(wal_entries[0].1[0].id, "row0");
        assert_eq!(wal_entries[0].1[0].data["field1"], json!("gop"));
        assert_eq!(wal_entries[0].1[0].data["field2"], json!(42));
        assert_eq!(wal_entries[0].1[1].id, "row1");
        assert_eq!(wal_entries[0].1[1].data["field1"], json!("foo"));
        assert_eq!(wal_entries[0].1[1].data["field3"], json!({"input": "bar"}));

        assert_eq!(wal_entries[1].0, TransactionId(1));
        assert_eq!(wal_entries[1].1.len(), 2);
        assert_eq!(wal_entries[1].1[0].id, "row1");
        assert_eq!(wal_entries[1].1[0].data["field1"], json!("grue"));
        assert_eq!(wal_entries[1].1[0].data["field2"], json!(99));
        assert_eq!(wal_entries[1].1[0].data["field3"], json!({"output": "baz"}));
        assert_eq!(wal_entries[1].1[1].id, "row2");
        assert_eq!(
            wal_entries[1].1[1].data["field3"],
            json!({"metadata": "yes"})
        );
    } else {
        assert_eq!(wal_entries.len(), 1);

        // The WAL entries for xact_id 0 should be gone.
        assert_eq!(wal_entries[0].0, TransactionId(1));
        assert_eq!(wal_entries[0].1.len(), 2);
        assert_eq!(wal_entries[0].1[0].id, "row1");
        assert_eq!(wal_entries[0].1[0].data["field1"], json!("grue"));
        assert_eq!(wal_entries[0].1[0].data["field2"], json!(99));
        assert_eq!(wal_entries[0].1[0].data["field3"], json!({"output": "baz"}));
        assert_eq!(wal_entries[0].1[1].id, "row2");
        assert_eq!(
            wal_entries[0].1[1].data["field3"],
            json!({"metadata": "yes"})
        );
    }

    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(if args.deleted { 1 } else { 0 }))
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(!args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(!args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
}

async fn check_segment_index(args: &CheckSegmentIndexArgs<'_>) {
    let fixture = args.segment_args.fixture;
    let object_id = args.segment_args.object_id;
    let segment_id = args.segment_args.segment_id;

    let actual_docs = fixture.read_segment_docs(segment_id).await;
    let expected_docs = basic_wal_entries_compacted(object_id, args.deletion_state);
    assert_hashmap_eq(&actual_docs, &expected_docs);

    // Check the index WAL entries, which should also match the expected
    // compacted entries.
    let object_ids_owned = &[object_id.to_owned()];
    let actual_index_wal_entries = fixture.read_index_wal_entries(object_ids_owned).await;

    let expected_index_wal_entries = basic_wal_entries_compacted(object_id, args.deletion_state);

    // Convert to a map keyed by row ID.
    let expected_index_wal_entries_by_id: HashMap<String, IndexDocument> =
        expected_index_wal_entries
            .into_iter()
            .map(|(full_row_id, doc)| (full_row_id.id, doc))
            .collect();

    assert_hashmap_eq(&actual_index_wal_entries, &expected_index_wal_entries_by_id);

    let min_pagination_key = match args.deletion_state {
        DeletionState::NotDeleted => 0,
        DeletionState::DeletedBeforeCompaction => 2,
        DeletionState::DeletedAfterCompaction => 1,
    };
    let min_created = match args.deletion_state {
        DeletionState::NotDeleted => 1000_i64,
        DeletionState::DeletedBeforeCompaction => 3000_i64,
        DeletionState::DeletedAfterCompaction => 2000_i64,
    };

    if matches!(args.deletion_state, DeletionState::DeletedAfterCompaction) {
        // NOTE(austin): For some reason, the tantivy index does not always keep
        // exact min/max field statistics after a delete. In particular, there is
        // a discrepancy between the tantivy docs and the field statistics for
        // the "delete after compaction" case.
        // Re-enable this check once we fix this issue.
        log::warn!("Skipping field statistics check for DeletedAfterCompaction case");
        return;
    }

    let field_statistics = fixture
        .config()
        .global_store
        .query_field_statistics(&[segment_id], &FIELD_STATISTICS_FIELD_NAMES)
        .await
        .unwrap();
    let expected_field_statistics = [
        (
            "_pagination_key".to_string(),
            SegmentFieldStatistics::new(min_pagination_key, 3).unwrap(),
        ),
        (
            "created".to_string(),
            SegmentFieldStatistics::new(
                MonotonicallyMappableToU64::to_u64(min_created),
                MonotonicallyMappableToU64::to_u64(4000_i64),
            )
            .unwrap(),
        ),
        (
            XACT_ID_FIELD.to_string(),
            SegmentFieldStatistics::new(
                MonotonicallyMappableToU64::to_u64(args.xact_id_range.start().0),
                MonotonicallyMappableToU64::to_u64(args.xact_id_range.end().0),
            )
            .unwrap(),
        ),
    ]
    .into_iter()
    .collect();
    assert_eq!(field_statistics[&segment_id], expected_field_statistics);
}

#[tokio::test]
async fn test_delete_from_multiple_segments() -> Result<()> {
    run_test_with_global_stores(test_delete_from_multiple_segments_inner).await
}

async fn test_delete_from_multiple_segments_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    // Put each segment into a separate object so we can retrieve entries
    // by object_id later.
    let segment_id0 = Uuid::new_v4();
    let segment_id1 = Uuid::new_v4();
    let segment_id2 = Uuid::new_v4();
    fixture
        .initialize_segment_metadata_in_object(*FULL_OBJECT_ID0, segment_id0)
        .await;
    fixture
        .initialize_segment_metadata_in_object(*FULL_OBJECT_ID1, segment_id1)
        .await;
    fixture
        .initialize_segment_metadata_in_object(*FULL_OBJECT_ID2, segment_id2)
        .await;
    fixture
        .write_wal_to_segment(segment_id0, basic_wal_entries(*FULL_OBJECT_ID0))
        .await;
    fixture
        .write_wal_to_segment(segment_id1, basic_wal_entries(*FULL_OBJECT_ID1))
        .await;
    fixture
        .write_wal_to_segment(segment_id2, basic_wal_entries(*FULL_OBJECT_ID2))
        .await;

    let segment_id_to_segment_args = HashMap::from([
        (
            segment_id0,
            SegmentArgs {
                fixture: &fixture,
                object_id: *FULL_OBJECT_ID0,
                segment_id: segment_id0,
            },
        ),
        (
            segment_id1,
            SegmentArgs {
                fixture: &fixture,
                object_id: *FULL_OBJECT_ID1,
                segment_id: segment_id1,
            },
        ),
        (
            segment_id2,
            SegmentArgs {
                fixture: &fixture,
                object_id: *FULL_OBJECT_ID2,
                segment_id: segment_id2,
            },
        ),
    ]);

    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deleted: false,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deleted: false,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deleted: false,
        compacted: false,
    })
    .await;

    // Before compacting, delete from segment_id0 and verify the result.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id0],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deleted: true,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deleted: false,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deleted: false,
        compacted: false,
    })
    .await;

    // Compact everything and verify that the deleted entries are still gone.
    for &segment_id in &[segment_id0, segment_id1, segment_id2] {
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deleted: true,
        compacted: true,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deleted: false,
        compacted: true,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deleted: false,
        compacted: true,
    })
    .await;

    // Now we can verify that the segment index is in the correct state.
    check_segment_index(&CheckSegmentIndexArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deletion_state: &DeletionState::DeletedBeforeCompaction,
        xact_id_range: TransactionId(1)..=TransactionId(1),
    })
    .await;
    check_segment_index(&CheckSegmentIndexArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deletion_state: &DeletionState::NotDeleted,
        xact_id_range: TransactionId(0)..=TransactionId(1),
    })
    .await;
    check_segment_index(&CheckSegmentIndexArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deletion_state: &DeletionState::NotDeleted,
        xact_id_range: TransactionId(0)..=TransactionId(1),
    })
    .await;

    // Delete from segment_id1 and verify that row0 is gone.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id1],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.clone(),
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // NOTE(austin): I encountered an issue where the tantivy field statistics
    // don't update after running a delete. I tried to solve this by adding the
    // following code that runs garbage collection and a tantivy merge prior to
    // recomputing the statistics, but it didn't seem to work. I'm leaving this
    // code here in case it proves useful for an eventual fix.
    //
    // let merge_opts = MergeOpts {
    //     // target_num_segments: default_target_num_segments(),
    //     target_num_segments: 1,
    //     total_merges: None,
    //     garbage_collect: true,
    //     use_exact_num_merge_policy: true,
    // };
    // let full_schema = make_full_schema(&fixture.schema).unwrap();
    //
    // for segment_id in [segment_id0, segment_id1, segment_id2] {
    //     let index_wrapper = fixture.make_tantivy_index_wrapper(segment_id).await;
    //     let index_writer = index_wrapper.make_writer(&writer_opts).await.unwrap();
    //
    //     index_writer.garbage_collect_files().await.unwrap();
    //
    //     merge_tantivy_segments(
    //         MergeTantivySegmentsInput {
    //             segment_id,
    //             config: fixture.config.clone(),
    //             schema: &full_schema,
    //             dry_run: false,
    //             try_acquire: false,
    //         },
    //         MergeTantivySegmentsOptionalInput::default(),
    //         MergeTantivySegmentsOptions {
    //             merge_opts: merge_opts.clone(),
    //             writer_opts: TantivyIndexWriterOpts::default(),
    //             process_wal_opts: ProcessObjectWalOptions::default(),
    //         },
    //     )
    //     .await?;
    // }

    for _ in 0..2 {
        check_segment_wal(&CheckSegmentWalArgs {
            segment_args: &segment_id_to_segment_args[&segment_id0],
            deleted: true,
            compacted: true,
        })
        .await;
        check_segment_wal(&CheckSegmentWalArgs {
            segment_args: &segment_id_to_segment_args[&segment_id1],
            deleted: true,
            compacted: true,
        })
        .await;
        check_segment_wal(&CheckSegmentWalArgs {
            segment_args: &segment_id_to_segment_args[&segment_id2],
            deleted: false,
            compacted: true,
        })
        .await;

        check_segment_index(&CheckSegmentIndexArgs {
            segment_args: &segment_id_to_segment_args[&segment_id0],
            deletion_state: &DeletionState::DeletedBeforeCompaction,
            xact_id_range: TransactionId(1)..=TransactionId(1),
        })
        .await;
        check_segment_index(&CheckSegmentIndexArgs {
            segment_args: &segment_id_to_segment_args[&segment_id1],
            deletion_state: &DeletionState::DeletedAfterCompaction,
            xact_id_range: TransactionId(1)..=TransactionId(1),
        })
        .await;
        check_segment_index(&CheckSegmentIndexArgs {
            segment_args: &segment_id_to_segment_args[&segment_id2],
            deletion_state: &DeletionState::NotDeleted,
            xact_id_range: TransactionId(0)..=TransactionId(1),
        })
        .await;

        // Recompact and re-check everything just as a sanity check.
        for &segment_id in &[segment_id0, segment_id1, segment_id2] {
            compact_segment_wal(
                fixture.compact_wal_input(segment_id),
                Default::default(),
                Default::default(),
            )
            .await?;
        }
    }

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: None,
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    // Now run deletes all the way up to xact_id 2. This should soft-delete the
    // remaining rows.
    for &segment_id in &[segment_id0, segment_id1, segment_id2] {
        delete_from_segments_for_testing(DeleteFromSegmentsInput {
            segment_ids: &[segment_id],
            index_store: &fixture.config().index,
            schema: &fixture.make_full_schema(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: fixture.config().locks_manager.clone(),
            min_retained_xact_id: TransactionId(2),
            dry_run: false,
        })
        .await;
    }

    let wal_entries = fixture.read_segment_wal_entries(segment_id0).await;
    assert!(wal_entries.is_empty());
    let wal_entries = fixture.read_segment_wal_entries(segment_id1).await;
    assert!(wal_entries.is_empty());
    let wal_entries = fixture.read_segment_wal_entries(segment_id2).await;
    assert!(wal_entries.is_empty());

    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(true)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(true)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );

    for &segment_id in &[segment_id0, segment_id1, segment_id2] {
        assert_hashmap_eq(
            &fixture.read_segment_docs(segment_id).await,
            &HashMap::new(),
        );
        let object_ids_owned = &[segment_id_to_segment_args[&segment_id].object_id.to_owned()];
        assert_hashmap_eq(
            &fixture.read_index_wal_entries(object_ids_owned).await,
            &HashMap::new(),
        );
    }

    vacuum_segment_wal_for_testing(
        VacuumSegmentWalForTestingInput {
            config_with_store: &fixture.config(),
            object_ids: Some(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1, *FULL_OBJECT_ID2]),
        },
        Default::default(),
        default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await;

    for stateless in [true, false] {
        force_vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
            config_with_store: &fixture.config(),
            full_schema: fixture.make_full_schema(),
            object_ids: Some(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1, *FULL_OBJECT_ID2]),
            stateless,
            options: default_vacuum_index_full_opts_for_testing(),
            expected_segment_id_cursor: if stateless {
                Some(Some(
                    *[segment_id0, segment_id1, segment_id2]
                        .iter()
                        .max()
                        .unwrap(),
                ))
            } else {
                None
            },
            dry_run: false,
        })
        .await;
    }

    Ok(())
}

// =============================================================================
// Soft-Delete / Restoration Tests
// =============================================================================

#[tokio::test]
async fn test_soft_delete_restore() -> Result<()> {
    run_test_with_global_stores(test_soft_delete_restore_inner).await
}

async fn test_soft_delete_restore_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    // Write initial rows
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: json!({
                "field1": "og1",
                "field2": 42,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            data: json!({
                "field1": "og2",
                "field2": 3,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    // Delete row 1.
    let deleted_count = fixture
        .delete_up_to_xact_id(segment_id, TransactionId(2))
        .await;
    assert_eq!(deleted_count, 1);

    let visible_entries = fixture
        .config()
        .global_store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(visible_entries.len(), 1);

    // Compact after delete
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);

    // Restore row 1.
    let restored_count = fixture
        .config()
        .global_store
        .restore_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(2))
        .await
        .unwrap();
    assert_eq!(restored_count, 1);

    let visible_entries = fixture
        .config()
        .global_store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(visible_entries.len(), 2);

    clear_compacted_index(ClearCompactedIndexInput {
        segment_id,
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.as_ref(),
    })
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);

    // Now delete both rows.
    let deleted_count = fixture
        .delete_up_to_xact_id(segment_id, TransactionId(3))
        .await;
    assert_eq!(deleted_count, 2);
    // Read segment docs and verify that they are empty.
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 0);

    // Recompact. The rows should not reappear.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 0);

    // Now insert some new rows -- an update to row 2 and a new row.
    let new_wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(3),
            id: "row2".to_string(),
            data: json!({
                "field2": 100,
            })
            .as_object()
            .unwrap()
            .clone(),
            _is_merge: Some(true),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(3),
            id: "row3".to_string(),
            data: json!({
                "field1": "og3",
                "field2": 5,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ];
    fixture
        .write_wal_to_segment(segment_id, new_wal_entries.clone())
        .await;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let segment_docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(segment_docs.len(), 2);

    let expected_compacted_docs = make_compacted_wal_entries(new_wal_entries);
    assert_hashmap_eq(&segment_docs, &expected_compacted_docs);

    // Now restore the original rows.
    let restored_count = fixture
        .config()
        .global_store
        .restore_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(3))
        .await
        .unwrap();
    assert_eq!(restored_count, 2);

    // Clear and recompact.
    clear_compacted_index(ClearCompactedIndexInput {
        segment_id,
        global_store: fixture.config().global_store.clone(),
        locks_manager: fixture.config().locks_manager.as_ref(),
    })
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let restored_wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: json!({
                "field1": "og1",
                "field2": 42,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(3),
            id: "row2".to_string(),
            data: json!({
                "field1": "og2",
                "field2": 100,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(3),
            id: "row3".to_string(),
            data: json!({
                "field1": "og3",
                "field2": 5,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ];
    let restored_compacted_docs = make_compacted_wal_entries(restored_wal_entries);

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 3);
    assert_hashmap_eq(&docs, &restored_compacted_docs);

    Ok(())
}

pub struct PostgresWalRetentionTestArgs {
    pub use_logs2: bool,
    pub object_type: ObjectType,
}

#[tokio::test]
async fn test_postgres_wal_retention_basic() -> Result<()> {
    for (use_logs2, object_type) in iproduct!(
        [false, true],
        [
            ObjectType::ProjectLogs,
            ObjectType::Experiment,
            ObjectType::Dataset
        ]
    ) {
        test_postgres_wal_retention_basic_inner(PostgresWalRetentionTestArgs {
            use_logs2,
            object_type,
        })
        .await?;
    }
    Ok(())
}

async fn test_postgres_wal_retention_basic_inner(args: PostgresWalRetentionTestArgs) -> Result<()> {
    let fixture = TestFixture::new_with_postgres_wal(args.use_logs2).await;

    let global_store = fixture.config().global_store.clone();
    let postgres_wal = fixture.config().wal.clone();

    // Create two different objects; we will only run deletes on the first.
    let project_id_1 = Uuid::new_v4().to_string();
    let object_id_1 = FullObjectId {
        object_type: args.object_type,
        object_id: ObjectId::new(&project_id_1).unwrap(),
    };
    let project_id_2 = Uuid::new_v4().to_string();
    let object_id_2 = FullObjectId {
        object_type: args.object_type,
        object_id: ObjectId::new(&project_id_2).unwrap(),
    };

    // Create test data with timestamps before and after the 30-day retention cutoff.
    let now = Utc::now();
    let old_timestamp = (now - Duration::days(31)).timestamp() as u64; // Old data
    let recent_timestamp = (now - Duration::days(29)).timestamp() as u64; // Recent data

    let old_xact_id = TransactionId::from_timestamp(old_timestamp, 0);
    let recent_xact_id = TransactionId::from_timestamp(recent_timestamp, 0);

    let object_1_entries = vec![
        // Object 1 - old entries (will be deleted)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_1.clone(),
            object_id: object_id_1.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 100,
            xact_id: old_xact_id,
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_1.clone(),
            object_id: object_id_1.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 101,
            xact_id: old_xact_id,
        },
        // Object 1 - recent entries (should be preserved)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_1.clone(),
            object_id: object_id_1.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 200,
            xact_id: recent_xact_id,
        },
    ];
    let object_2_entries = vec![
        // Object 2 - old entries (should not be deleted)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_2.clone(),
            object_id: object_id_2.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 300,
            xact_id: old_xact_id,
        },
        // Object 2 - recent entries (should not be deleted)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_2.clone(),
            object_id: object_id_2.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 400,
            xact_id: recent_xact_id,
        },
    ];
    let all_entries = [object_1_entries.clone(), object_2_entries.clone()].concat();

    // Insert all entries into the Postgres WAL.
    insert_object_atoms_into_wal(
        all_entries,
        vec![],
        postgres_wal.clone(),
        global_store.clone(),
    )
    .await?;

    let object_1_before = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_1, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_1_total_before = object_1_before
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_1_total_before, 3,
        "Object 1 should have 3 entries before delete"
    );
    let object_2_before = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_2, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_2_total_before = object_2_before
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_2_total_before, 2,
        "Object 2 should have 2 entries before delete"
    );

    // If we do a dry run with a 30-day window, nothing should happen.
    let dry_run_result = postgres_wal
        .delete_up_to_xact_id(
            DeleteUpToXactIdInput {
                scope: WALScope::ObjectId(object_id_1, Uuid::nil()),
                min_retained_xact_id: recent_xact_id,
                dry_run: true,
            },
            &default_delete_up_to_xact_id_options_for_testing(),
        )
        .await?;
    assert_eq!(
        dry_run_result.planned_num_deletes, 2,
        "Dry run should find 2 entries that would be deleted"
    );
    assert_eq!(
        dry_run_result.num_deletes, 0,
        "Dry run should not delete any entries"
    );

    // Verify that no entries were deleted.
    let object_1_dry_run = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_1, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_1_total_dry_run = object_1_dry_run
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_1_total_dry_run, 3,
        "Object 1 should have 3 entries after dry run"
    );

    // Now delete entries older than 30 days for the first object.
    let delete_result = postgres_wal
        .delete_up_to_xact_id(
            DeleteUpToXactIdInput {
                scope: WALScope::ObjectId(object_id_1, Uuid::nil()),
                min_retained_xact_id: recent_xact_id,
                dry_run: false,
            },
            &default_delete_up_to_xact_id_options_for_testing(),
        )
        .await?;
    assert_eq!(
        delete_result.num_deletes, 2,
        "Should have deleted exactly 2 old entries from object 1"
    );

    // Verify post-deletion state.
    let object_1_after = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_1, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_1_total_after = object_1_after
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_1_total_after, 1,
        "Object 1 should have only 1 entry after deletion (recent only)"
    );
    let object_2_after = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_2, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_2_total_after = object_2_after
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_2_total_after, 2,
        "Object 2 should be unaffected by object 1's deletion"
    );

    // Verify that object 1's remaining entry is the recent one.
    let object_1_remaining_xact_ids: Vec<_> =
        object_1_after.iter().map(|(xact_id, _)| *xact_id).collect();
    assert_eq!(
        object_1_remaining_xact_ids,
        vec![recent_xact_id],
        "Object 1 should only have recent transaction remaining"
    );

    // Verify that object 2 still has both old and recent entries.
    let mut object_2_xact_ids: Vec<_> =
        object_2_after.iter().map(|(xact_id, _)| *xact_id).collect();
    object_2_xact_ids.sort();
    let mut expected_object_2_xact_ids = vec![old_xact_id, recent_xact_id];
    expected_object_2_xact_ids.sort();
    assert_eq!(
        object_2_xact_ids, expected_object_2_xact_ids,
        "Object 2 should still have both old and recent transactions"
    );

    Ok(())
}

#[tokio::test]
async fn test_postgres_wal_retention_then_backfill() -> Result<()> {
    for (use_logs2, object_type) in iproduct!(
        [false, true],
        [
            ObjectType::ProjectLogs,
            ObjectType::Experiment,
            ObjectType::Dataset
        ]
    ) {
        test_postgres_wal_retention_then_backfill_inner(PostgresWalRetentionTestArgs {
            use_logs2,
            object_type,
        })
        .await?;
    }
    Ok(())
}

async fn test_postgres_wal_retention_then_backfill_inner(
    args: PostgresWalRetentionTestArgs,
) -> Result<()> {
    let fixture = TestFixture::new_with_postgres_wal(args.use_logs2).await;

    let global_store = fixture.config().global_store.clone();
    let postgres_wal = fixture.config().wal.clone();

    let project_id = Uuid::new_v4().to_string();
    let object_id = FullObjectId {
        object_type: args.object_type,
        object_id: ObjectId::new(&project_id).unwrap(),
    };

    // Create test data before and after the 30-day retention cutoff.
    let now = Utc::now();
    let old_timestamp = (now - Duration::days(31)).timestamp() as u64;
    let recent_timestamp = (now - Duration::days(29)).timestamp() as u64;

    let old_xact_id = TransactionId::from_timestamp(old_timestamp, 0);
    let recent_xact_id = TransactionId::from_timestamp(recent_timestamp, 0);

    // 31-day-old objects
    let old_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 100,
            xact_id: old_xact_id,
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 101,
            xact_id: old_xact_id,
        },
    ];
    // 29-day-old objects
    let new_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 200,
            xact_id: recent_xact_id,
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 201,
            xact_id: recent_xact_id,
        },
    ];
    let all_objects = [old_objects.clone(), new_objects.clone()].concat();
    let num_objects = all_objects.len();
    insert_object_atoms_into_wal(
        all_objects,
        vec![],
        postgres_wal.clone(),
        global_store.clone(),
    )
    .await?;
    let entries_before = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let total_entries_before = entries_before
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        total_entries_before, num_objects,
        "Expected to see all inserted WAL entries before retention",
    );
    // Check that we have entries for both expected transaction IDs.
    let old_entries_before = entries_before
        .iter()
        .find(|(xact_id, _)| *xact_id == old_xact_id)
        .map(|(_, entries)| entries.len())
        .unwrap_or(0);
    assert_eq!(
        old_entries_before,
        old_objects.len(),
        "Expected entries for old transaction before deletion",
    );
    let recent_entries_before = entries_before
        .iter()
        .find(|(xact_id, _)| *xact_id == recent_xact_id)
        .map(|(_, entries)| entries.len())
        .unwrap_or(0);
    assert_eq!(
        recent_entries_before,
        new_objects.len(),
        "Expected entries for recent transaction before deletion",
    );

    // Delete from the Postgres WAL up to 30 days ago.
    let min_retained_xact_id =
        TransactionId::from_timestamp((now - Duration::days(30)).timestamp() as u64, 0);
    let wal_delete_result = postgres_wal
        .delete_up_to_xact_id(
            DeleteUpToXactIdInput {
                scope: WALScope::ObjectId(object_id, Uuid::nil()),
                min_retained_xact_id,
                dry_run: false,
            },
            &default_delete_up_to_xact_id_options_for_testing(),
        )
        .await?;
    assert_eq!(
        wal_delete_result.num_deletes,
        old_objects.len() as u64,
        "Expected to delete all old objects",
    );
    let entries_after_delete = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let total_entries_after_delete = entries_after_delete
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        total_entries_after_delete,
        new_objects.len(),
        "Expected only recent entries to remain after deletion"
    );

    let process_result = process_object_wal(
        ProcessObjectWalInput {
            object_id: object_id,
            config: &fixture.config(),
        },
        Default::default(),
        Default::default(),
    )
    .await?;
    for segment_id in &process_result.modified_segment_ids {
        compact_segment_wal(
            fixture.compact_wal_input(*segment_id),
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    // Verify transaction IDs and entry counts after deletion and processing.
    let entries_final = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let total_entries_final = entries_final
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        total_entries_final,
        new_objects.len(),
        "Expected only recent entries to remain after processing and compaction"
    );
    let old_entries_final = entries_final
        .iter()
        .find(|(xact_id, _)| *xact_id == old_xact_id)
        .map(|(_, entries)| entries.len())
        .unwrap_or(0);
    assert_eq!(
        old_entries_final, 0,
        "Expected old transaction to have no entries after processing and compaction"
    );
    let recent_entries_final = entries_final
        .iter()
        .find(|(xact_id, _)| *xact_id == recent_xact_id)
        .map(|(_, entries)| entries.len())
        .unwrap_or(0);
    assert_eq!(
        recent_entries_final,
        new_objects.len(),
        "Expected recent transaction to have all original entries after processing and compaction",
    );

    for segment_id in &process_result.modified_segment_ids {
        // Should have exactly one transaction (recent_xact_id) with entries.
        let segment_wal_entries = fixture.read_segment_wal_entries(*segment_id).await;
        assert_eq!(
            segment_wal_entries.len(),
            1,
            "Expected exactly 1 transaction in segment WAL after retention",
        );
        let (wal_xact_id, wal_entries) = &segment_wal_entries[0];
        assert_eq!(
            *wal_xact_id, recent_xact_id,
            "Expected segment WAL to contain data from recent transaction",
        );
        assert_eq!(
            wal_entries.len(),
            new_objects.len(),
            "Expected segment WAL to have entries from recent transaction",
        );
        for entry in wal_entries {
            assert_eq!(
                entry._xact_id, recent_xact_id,
                "Expected WAL entry to have xact_id {}",
                recent_xact_id.0,
            );
        }
        let segment_docs = fixture.read_segment_docs(*segment_id).await;
        assert_eq!(
            segment_docs.len(),
            new_objects.len(),
            "Expected segment docs to have entries from recent transaction",
        );
        for (_, doc) in &segment_docs {
            assert_eq!(
                doc.wal_entry._xact_id, recent_xact_id,
                "Expected segment document to have xact_id {}",
                recent_xact_id.0,
            );
        }
    }

    Ok(())
}

#[tokio::test]
async fn test_realtime_wal_delete_up_to_xact_id() -> Result<()> {
    let fixture = TestFixture::new(false).await; // Don't need to use the postgres global store here
    let global_store = fixture.config().global_store.clone();
    let realtime_wal = fixture.config().realtime_wal.clone().unwrap();

    // Create two different objects, only one of which will be deleted from.
    let project_id_1 = Uuid::new_v4().to_string();
    let project_id_2 = Uuid::new_v4().to_string();
    let object_id_1 = FullObjectId {
        object_type: ObjectType::ProjectLogs,
        object_id: ObjectId::new(&project_id_1).unwrap(),
    };
    let object_id_2 = FullObjectId {
        object_type: ObjectType::ProjectLogs,
        object_id: ObjectId::new(&project_id_2).unwrap(),
    };

    // Create test data with timestamps before and after the deletion cutoff.
    let now = Utc::now();
    let old_timestamp = (now - Duration::days(61)).timestamp() as u64;
    let recent_timestamp = (now - Duration::days(59)).timestamp() as u64;
    let old_xact_id = TransactionId::from_timestamp(old_timestamp, 0);
    let recent_xact_id = TransactionId::from_timestamp(recent_timestamp, 0);

    // Create WAL entries for both objects. Each object has old and recent data.
    let object_1_entries = vec![
        // Object 1 - old entries (should be deleted).
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_1.clone(),
            object_id: object_id_1.to_owned(),
            is_logs2: false,
            sequence_id: 100,
            xact_id: old_xact_id,
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_1.clone(),
            object_id: object_id_1.to_owned(),
            is_logs2: false,
            sequence_id: 101,
            xact_id: old_xact_id,
        },
        // Object 1 - recent entries (should be preserved).
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_1.clone(),
            object_id: object_id_1.to_owned(),
            is_logs2: false,
            sequence_id: 200,
            xact_id: recent_xact_id,
        },
    ];
    let object_2_entries = vec![
        // Object 2 - old entries (should not be touched by delete).
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_2.clone(),
            object_id: object_id_2.to_owned(),
            is_logs2: false,
            sequence_id: 300,
            xact_id: old_xact_id,
        },
        // Object 2 - recent entries (should not be touched by delete).
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id_2.clone(),
            object_id: object_id_2.to_owned(),
            is_logs2: false,
            sequence_id: 400,
            xact_id: recent_xact_id,
        },
    ];
    let all_entries = [object_1_entries.clone(), object_2_entries.clone()].concat();

    // Insert our entries into the realtime WAL.
    insert_object_atoms_into_wal(
        all_entries,
        vec![],
        realtime_wal.clone(),
        global_store.clone(),
    )
    .await?;

    let metadata = global_store
        .query_object_metadatas(&[object_id_1, object_id_2])
        .await?;
    if metadata.len() < 2 {
        return Err(anyhow!("Could not retrieve object metadata"));
    }
    let wal_token_1 = metadata[0].wal_token;
    let wal_token_2 = metadata[1].wal_token;

    // Verify initial state - both objects have their entries
    let object_1_before = collect_wal_stream(wal_stream(
        realtime_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_1, wal_token_1),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_2_before = collect_wal_stream(wal_stream(
        realtime_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_2, wal_token_2),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;

    let object_1_total_before = object_1_before
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_1_total_before, 3,
        "Object 1 should have 3 entries before delete"
    );
    let object_2_total_before = object_2_before
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_2_total_before, 2,
        "Object 2 should have 2 entries before delete"
    );

    // Compute a xact_id corresponding to 60 days ago. We'll use this to run our deletes.
    let min_retained_xact_id =
        TransactionId::from_timestamp((now - Duration::days(60)).timestamp() as u64, 0);

    // Do a dry run of the delete.
    let delete_result = realtime_wal
        .delete_up_to_xact_id(
            DeleteUpToXactIdInput {
                scope: WALScope::ObjectId(object_id_1, wal_token_1),
                min_retained_xact_id,
                dry_run: true,
            },
            &default_delete_up_to_xact_id_options_for_testing(),
        )
        .await?;
    assert_eq!(
        delete_result.planned_num_deletes, 1,
        "Dry run should plan to delete 1 entry"
    );
    assert_eq!(
        delete_result.num_deletes, 0,
        "Dry run should not delete any entries"
    );
    // Verify that the data is intact.
    let object_1_dry_run = collect_wal_stream(wal_stream(
        realtime_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_1, wal_token_1),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_1_total_dry_run = object_1_dry_run
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_1_total_dry_run, 3,
        "Object 1 should have 3 entries after dry run"
    );

    // Now run a real delete on the first object and verify the realtime WAL.
    let delete_result = realtime_wal
        .delete_up_to_xact_id(
            DeleteUpToXactIdInput {
                scope: WALScope::ObjectId(object_id_1, wal_token_1),
                min_retained_xact_id,
                dry_run: false,
            },
            &default_delete_up_to_xact_id_options_for_testing(),
        )
        .await?;
    assert_eq!(
        delete_result.num_deletes, 1,
        "Should have deleted exactly 1 WAL file containing old entries from object_id_1"
    );
    let object_1_after = collect_wal_stream(wal_stream(
        realtime_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_1, wal_token_1),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    // Verify that object 1's remaining entry is the recent one
    let object_1_num_entries = object_1_after
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_1_num_entries, 1,
        "Object 1 should have only 1 entry after deletion (recent only)"
    );
    let object_1_remaining_xact_ids: Vec<_> =
        object_1_after.iter().map(|(xact_id, _)| *xact_id).collect();
    assert_eq!(
        object_1_remaining_xact_ids,
        vec![recent_xact_id],
        "Object 1 should only have entries from the recent transaction left"
    );

    // Verify that object 2 still has both old and recent entries
    let object_2_after = collect_wal_stream(wal_stream(
        realtime_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id_2, wal_token_2),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let object_2_num_entries = object_2_after
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        object_2_num_entries, 2,
        "Object 2 should be unaffected by object 1's deletion"
    );
    let mut object_2_xact_ids: Vec<_> =
        object_2_after.iter().map(|(xact_id, _)| *xact_id).collect();
    object_2_xact_ids.sort();
    let mut expected_object_2_xact_ids = vec![old_xact_id, recent_xact_id];
    expected_object_2_xact_ids.sort();
    assert_eq!(
        object_2_xact_ids, expected_object_2_xact_ids,
        "Object 2 should still have both old and recent transactions"
    );

    Ok(())
}

#[derive(Debug, Copy, Clone)]
enum RetentionPoisoningRepairMode {
    WipeOpstampFile,
    Recompact,
}

// In the past we have observed crashes where an un-committed `.del` file from a previous
// retention operation breaks retention because it tries to create a `.del` file with the
// same opstamp. We reproduce the issue here and make sure it doesn't crash.
async fn test_retention_after_tantivy_index_poisoning_harness(
    mode: RetentionPoisoningRepairMode,
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row0".to_string(),
            data: [("field1".to_string(), json!("data0"))]
                .into_iter()
                .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("data1"))]
                .into_iter()
                .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("data2"))]
                .into_iter()
                .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(3),
            id: "row3".to_string(),
            data: [("field1".to_string(), json!("data3"))]
                .into_iter()
                .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(4),
            id: "row4".to_string(),
            data: [("field1".to_string(), json!("data4"))]
                .into_iter()
                .collect(),
            ..Default::default()
        },
    ];
    fixture
        .basic_fixture
        .write_object_wal_entries(wal_entries)
        .await;
    let segment_id = process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap()
    .modified_segment_ids
    .into_iter()
    .next()
    .unwrap();
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Capture the original tantivy segment IDs.
    let orig_tantivy_segments = {
        let meta = fixture
            .basic_fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await;
        meta.segments
            .into_iter()
            .map(|s| s.segment_id)
            .collect::<HashSet<_>>()
    };

    {
        // Construct the tantivy index wrapper with a cold directory, so that we don't pollute the
        // cached directory namespace with files that we'll later delete. The cached directory
        // seems bad at handling files created with `open_write` that are later deleted and then
        // re-written, but this should not really happen in prod, since each compaction gets its
        // own cached directory.
        let schema = fixture.make_full_schema();
        let tantivy_index_wrapper = ReadWriteTantivyIndexWrapper::new(
            crate::directory::AsyncDirectoryArc::new(
                crate::directory::object_store_directory::ObjectStoreDirectory::new(
                    fixture.config().index.store.clone(),
                ),
            ),
            schema,
            &fixture.config().index.prefix,
            &TantivyIndexScope::Segment(segment_id),
        )
        .await?;

        let full_row_id_field = tantivy_index_wrapper
            .tantivy_schema()
            .get_field("_full_row_id")?;

        await_spawn_blocking!(move || -> Result<()> {
            let make_term = |row_id: &str| {
                tantivy::schema::Term::from_field_text(
                    full_row_id_field,
                    &FullRowIdOwned {
                        id: row_id.to_string(),
                        ..Default::default()
                    }
                    .to_string(),
                )
            };
            let mut writer = tantivy_index_wrapper.make_writer_blocking(&Default::default())?;
            // Poison the index by doing operations that will leave `.del` files.
            writer.delete_term(make_term("row4"));
            writer.commit()?;
            Ok(())
        })???;
    }

    match mode {
        RetentionPoisoningRepairMode::WipeOpstampFile => {
            // In this mode, run retention with the default options. The harness retry logic
            // should kick in and let retention proceed successfully by wiping the opstamp file.
            delete_from_segments_up_to_xact_id(
                DeleteFromSegmentsInput {
                    segment_ids: &[segment_id],
                    min_retained_xact_id: TransactionId(3), // Delete everything up to xact_id 3 (first 3 rows)
                    index_store: &fixture.config().index,
                    schema: &fixture.make_full_schema(),
                    global_store: fixture.config().global_store.clone(),
                    locks_manager: fixture.config().locks_manager.clone(),
                    dry_run: false,
                },
                Default::default(),
                DeleteFromSegmentsOptions::default(),
            )
            .await
            .unwrap();
        }
        RetentionPoisoningRepairMode::Recompact => {
            // To force recompaction, allow 0 retries for missing `.del` file handling, so we have to
            // fall back to recompaction. This should result in the segment ids all being different.
            delete_from_segments_up_to_xact_id(
                DeleteFromSegmentsInput {
                    segment_ids: &[segment_id],
                    min_retained_xact_id: TransactionId(3), // Delete everything up to xact_id 3 (first 3 rows)
                    index_store: &fixture.config().index,
                    schema: &fixture.make_full_schema(),
                    global_store: fixture.config().global_store.clone(),
                    locks_manager: fixture.config().locks_manager.clone(),
                    dry_run: false,
                },
                DeleteFromSegmentsOptionalInput {
                    testing_override_missing_del_file_retries: Some(0), // No retries, force recompaction
                    ..Default::default()
                },
                DeleteFromSegmentsOptions::default(),
            )
            .await
            .unwrap();

            // In recompact mode, the index was cleared, so we need to recompact to restore the data.
            compact_segment_wal(
                fixture.compact_wal_input(segment_id),
                Default::default(),
                Default::default(),
            )
            .await?;
        }
    };

    // Get the new tantivy segments after retention. In recompact mode, these will be all-new segments
    // because the index was cleared.
    let new_tantivy_segments = {
        let meta = fixture
            .basic_fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await;
        meta.segments
            .into_iter()
            .map(|s| s.segment_id)
            .collect::<HashSet<_>>()
    };

    match mode {
        RetentionPoisoningRepairMode::WipeOpstampFile => {
            // We should have preserved all the original segments.
            assert!(
                orig_tantivy_segments.is_subset(&new_tantivy_segments),
                "orig {:?} new: {:?}",
                orig_tantivy_segments,
                new_tantivy_segments
            );
        }
        RetentionPoisoningRepairMode::Recompact => {
            // We should have no segments in common.
            assert!(
                orig_tantivy_segments.is_disjoint(&new_tantivy_segments),
                "orig {:?} new: {:?}",
                orig_tantivy_segments,
                new_tantivy_segments
            );
        }
    };

    // Check that retention ran as expected - rows 0, 1, 2 should be deleted.
    let all_docs = fixture.read_segment_docs(segment_id).await;
    let remaining_row_ids: HashSet<_> = all_docs.keys().map(|x| x.id.clone()).collect();
    let expected_row_ids: HashSet<_> = ["row3", "row4"].iter().map(|x| x.to_string()).collect();
    assert_eq!(
        remaining_row_ids, expected_row_ids,
        "Expected rows 3 and 4 to remain after retention deletion"
    );

    Ok(())
}

#[tokio::test]
async fn test_retention_after_tantivy_index_poisoning_with_wipe_opstamp() -> Result<()> {
    run_test_with_global_stores(|use_postgres_global_store| async move {
        test_retention_after_tantivy_index_poisoning_harness(
            RetentionPoisoningRepairMode::WipeOpstampFile,
            use_postgres_global_store,
        )
        .await
    })
    .await
}

#[tokio::test]
async fn test_retention_after_tantivy_index_poisoning_with_recompact() -> Result<()> {
    run_test_with_global_stores(|use_postgres_global_store| async move {
        test_retention_after_tantivy_index_poisoning_harness(
            RetentionPoisoningRepairMode::Recompact,
            use_postgres_global_store,
        )
        .await
    })
    .await
}
