use crate::wal_entry::{
    AuditDataEntries, AuditDataEntry, WalEntry, WalEntryComment, WalEntryComments,
    WalEntrySystemFields,
};
use util::{
    chrono::{DateTime, Utc},
    itertools::Itertools,
    serde_json::{json, Value},
    system_types::{ObjectIdOwned, ObjectType},
    xact::{PaginationKey, PaginationKeyCounter, TransactionId},
};

fn make_timestamp(ts: i64) -> DateTime<Utc> {
    DateTime::from_timestamp_millis(ts).unwrap()
}

fn assert_sanitized_entries_match(lhs: &WalEntry, rhs: &WalEntry, context: &str) {
    let mut lhs = lhs.clone();
    let mut rhs = rhs.clone();
    lhs.sanitize();
    rhs.sanitize();
    assert_eq!(lhs, rhs, "{}", context);
}

#[test]
fn test_wal_entry_from_json() {
    let base_value = json!({
      "id": "id",
      "created": make_timestamp(100).to_rfc3339(),
      "_pagination_key": "p42",
      "_xact_id": u64::MAX,
      "_object_type": "experiment",
      "_object_id": "obj1",
      "root_span_id": "foo",
      "span_id": "bar",
    });

    let not_object_failure_cases = [
        Value::Null,
        Value::Bool(true),
        Value::Array(vec![base_value.clone()]),
    ];

    not_object_failure_cases.iter().for_each(|data| {
        assert!(
            WalEntry::new(data.clone()).is_err(),
            "Expected error for: {:?}",
            data
        );
    });

    {
        let entry = WalEntry::new(base_value.clone()).unwrap();
        assert_eq!(entry._xact_id, TransactionId(u64::MAX));
        assert_eq!(entry.id, "id");
        assert_eq!(entry.created, make_timestamp(100));
        assert_eq!(entry._object_type, ObjectType::Experiment);
        assert_eq!(&*entry._object_id, "obj1");
    }

    // Missing fields / wrong type.
    let test_missing_field_failure_case = |field: &str| {
        let mut value = base_value.clone();
        value.as_object_mut().unwrap().remove(field);
        assert!(
            WalEntry::new(value).is_err(),
            "Expected error for missing field: {}",
            field
        );
        value = base_value.clone();
        value
            .as_object_mut()
            .unwrap()
            .insert(field.to_string(), Value::Null);
        assert!(
            WalEntry::new(value).is_err(),
            "Expected error for null field: {}",
            field
        );
    };

    for field in &[
        "id",
        "created",
        "_pagination_key",
        "_xact_id",
        "_object_type",
        "_object_id",
    ] {
        test_missing_field_failure_case(field);
    }
}

fn base_wal_entry() -> WalEntry {
    return WalEntry {
        id: "1".to_string(),
        created: make_timestamp(100),
        _pagination_key: PaginationKey(0),
        _xact_id: TransactionId(200),
        _object_type: ObjectType::Dataset,
        _object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
        root_span_id: "foo".to_string(),
        span_id: "bar".to_string(),
        data: json!({"key1": "value1"}).as_object().unwrap().clone(),
        ..Default::default()
    };
}

#[test]
fn test_replace() {
    let mut entry1 = WalEntry {
        _is_merge: Some(true),
        ..base_wal_entry()
    };
    let entry2 = WalEntry {
        created: make_timestamp(200),
        _xact_id: TransactionId(300),
        _is_merge: Some(false),
        _object_delete: Some(false),
        root_span_id: "baz".to_string(),
        span_id: "qux".to_string(),
        data: json!({"key2": "value2"}).as_object().unwrap().clone(),
        ..base_wal_entry()
    };
    assert!(entry1.merge(entry2).is_ok());
    assert_eq!(entry1.created, make_timestamp(100));
    assert_eq!(entry1._xact_id, TransactionId(300));
    assert!(!entry1._is_merge.unwrap_or(false));
    assert!(!entry1._object_delete.unwrap_or(false));
    assert_eq!(entry1.root_span_id, "baz".to_string());
    assert_eq!(entry1.span_id, "qux".to_string());
    assert_eq!(entry1.data.get("key1"), None);
    assert_eq!(entry1.data.get("key2").unwrap(), "value2");
}

#[test]
fn test_merge() {
    let mut entry1 = WalEntry { ..base_wal_entry() };
    let entry2 = WalEntry {
        created: make_timestamp(200),
        _xact_id: TransactionId(300),
        _is_merge: Some(true),
        data: json!({"key2": "value2"}).as_object().unwrap().clone(),
        ..base_wal_entry()
    };
    assert!(entry1.merge(entry2).is_ok());
    assert_eq!(entry1.created, make_timestamp(100));
    assert_eq!(entry1._xact_id, TransactionId(300));
    assert!(!entry1._is_merge.unwrap_or(false));
    assert!(!entry1._object_delete.unwrap_or(false));
    assert_eq!(entry1.data.get("key1").unwrap(), "value1");
    assert_eq!(entry1.data.get("key2").unwrap(), "value2");

    // Merge in a delete.
    let entry3 = WalEntry {
        _xact_id: TransactionId(400),
        _object_delete: Some(true),
        data: json!({"key3": "value3"}).as_object().unwrap().clone(),
        ..base_wal_entry()
    };
    assert!(entry1.merge(entry3).is_ok());
    assert_eq!(entry1._object_delete, Some(true));

    // Merging into a deleted row should end up as a replace.
    let entry4 = WalEntry {
        created: make_timestamp(300),
        _pagination_key: PaginationKey(10),
        _xact_id: TransactionId(500),
        _is_merge: Some(true),
        data: json!({"key3": "value3"}).as_object().unwrap().clone(),
        ..base_wal_entry()
    };
    // Now merge this entry into our original entry
    assert!(entry1.merge(entry4).is_ok());
    assert_eq!(entry1.created, make_timestamp(300));
    assert_eq!(entry1._pagination_key, PaginationKey(10));
    assert_eq!(entry1._xact_id, TransactionId(500));
    assert!(!entry1._is_merge.unwrap_or(false));
    assert!(!entry1._object_delete.unwrap_or(false));
    assert_eq!(entry1.data.get("key1"), None);
    assert_eq!(entry1.data.get("key2"), None);
    assert_eq!(entry1.data.get("key3").unwrap(), "value3");
}

// Given a sequence of WAL entries, try merging them in all different associativity orders. Check
// that the result matches the expected in every permutation of merges.
fn check_merge_all_associativities(entries: Vec<WalEntry>, expected: WalEntry) {
    // Each associativity order can be described as an ordering of indices to merge from. So e.g.
    // if we have entries [e0, e1, e2], the possible associativities are [1, 2] ((e0, e1), e2) and
    // [2, 1] (e0, (e1, e2)). So the assocativities are all permutations of the indices [1, n).
    assert!(!entries.is_empty());
    if entries.len() == 1 {
        assert_sanitized_entries_match(&entries[0], &expected, "singular entry");
        return;
    }

    let inds: Vec<usize> = (1..entries.len()).collect();
    for ind_permutation in inds.iter().permutations(inds.len()) {
        let mut merged_entries = entries.clone().into_iter().enumerate().collect::<Vec<_>>();
        for &&ind in ind_permutation.iter() {
            // Find the entry with the index we're merging from.
            let ind_pos = merged_entries.iter().position(|(i, _)| *i == ind).unwrap();
            // Merge the entry at ind_pos into the entry at ind_pos - 1.
            let (_, merge_from) = merged_entries.remove(ind_pos);
            merged_entries[ind_pos - 1].1.merge(merge_from).unwrap();
        }
        assert_eq!(merged_entries.len(), 1);
        assert_sanitized_entries_match(
            &merged_entries[0].1,
            &expected,
            &format!("permutation {:?}", ind_permutation),
        );
    }
}

#[test]
fn test_merge_associativity_delete() {
    let entries = vec![
        WalEntry {
            data: json!({"k1": "v1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _is_merge: Some(true),
            data: json!({"k2": "v2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _object_delete: Some(true),
            data: json!({"k3": "v3"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _is_merge: Some(true),
            root_span_id: "foo".to_string(),
            data: json!({"k4": "v4"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    check_merge_all_associativities(
        entries,
        WalEntry {
            root_span_id: "foo".to_string(),
            _replace_sticky_system_fields: Some(true),
            data: json!({"k4": "v4"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    );
}

#[test]
fn test_merge_associativity_merge_replace() {
    let entries = vec![
        WalEntry {
            data: json!({"k1": "v1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _is_merge: Some(true),
            data: json!({"k2": "v2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            data: json!({"k3": "v3"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _is_merge: Some(true),
            root_span_id: "foo".to_string(),
            data: json!({"k4": "v4"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    check_merge_all_associativities(
        entries,
        WalEntry {
            data: json!({"k3": "v3", "k4": "v4"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    );
}

#[test]
fn test_merge_associativity_replace_sticky_system_fields() {
    let entries = vec![
        WalEntry {
            root_span_id: "foo".to_string(),
            data: json!({"k1": "v1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            root_span_id: "bar".to_string(),
            _is_merge: Some(true),
            _replace_sticky_system_fields: Some(true),
            data: json!({"k2": "v2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _is_merge: Some(true),
            root_span_id: "baz".to_string(),
            data: json!({"k3": "v3"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    check_merge_all_associativities(
        entries,
        WalEntry {
            root_span_id: "bar".to_string(),
            _replace_sticky_system_fields: Some(true),
            data: json!({"k1": "v1", "k2": "v2", "k3": "v3"})
                .as_object()
                .unwrap()
                .clone(),
            ..Default::default()
        },
    );
}

#[test]
fn test_audit_data_merging() {
    // Test 1: Basic case - merging entries with different transaction IDs
    let entry1 = WalEntry {
        _xact_id: TransactionId(100),
        _self_audit_data: Some(json!({"foo": "bar"}).as_object().unwrap().clone()),
        ..Default::default()
    };

    let entry2 = WalEntry {
        _xact_id: TransactionId(200),
        _is_merge: Some(true),
        _self_audit_data: Some(json!({"baz": "bear"}).as_object().unwrap().clone()),
        ..Default::default()
    };

    // Entry with same xact_id as entry2 - will replace its audit data
    let entry3 = WalEntry {
        _xact_id: TransactionId(200),
        _is_merge: Some(true),
        _self_audit_data: Some(json!({"fab": "gear"}).as_object().unwrap().clone()),
        ..Default::default()
    };

    let mut merged_entry = entry1.clone();
    merged_entry.merge(entry2).unwrap();
    merged_entry.merge(entry3).unwrap();
    merged_entry.sanitize();

    // Should have one entry per unique transaction ID, sorted by xact_id
    assert_eq!(merged_entry.audit_data.0.len(), 2);
    assert_eq!(merged_entry.audit_data.0[0]._xact_id, TransactionId(100));
    assert_eq!(merged_entry.audit_data.0[1]._xact_id, TransactionId(200));
    assert_eq!(
        merged_entry.audit_data.0[0].data,
        json!({"foo": "bar"}).as_object().unwrap().clone()
    );
    assert_eq!(
        merged_entry.audit_data.0[1].data,
        json!({"fab": "gear"}).as_object().unwrap().clone(),
    );

    // Test 2: Delete clears all audit data
    let delete_entry = WalEntry {
        _xact_id: TransactionId(300),
        _object_delete: Some(true),
        _self_audit_data: Some(json!({"ignored": "data"}).as_object().unwrap().clone()),
        ..Default::default()
    };
    merged_entry.merge(delete_entry).unwrap();
    merged_entry.sanitize();
    assert_eq!(merged_entry.audit_data.0.len(), 0);

    // Test 3: Direct audit_data manipulation (not via _self_audit_data)
    let mut entry_with_direct_audit = WalEntry {
        _xact_id: TransactionId(400),
        audit_data: AuditDataEntries(vec![
            AuditDataEntry {
                _xact_id: TransactionId(400),
                data: json!({"direct": "entry1"}).as_object().unwrap().clone(),
            },
            AuditDataEntry {
                _xact_id: TransactionId(500),
                data: json!({"direct": "entry2"}).as_object().unwrap().clone(),
            },
        ]),
        ..Default::default()
    };

    // Merging with entry that has audit data for same xact_id
    let entry_with_conflicting_audit = WalEntry {
        _xact_id: TransactionId(600),
        audit_data: AuditDataEntries(vec![AuditDataEntry {
            _xact_id: TransactionId(400),
            data: json!({"replaced": "value"}).as_object().unwrap().clone(),
        }]),
        ..Default::default()
    };

    entry_with_direct_audit
        .merge(entry_with_conflicting_audit)
        .unwrap();
    entry_with_direct_audit.sanitize();

    // xact_id 400's data should be replaced, xact_id 500 remains unchanged
    assert_eq!(entry_with_direct_audit.audit_data.0.len(), 2);
    assert_eq!(
        entry_with_direct_audit.audit_data.0[0]._xact_id,
        TransactionId(400)
    );
    assert_eq!(
        entry_with_direct_audit.audit_data.0[1]._xact_id,
        TransactionId(500)
    );
    assert_eq!(
        entry_with_direct_audit.audit_data.0[0].data,
        json!({"replaced": "value"}).as_object().unwrap().clone()
    );

    // Test 4: Transaction ID change preserves previous audit data
    let mut entry_xact_change = WalEntry {
        _xact_id: TransactionId(700),
        _self_audit_data: Some(json!({"original": "data"}).as_object().unwrap().clone()),
        ..Default::default()
    };

    entry_xact_change.sanitize();
    assert_eq!(entry_xact_change.audit_data.0.len(), 1);

    let new_xact_entry = WalEntry {
        _xact_id: TransactionId(800),
        _is_merge: Some(false), // Replace mode - changes transaction ID
        _self_audit_data: Some(json!({"new": "data"}).as_object().unwrap().clone()),
        ..Default::default()
    };

    entry_xact_change.merge(new_xact_entry).unwrap();
    entry_xact_change.sanitize();

    // Both transaction IDs' audit data should be preserved
    assert_eq!(entry_xact_change.audit_data.0.len(), 2);
    assert_eq!(
        entry_xact_change.audit_data.0[0]._xact_id,
        TransactionId(700)
    );
    assert_eq!(
        entry_xact_change.audit_data.0[1]._xact_id,
        TransactionId(800)
    );
}

#[test]
fn test_merge_associativity_standalone_comment() {
    // Test merging into a standalone comment
    let comment1_0 = WalEntryComment {
        id: "comment1".to_string(),
        _xact_id: TransactionId(0),
        _is_merge: Some(false),
        _object_delete: Some(false),
        data: json!({"comment_data": "comment1"})
            .as_object()
            .unwrap()
            .clone(),
        ..Default::default()
    };

    let comment1_1 = WalEntryComment {
        id: "comment1".to_string(),
        _xact_id: TransactionId(10),
        _is_merge: Some(true),
        _object_delete: None,
        data: json!({"more_data": "comment2"})
            .as_object()
            .unwrap()
            .clone(),
    };

    let comment2 = WalEntryComment {
        id: "comment2".to_string(),
        _xact_id: TransactionId(10),
        _is_merge: None,
        _object_delete: Some(true),
        data: json!({"deleted_data": "comment3"})
            .as_object()
            .unwrap()
            .clone(),
    };

    let comment3 = WalEntryComment {
        id: "comment3".to_string(),
        _xact_id: TransactionId(5),
        ..Default::default()
    };

    // Create comment collections
    let comments_list1 = WalEntryComments(vec![comment1_0]);
    let comments_list2 = WalEntryComments(vec![comment1_1, comment2, comment3]);

    let entries = vec![
        WalEntry {
            root_span_id: "foo".to_string(),
            span_id: "span1".to_string(),
            _is_standalone_comment: Some(true),
            comments: comments_list1,
            data: json!({"k1": "v1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            root_span_id: "bar".to_string(),
            span_id: "span2".to_string(),
            _is_merge: Some(true),
            data: json!({"k2": "v2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            root_span_id: "baz".to_string(),
            span_id: "span3".to_string(),
            _is_standalone_comment: Some(true),
            comments: comments_list2,
            data: json!({"k3": "v3"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    // When merging into a standalone comment, the standalone comment's data is preserved
    // but all other fields are replaced by the merged entry, and comments are merged
    let expected_comment1 = WalEntryComment {
        id: "comment1".to_string(),
        _xact_id: TransactionId(10),
        data: json!({"comment_data": "comment1", "more_data": "comment2"})
            .as_object()
            .unwrap()
            .clone(),
        ..Default::default()
    };

    let expected_comment3 = WalEntryComment {
        id: "comment3".to_string(),
        _xact_id: TransactionId(5),
        ..Default::default()
    };
    // Note: comment2 will be filtered out during sanitization because it has _object_delete=true
    let expected_comments = WalEntryComments(vec![expected_comment3, expected_comment1]);

    check_merge_all_associativities(
        entries,
        WalEntry {
            root_span_id: "bar".to_string(),
            span_id: "span2".to_string(),
            _is_merge: Some(true),
            comments: expected_comments,
            data: json!({"k2": "v2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    );
}

#[test]
fn test_merge_with_merge_paths() {
    let mut entry1 = WalEntry {
        data: json!({
            "a": {
                "b": {
                    "c": 1,
                    "d": 2
                },
                "e": 3
            },
            "x": {
                "y": {
                    "m": 4
                }
            }
        })
        .as_object()
        .unwrap()
        .clone(),
        ..base_wal_entry()
    };

    let mut entry2 = WalEntry {
        _is_merge: Some(true),
        _merge_paths: Some(vec![vec!["a".to_string(), "b".to_string()]]),
        data: json!({
            "a": {
                "b": {
                    "c": 10,
                    "f": 20
                },
            },
            "x": {
                "y": {
                    "q": 40,
                },
            },
        })
        .as_object()
        .unwrap()
        .clone(),
        ..base_wal_entry()
    };

    let entry3 = WalEntry {
        _is_merge: Some(true),
        _merge_paths: Some(vec![vec!["x".to_string(), "y".to_string()]]),
        data: json!({
            "a": {
                "b": {
                    "c": 100,
                    "g": 200
                }
            },
            "x": {
                "y": {
                    "z": 300
                }
            }
        })
        .as_object()
        .unwrap()
        .clone(),
        ..base_wal_entry()
    };

    // Combine the two entries with merge paths.
    assert!(entry2.merge(entry3).is_ok());

    // The path "x.y" should be completely overwritten, not deeply merged
    assert_eq!(
        entry2.data,
        json!({
            "a": {
                "b": {
                    "c": 100,
                    "f": 20,
                    "g": 200
                },
            },
            "x": {
                "y": {
                    "z": 300
                }
            }
        })
        .as_object()
        .unwrap()
        .clone()
    );

    // Check that the merge paths are combined
    assert!(entry2._is_merge.unwrap_or(false));
    assert_eq!(
        entry2._merge_paths,
        Some(vec![
            vec!["a".to_string(), "b".to_string()],
            vec!["x".to_string(), "y".to_string()],
        ])
    );

    assert!(entry1.merge(entry2).is_ok());

    // Both paths should be overwritten now
    assert_eq!(
        entry1.data,
        json!({
            "a": {
                "b": {
                    "c": 100,
                    "f": 20,
                    "g": 200
                },
                "e": 3
            },
            "x": {
                "y": {
                    "z": 300
                }
            }
        })
        .as_object()
        .unwrap()
        .clone()
    );

    // Merge paths should be None since entry1 is not a merge-type entry
    entry1.sanitize();
    assert_eq!(entry1._merge_paths, None);
}

#[test]
fn test_merge_different_full_row_id() {
    let mut entry1 = base_wal_entry();
    let entry2 = WalEntry {
        id: "2".to_string(),
        ..base_wal_entry()
    };
    assert!(entry1.merge(entry2.clone()).is_err());
    let entry2 = WalEntry {
        _object_type: ObjectType::Experiment,
        ..base_wal_entry()
    };
    assert!(entry1.merge(entry2.clone()).is_err());
    let entry2 = WalEntry {
        _object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
        ..base_wal_entry()
    };
    assert!(entry1.merge(entry2.clone()).is_err());
}

// Helper function to create a test value with all system fields
fn create_test_value() -> Value {
    json!({
        "id": "test_id",
        "created": serde_json::to_value(Utc::now()).unwrap(),
        "_pagination_key": serde_json::to_value(PaginationKey(123)).unwrap(),
        "_xact_id": serde_json::to_value(TransactionId(456)).unwrap(),
        "_object_type": serde_json::to_value(ObjectType::Project).unwrap(),
        "_object_id": serde_json::to_value("project_xyz").unwrap(),
        "root_span_id": serde_json::to_value("root_span_1").unwrap(),
        "span_id": serde_json::to_value("span_2").unwrap(),
        "data": {
            "field1": "value1",
            "field2": 42
        }
    })
}

// Helper function to run normalize_row and convert back to Value
fn normalize_and_convert(value: Value) -> Value {
    let now_ts_millis = util::now_ts_millis();
    let normalized = crate::wal::normalize_row(
        TransactionId(456),
        PaginationKeyCounter::WithinXactRowNum(0),
        value,
        now_ts_millis,
    )
    .unwrap();
    normalized.to_value()
}

// Test that to the extent possible, rows that normalize successfully can be
// parsed with WalEntrySystemFields, and the converse.
#[test]
fn test_wal_entry_system_fields_parity() {
    {
        // Test case 1: Value with all system fields in correct format
        let value = create_test_value();
        let normalized = normalize_and_convert(value.clone());
        assert_eq!(value, normalized);
        // Should parse successfully
        let _: WalEntrySystemFields = serde_json::from_value(value).unwrap();
    }

    // Test case 2: Value missing some system fields
    for system_field in &["id", "_pagination_key", "_xact_id", "root_span_id"] {
        let mut value = create_test_value();
        if let Some(obj) = value.as_object_mut() {
            obj.remove(*system_field);
        }
        let normalized = normalize_and_convert(value.clone());
        assert_ne!(value, normalized);
        // Should fail to parse
        assert!(serde_json::from_value::<WalEntrySystemFields>(value).is_err());
    }

    // Test case 3: Value with extra fields that don't affect normalization
    {
        let mut value = create_test_value();
        if let Some(obj) = value.as_object_mut() {
            obj.insert("extra_field".to_string(), json!("extra_value"));
        }
        let normalized = normalize_and_convert(value.clone());
        assert_eq!(value, normalized);
        // Should parse successfully
        let _: WalEntrySystemFields = serde_json::from_value(value).unwrap();
    }
}
