use std::{
    collections::{HashMap, HashSet},
    path::Path,
    sync::Arc,
};

use async_util::{await_spawn_blocking, test_util::two_way_sync_point};
use futures::join;
use once_cell::sync::Lazy;
use tantivy::columnar::MonotonicallyMappableToU64;
use tokio_stream::StreamExt;
use util::{
    anyhow::Result,
    chrono::{DateTime, Utc},
    schema::{BaseOptions, Field, Schema, TantivyField, TantivyType, TextOptions},
    serde_json::json,
    system_types::{FullObjectId, FullRowIdOwned},
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    clear_compacted_index::{clear_compacted_index, ClearCompactedIndexInput},
    config_with_store::ConfigWithStore,
    global_store::{
        IdSegmentMembershipType, SegmentFieldStatistics, SegmentMetadataUpdate,
        SegmentWalEntriesXactIdStatistic,
    },
    index_document::{make_full_schema, IndexDocument},
    noop_global_locks_manager::NoopGlobalLocksManager,
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    process_wal::{
        compact_segment_wal, process_object_wal, process_object_wal_loaded_for_testing,
        CompactSegmentWalInput, CompactSegmentWalOptionalInput, CompactSegmentWalOptions,
        CompactSegmentWalStreamOptions, ProcessObjectWalInput, ProcessObjectWalLoadedInput,
        ProcessObjectWalLoadedOptionalInput, ProcessObjectWalLoadedOptions,
        ProcessObjectWalLoadedTestingSyncPoints, ProcessObjectWalOptionalInput,
        ProcessObjectWalOptions, ProcessSegmentWalInput, ProcessSegmentWalOptionalInput,
        FIELD_STATISTICS_FIELD_NAMES, XACT_ID_FIELD,
    },
    tantivy_index::{
        write_meta_json, IndexMetaJson, TantivyIndexScope, TantivyIndexWriterOpts,
        ValidateTantivyIndexOptions,
    },
    tantivy_index_test_util::read_all_documents,
    tantivy_index_wrapper::{ReadWriteTantivyIndexWrapper, ReadonlyTantivyIndexWrapper},
    tantivy_index_wrapper_test_util::make_readonly_tantivy_index_wrapper,
    test_util::{
        collect_wal_stream, group_wal_entries, make_compacted_wal_entries,
        make_xact_wal_entry_variants, TmpDirConfigWithStore,
    },
    vacuum_test_util::{
        default_vacuum_index_full_opts_for_testing,
        default_vacuum_segment_wal_full_opts_for_testing, force_vacuum_then_validate_index_wal,
        vacuum_segment_wal_for_testing, VacuumSegmentWalForTestingInput,
        VacuumThenValidateIndexWalArgs,
    },
    wal::{wal_stream, WALScope, Wal},
    wal_entry::{WalEntry, WalEntryComment, WalEntryComments},
};

struct TestFixture {
    _temp_dir: tempfile::TempDir,
    config: ConfigWithStore,
    schema: Schema,
}

impl TestFixture {
    fn new() -> Self {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        Self {
            _temp_dir: tmp_dir_config.tmp_dir,
            config: tmp_dir_config.config,
            schema: basic_schema(),
        }
    }

    async fn wal_token(&self) -> Uuid {
        self.config
            .global_store
            .query_object_metadatas(&[FullObjectId::default()])
            .await
            .unwrap()
            .remove(0)
            .wal_token
    }

    fn process_object_wal_input(&self) -> ProcessObjectWalInput<'_> {
        ProcessObjectWalInput {
            object_id: FullObjectId::default(),
            config: &self.config,
        }
    }

    fn compact_segment_wal_input(&self, segment_id: Uuid) -> CompactSegmentWalInput {
        CompactSegmentWalInput {
            segment_id,
            index_store: self.config.index.clone(),
            schema: make_full_schema(&self.schema).unwrap(),
            global_store: self.config.global_store.clone(),
            locks_manager: self.config.locks_manager.clone(),
        }
    }

    fn index_prefix(&self) -> &Path {
        self.config.index.prefix.as_path()
    }

    fn make_segment_wal(&self) -> ObjectAndGlobalStoreWal {
        ObjectAndGlobalStoreWal {
            object_store: self.config.index.store.clone(),
            global_store: self.config.global_store.clone(),
            directory: self.config.index.directory.clone(),
            store_prefix: self.index_prefix().to_path_buf(),
            store_type: self.config.index.store_type,
        }
    }

    #[allow(unused)]
    async fn make_readwrite_tantivy_index_wrapper(
        &self,
        segment_id: Uuid,
    ) -> ReadWriteTantivyIndexWrapper {
        let schema = make_full_schema(&self.schema).unwrap();
        ReadWriteTantivyIndexWrapper::new(
            self.config.index.directory.clone(),
            schema,
            self.index_prefix(),
            &TantivyIndexScope::Segment(segment_id),
        )
        .await
        .unwrap()
    }

    async fn make_tantivy_index_wrapper(&self, segment_id: Uuid) -> ReadonlyTantivyIndexWrapper {
        let schema = make_full_schema(&self.schema).unwrap();
        make_readonly_tantivy_index_wrapper(
            self.config.global_store.clone(),
            &self.config.index,
            schema,
            &[segment_id],
        )
        .await
        .unwrap()
    }

    fn make_object_wal(&self) -> Arc<dyn Wal> {
        self.config.wal.clone()
    }

    async fn read_segment_wal_entries(
        &self,
        segment_id: Uuid,
    ) -> Vec<(TransactionId, Vec<WalEntry>)> {
        let segment_wal = self.make_segment_wal();
        collect_wal_stream(wal_stream(
            segment_wal
                .wal_metadata_stream(WALScope::Segment(segment_id), Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap()
    }

    async fn write_wal_to_segment(&self, segment_id: Uuid, entries: Vec<WalEntry>) {
        let segment_wal = self.make_segment_wal();
        segment_wal
            .insert(WALScope::Segment(segment_id), entries)
            .await
            .unwrap();
    }

    async fn read_segment_docs(&self, segment_id: Uuid) -> HashMap<FullRowIdOwned, IndexDocument> {
        let full_schema = make_full_schema(&self.schema).unwrap();

        let readonly_tantivy_index_wrapper = make_readonly_tantivy_index_wrapper(
            self.config.global_store.clone(),
            &self.config.index,
            full_schema,
            &[segment_id],
        )
        .await
        .unwrap();

        self.read_segment_docs_with_index_wrapper(readonly_tantivy_index_wrapper)
            .await
    }

    async fn read_segment_docs_with_index_wrapper(
        &self,
        tantivy_index_wrapper: ReadonlyTantivyIndexWrapper,
    ) -> HashMap<FullRowIdOwned, IndexDocument> {
        await_spawn_blocking!(move || {
            let reader = tantivy_index_wrapper.make_reader_blocking().unwrap();
            let tantivy_docs = read_all_documents(&reader);
            let writable_tantivy_schema = tantivy_index_wrapper.writable_schema;
            let schema_invert_fields = &writable_tantivy_schema.invert_fields;
            let mut docs: HashMap<FullRowIdOwned, IndexDocument> = HashMap::new();
            for (_, doc) in tantivy_docs {
                let index_doc = IndexDocument::from_tantivy_document(&doc, schema_invert_fields)
                    .unwrap()
                    .to_sanitized()
                    .unwrap();
                let row_id = index_doc.wal_entry.full_row_id().to_owned();
                if let Some(dup) = docs.insert(row_id, index_doc) {
                    panic!("Duplicate row id {:?}", dup.wal_entry.full_row_id());
                }
            }
            docs
        })
        .unwrap()
        .unwrap()
    }

    async fn write_object_wal_entries(&self, entries: Vec<WalEntry>) {
        let wal = self.make_object_wal();
        wal.insert(
            WALScope::ObjectId(FullObjectId::default(), self.wal_token().await),
            entries,
        )
        .await
        .unwrap();
    }

    async fn read_global_last_processed_xact_id(&self) -> Option<TransactionId> {
        self.config
            .global_store
            .query_object_metadatas(&[FullObjectId::default()])
            .await
            .unwrap()
            .remove(0)
            .last_processed_xact_id
    }

    async fn read_multi_segment_docs(
        &self,
        segment_ids: &[Uuid],
    ) -> HashMap<FullRowIdOwned, IndexDocument> {
        let mut docs = HashMap::new();
        for segment_id in segment_ids {
            docs.extend(self.read_segment_docs(*segment_id).await);
        }
        docs
    }

    async fn initialize_segment_metadata(&self, segment_id: Uuid) {
        self.initialize_segment_metadata_in_object(FullObjectId::default(), segment_id)
            .await;
    }

    async fn initialize_segment_metadata_in_object<'a>(
        &self,
        object_id: FullObjectId<'a>,
        segment_id: Uuid,
    ) {
        self.config
            .global_store
            .update_segment_ids(&[(object_id, &[segment_id], &[])])
            .await
            .unwrap();
        self.config
            .global_store
            .upsert_segment_metadatas(
                vec![(segment_id, SegmentMetadataUpdate::default())]
                    .into_iter()
                    .collect(),
            )
            .await
            .unwrap();
    }

    async fn run_process_segment_wal(
        &self,
        input: ProcessSegmentWalInput<'_>,
        optional_input: ProcessSegmentWalOptionalInput,
    ) -> Result<()> {
        let wal_entries_metadata =
            crate::process_wal::process_segment_wal_for_testing(input, optional_input).await?;
        self.config
            .global_store
            .upsert_segment_wal_entries(wal_entries_metadata)
            .await?;
        Ok(())
    }

    async fn fetch_segment_tantivy_metadata(&self, segment_id: Uuid) -> IndexMetaJson {
        self.config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta
            .unwrap()
            .tantivy_meta
    }
}

static BASIC_PROCESS_OBJECT_WAL_OPTIONS: Lazy<ProcessObjectWalOptions> =
    Lazy::new(|| ProcessObjectWalOptions {
        max_rows_per_segment: 1,
        ..Default::default()
    });

static BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS: Lazy<ProcessObjectWalLoadedOptions> =
    Lazy::new(|| ProcessObjectWalLoadedOptions {
        max_rows_per_segment: BASIC_PROCESS_OBJECT_WAL_OPTIONS.max_rows_per_segment,
    });

static BASIC_COMPACT_SEGMENT_WAL_OPTIONS: Lazy<CompactSegmentWalOptions> =
    Lazy::new(|| CompactSegmentWalOptions {
        writer_opts: TantivyIndexWriterOpts {
            num_writer_threads: 8,
            memory_budget_per_indexing_operation: 800_000_000,
            index_batch_size: 1000,
            validate_opts: ValidateTantivyIndexOptions {
                index_writer_validate: true,
                index_writer_validate_only_deletes: false,
                ..Default::default()
            },
            ..Default::default()
        },
        ..Default::default()
    });

static COMPACT_SEGMENT_WAL_NO_MERGES_OPTS: Lazy<CompactSegmentWalOptions> =
    Lazy::new(|| CompactSegmentWalOptions {
        writer_opts: TantivyIndexWriterOpts {
            index_writer_force_no_merges: true,
            ..Default::default()
        },
        ..Default::default()
    });

fn basic_schema() -> Schema {
    let text_options = TextOptions {
        stored: true,
        fast: false,
        tokenize: false,
    };
    let base_options = BaseOptions {
        stored: true,
        fast: false,
    };

    Schema::new(
        "test".to_string(),
        vec![
            Field {
                name: "field1".to_string(),
                tantivy: vec![TantivyField {
                    name: "field1".to_string(),
                    field_ts: 1,
                    field_type: TantivyType::Str(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field2".to_string(),
                tantivy: vec![TantivyField {
                    name: "field2".to_string(),
                    field_ts: 2,
                    field_type: TantivyType::I64(base_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field3".to_string(),
                tantivy: vec![TantivyField {
                    name: "field3".to_string(),
                    field_ts: 3,
                    field_type: TantivyType::Json(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "comments".to_string(),
                tantivy: vec![TantivyField {
                    name: "comments".to_string(),
                    field_ts: 4,
                    field_type: TantivyType::Json(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
        ],
        Default::default(),
    )
    .unwrap()
}

fn basic_wal_entries() -> Vec<WalEntry> {
    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(0),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            id: "row0".to_string(),
            data: json!({
                "field1": "foo",
                "field3": json!({ "input": "bar" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(0),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            _is_merge: Some(true),
            id: "row1".to_string(),
            data: json!({
                "field1": "gop",
                "field2": 42,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: TransactionId(1),
            created: DateTime::<Utc>::from_timestamp_nanos(3000),
            _is_merge: Some(true),
            id: "row0".to_string(),
            data: json!({
                "field1": "grue",
                "field2": 99,
                "field3": json!({ "output": "baz" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(1),
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            _is_merge: Some(true),
            id: "row2".to_string(),
            // Make sure this is in a different trace from the other row in this object.
            root_span_id: "r2".to_string(),
            data: json!({
                "field3": json!({ "metadata": "yes" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

fn basic_wal_entries_compacted() -> HashMap<FullRowIdOwned, IndexDocument> {
    let docs = vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(1),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            id: "row0".to_string(),
            data: json!({
                "field1": "grue",
                "field2": 99,
                "field3": json!({ "input": "bar", "output": "baz" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(0),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: "row1".to_string(),
            data: json!({
                "field1": "gop",
                "field2": 42,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(1),
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            id: "row2".to_string(),
            root_span_id: "r2".to_string(),
            data: json!({
                "field3": json!({ "metadata": "yes" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ];
    make_compacted_wal_entries(docs)
}

#[tokio::test]
async fn test_basic_compact_segment_wal() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;
    fixture
        .write_wal_to_segment(segment_id, basic_wal_entries())
        .await;

    // Check the wal entry statistics.
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(0))
    );
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(true)
            )
            .await
            .unwrap()
            .remove(0),
        None
    );

    assert_eq!(
        compact_segment_wal(
            fixture.compact_segment_wal_input(segment_id),
            Default::default(),
            BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
        )
        .await
        .unwrap()
        .num_wal_entries_compacted,
        4
    );

    let check_segment = || async {
        // Check the wal entry statistics again.
        assert_eq!(
            fixture
                .config
                .global_store
                .query_segment_wal_entries_xact_id_statistic(
                    &[segment_id],
                    SegmentWalEntriesXactIdStatistic::Min,
                    Some(false)
                )
                .await
                .unwrap()
                .remove(0),
            None
        );
        assert_eq!(
            fixture
                .config
                .global_store
                .query_segment_wal_entries_xact_id_statistic(
                    &[segment_id],
                    SegmentWalEntriesXactIdStatistic::Min,
                    Some(true)
                )
                .await
                .unwrap()
                .remove(0),
            Some(TransactionId(0))
        );
        assert_eq!(
            fixture
                .config
                .global_store
                .query_segment_wal_entries_xact_id_statistic(
                    &[segment_id],
                    SegmentWalEntriesXactIdStatistic::Max,
                    Some(true)
                )
                .await
                .unwrap()
                .remove(0),
            Some(TransactionId(1))
        );

        assert_hashmap_eq(
            &fixture.read_segment_docs(segment_id).await,
            &basic_wal_entries_compacted(),
        );

        // Check the pagination key segment statistics with global_store.query_field_statistics.
        let field_statistics = fixture
            .config
            .global_store
            .query_field_statistics(&[segment_id], &FIELD_STATISTICS_FIELD_NAMES)
            .await
            .unwrap();
        assert_eq!(
            field_statistics[&segment_id],
            [
                (
                    "_pagination_key".to_string(),
                    SegmentFieldStatistics::new(0, 3).unwrap()
                ),
                (
                    "created".to_string(),
                    SegmentFieldStatistics::new(
                        MonotonicallyMappableToU64::to_u64(1000_i64),
                        MonotonicallyMappableToU64::to_u64(4000_i64)
                    )
                    .unwrap()
                ),
                (
                    XACT_ID_FIELD.to_string(),
                    SegmentFieldStatistics::new(
                        MonotonicallyMappableToU64::to_u64(0_u64),
                        MonotonicallyMappableToU64::to_u64(1_u64),
                    )
                    .unwrap(),
                ),
            ]
            .into_iter()
            .collect()
        );
    };

    check_segment().await;

    // Running compaction again should do nothing.
    assert_eq!(
        compact_segment_wal(
            fixture.compact_segment_wal_input(segment_id),
            Default::default(),
            BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
        )
        .await
        .unwrap()
        .num_wal_entries_compacted,
        0
    );

    check_segment().await;

    vacuum_segment_wal_for_testing(
        VacuumSegmentWalForTestingInput {
            config_with_store: &fixture.config,
            object_ids: Some(&[FullObjectId::default()]),
        },
        Default::default(),
        default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await;

    for stateless in [false, true] {
        force_vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
            config_with_store: &fixture.config,
            full_schema: make_full_schema(&fixture.schema).unwrap(),
            object_ids: Some(&[FullObjectId::default()]),
            stateless,
            options: default_vacuum_index_full_opts_for_testing(),
            expected_segment_id_cursor: if stateless {
                Some(Some(segment_id))
            } else {
                None
            },
            dry_run: false,
        })
        .await;
    }

    // If we re-compact starting from any of the transaction IDs, it should end up with the same
    // result.
    for start_xact_id in [TransactionId(0), TransactionId(1)] {
        let before_index_meta = fixture
            .config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta;

        // Wipe the meta.json file just to make sure we flash it when we re-compact.
        write_meta_json(
            fixture.config.index.directory.as_ref(),
            &TantivyIndexScope::Segment(segment_id).path(&fixture.config.index.prefix),
            &IndexMetaJson::default(),
        )
        .await
        .unwrap();

        assert_eq!(
            compact_segment_wal(
                fixture.compact_segment_wal_input(segment_id),
                CompactSegmentWalOptionalInput {
                    start_xact_id: Some(start_xact_id),
                    ..Default::default()
                },
                CompactSegmentWalOptions {
                    ..BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone()
                },
            )
            .await
            .unwrap()
            .num_wal_entries_compacted,
            2 * (2 - start_xact_id.0) as usize
        );
        check_segment().await;
        // Also check that the metadata changed, but the last_compacted_xact_id did not.
        let after_index_meta = fixture
            .config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta;
        assert_eq!(
            before_index_meta.as_ref().map(|x| x.xact_id),
            after_index_meta.as_ref().map(|x| x.xact_id)
        );
        assert_ne!(before_index_meta, after_index_meta);

        vacuum_segment_wal_for_testing(
            VacuumSegmentWalForTestingInput {
                config_with_store: &fixture.config,
                object_ids: Some(&[FullObjectId::default()]),
            },
            Default::default(),
            default_vacuum_segment_wal_full_opts_for_testing(),
        )
        .await;

        for stateless in [true, false] {
            force_vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
                config_with_store: &fixture.config,
                full_schema: make_full_schema(&fixture.schema).unwrap(),
                object_ids: Some(&[FullObjectId::default()]),
                stateless,
                options: default_vacuum_index_full_opts_for_testing(),
                expected_segment_id_cursor: if stateless {
                    Some(Some(segment_id))
                } else {
                    None
                },
                dry_run: false,
            })
            .await;
        }
    }
}

#[tokio::test]
async fn test_compact_segment_wal_end_xact_id() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;
    let wal_entries = basic_wal_entries();
    fixture
        .write_wal_to_segment(segment_id, wal_entries.clone())
        .await;

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            end_xact_id: Some(TransactionId(0)),
            ..Default::default()
        },
        CompactSegmentWalOptions {
            ..BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone()
        },
    )
    .await
    .unwrap();

    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(wal_entries[0..2].to_vec()),
    );

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            start_xact_id: Some(TransactionId(1)),
            end_xact_id: Some(TransactionId(2)),
            ..Default::default()
        },
        CompactSegmentWalOptions {
            ..BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone()
        },
    )
    .await
    .unwrap();

    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &basic_wal_entries_compacted(),
    );

    // Add some new entries at xact id 0 and re-compact. Even if we just specify xact id 0, we
    // should re-compact the full WAL.
    let new_wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row3".to_string(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(0),
            id: "row2".to_string(),
            root_span_id: "r2".to_string(),
            data: json!({
                "field3": json!({ "metadata": "no" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ];
    fixture
        .write_wal_to_segment(segment_id, new_wal_entries.clone())
        .await;

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            start_xact_id: Some(TransactionId(0)),
            end_xact_id: Some(TransactionId(0)),
            ..Default::default()
        },
        CompactSegmentWalOptions {
            ..BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone()
        },
    )
    .await
    .unwrap();

    {
        let mut expected = basic_wal_entries_compacted();
        // The "row2" entry will get merged. Except for the sticky system fields, which will be
        // carried over from the original compacted entry.
        expected.insert(new_wal_entries[1].full_row_id().to_owned(), {
            let mut ret = basic_wal_entries_compacted()
                .remove(&new_wal_entries[1].full_row_id().to_owned())
                .unwrap()
                .wal_entry;
            ret.merge(new_wal_entries[1].clone()).unwrap();
            ret.merge(basic_wal_entries().swap_remove(3)).unwrap();
            // The audit log will not be indexed, so we clear it out.
            ret.audit_data.0.clear();
            IndexDocument { wal_entry: ret }.to_sanitized().unwrap()
        });
        // The "row3" entry is new.
        expected.extend(make_compacted_wal_entries(vec![new_wal_entries[0].clone()]));
        assert_hashmap_eq(&fixture.read_segment_docs(segment_id).await, &expected);
    }
}

#[tokio::test]
async fn test_basic_process_segment_wal() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;
    let wal_entries = group_wal_entries(basic_wal_entries());
    fixture
        .run_process_segment_wal(
            ProcessSegmentWalInput {
                orig_input: fixture.process_object_wal_input(),
                segment_id,
                xact_wal_entries: make_xact_wal_entry_variants(wal_entries.clone()),
                is_new_segment: false,
            },
            Default::default(),
        )
        .await
        .unwrap();

    let check_segment = || async {
        assert_eq!(
            fixture.read_segment_wal_entries(segment_id).await,
            wal_entries
        );
        assert!(fixture.read_segment_docs(segment_id).await.is_empty());
    };

    check_segment().await;

    // If we re-process starting from any of the transaction IDs, it should end up with the same
    // result.
    for slice_ind in 0..wal_entries.len() {
        fixture
            .run_process_segment_wal(
                ProcessSegmentWalInput {
                    orig_input: fixture.process_object_wal_input(),
                    segment_id,
                    xact_wal_entries: make_xact_wal_entry_variants(
                        wal_entries[slice_ind..].to_vec(),
                    ),
                    is_new_segment: false,
                },
                Default::default(),
            )
            .await
            .unwrap();
        check_segment().await;
    }

    // Now if we run compaction, we should end up with all the compacted docs.
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &basic_wal_entries_compacted(),
    );
}

#[tokio::test]
async fn test_process_segment_wal_mark_compacted() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    // First process a few WAL entries on this segment normally. Then process a few more WAL
    // entries, but this time marked as compacted. When we query the min/max uncompacted xact ids
    // from the global store, we should only get the range from the original set of WAL entries.

    // Process the first set of WAL entries normally
    let normal_entries = vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(0),
            id: "row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            ..Default::default()
        },
    ];

    fixture
        .run_process_segment_wal(
            ProcessSegmentWalInput {
                orig_input: fixture.process_object_wal_input(),
                segment_id,
                xact_wal_entries: make_xact_wal_entry_variants(group_wal_entries(
                    normal_entries.clone(),
                )),
                is_new_segment: false,
            },
            Default::default(),
        )
        .await
        .unwrap();

    // Verify the min/max uncompacted xact ids
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(0))
    );

    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );

    // Now process entries marked as compacted
    let compacted_entries = vec![
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(3),
            id: "row3".to_string(),
            ..Default::default()
        },
    ];

    fixture
        .run_process_segment_wal(
            ProcessSegmentWalInput {
                orig_input: fixture.process_object_wal_input(),
                segment_id,
                xact_wal_entries: make_xact_wal_entry_variants(group_wal_entries(
                    compacted_entries.clone(),
                )),
                is_new_segment: false,
            },
            ProcessSegmentWalOptionalInput {
                mark_as_compacted: true,
                ..Default::default()
            },
        )
        .await
        .unwrap();

    // Verify that the min/max uncompacted xact ids still only reflect the original entries.
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(0))
    );

    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );

    // But the compacted entries should be reflected in the compacted statistics
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(true)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(2))
    );

    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(true)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(3))
    );

    // Verify all entries are in the WAL
    let all_wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(all_wal_entries.len(), 4); // 2 normal + 2 compacted entries
}

#[tokio::test]
async fn test_basic_process_object_wal_loaded() {
    let fixture = TestFixture::new();
    let wal_entries = group_wal_entries(basic_wal_entries());

    let run_compaction = |segment_id: Uuid| {
        let fixture = &fixture;
        async move {
            compact_segment_wal(
                fixture.compact_segment_wal_input(segment_id),
                Default::default(),
                BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
            )
            .await
            .unwrap();
        }
    };

    let wal_entries0 = wal_entries[0].clone();
    let process_output0 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries0.clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();

    // We should have one segment containing both rows.
    assert_eq!(process_output0.modified_segment_ids.len(), 1);
    let segment_id0 = process_output0.modified_segment_ids.iter().next().unwrap();
    assert_eq!(
        fixture
            .config
            .global_store
            .list_segment_ids(&[wal_entries0.1[0].full_object_id()], None)
            .await
            .unwrap()
            .remove(0),
        vec![segment_id0.clone()]
    );
    assert_eq!(
        fixture.read_segment_wal_entries(*segment_id0).await,
        vec![wal_entries0.clone()]
    );
    // At first there should be no compacted entries.
    assert!(fixture.read_segment_docs(*segment_id0).await.is_empty());
    run_compaction(*segment_id0).await;
    // The compacted segment should be the same as the two rows themselves.
    assert_hashmap_eq(
        &fixture.read_segment_docs(*segment_id0).await,
        &make_compacted_wal_entries(wal_entries0.1),
    );

    // Processing the next transaction ID should re-use this segment and create a new segment
    // because we are both merging into an existing row and creating a new row on a different
    // trace.
    let wal_entries1 = wal_entries[1].clone();
    let mut process_output1 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries1.clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(process_output1.modified_segment_ids.len(), 2);
    assert!(process_output1.modified_segment_ids.remove(segment_id0));
    let segment_id1 = process_output1.modified_segment_ids.iter().next().unwrap();
    assert_eq!(
        fixture
            .config
            .global_store
            .list_segment_ids(&[FullObjectId::default()], None)
            .await
            .unwrap()
            .remove(0)
            .into_iter()
            .collect::<HashSet<_>>(),
        HashSet::from_iter(vec![segment_id0.clone(), segment_id1.clone()])
    );
    assert_eq!(
        fixture.read_segment_wal_entries(*segment_id1).await,
        vec![(TransactionId(1), vec![wal_entries1.1[1].clone()])]
    );
    run_compaction(*segment_id0).await;
    run_compaction(*segment_id1).await;
    assert_hashmap_eq(
        &fixture.read_segment_docs(*segment_id0).await,
        &basic_wal_entries_compacted()
            .into_iter()
            .filter(|(full_row_id, _)| full_row_id.id != "row2")
            .collect(),
    );
    assert_hashmap_eq(
        &fixture.read_segment_docs(*segment_id1).await,
        &basic_wal_entries_compacted()
            .into_iter()
            .filter(|(full_row_id, _)| full_row_id.id == "row2")
            .collect(),
    );
}

#[tokio::test]
async fn test_process_object_wal_loaded_empty() {
    let fixture = TestFixture::new();
    let process_output = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: vec![],
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();

    // We should have no segments created.
    assert_eq!(process_output.modified_segment_ids.len(), 0);

    // If we pass in a non-empty vector with an empty set of wal entries, it should also create no
    // segments.
    let process_output = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: vec![(TransactionId(0), vec![])],
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(process_output.modified_segment_ids.len(), 0);
}

#[tokio::test]
async fn test_process_object_wal_segment_allocation() {
    let fixture = TestFixture::new();
    let wal_entries: Vec<(TransactionId, Vec<WalEntry>)> = vec![
        (
            TransactionId(0),
            vec![WalEntry {
                _pagination_key: PaginationKey(0),
                _xact_id: TransactionId(0),
                id: "row0".to_string(),
                root_span_id: "r0".to_string(),
                ..Default::default()
            }],
        ),
        (
            TransactionId(1),
            vec![
                // Both should end up in the same segment as the original row0.
                WalEntry {
                    _pagination_key: PaginationKey(1),
                    _xact_id: TransactionId(1),
                    id: "row0".to_string(),
                    ..Default::default()
                },
                WalEntry {
                    _pagination_key: PaginationKey(2),
                    _xact_id: TransactionId(1),
                    id: "row1".to_string(),
                    root_span_id: "r0".to_string(),
                    ..Default::default()
                },
                // This one should end up in a different segment.
                WalEntry {
                    _pagination_key: PaginationKey(3),
                    _xact_id: TransactionId(1),
                    id: "row2".to_string(),
                    root_span_id: "r1".to_string(),
                    ..Default::default()
                },
            ],
        ),
        (
            TransactionId(2),
            vec![
                // Should end up in the same segment as the original row0.
                WalEntry {
                    _pagination_key: PaginationKey(4),
                    _xact_id: TransactionId(2),
                    _is_merge: Some(true),
                    id: "row1".to_string(),
                    ..Default::default()
                },
            ],
        ),
        (
            TransactionId(3),
            vec![
                // Should end up in the same segment as the original row0.
                WalEntry {
                    _pagination_key: PaginationKey(6),
                    _xact_id: TransactionId(3),
                    id: "row3".to_string(),
                    root_span_id: "r0".to_string(),
                    ..Default::default()
                },
            ],
        ),
    ];

    // Process each transaction, one at a time.
    let output0 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries[0].clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output0.modified_segment_ids.len(), 1);
    let segment0 = output0.modified_segment_ids.iter().next().unwrap();

    let mut output1 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries[1].clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output1.modified_segment_ids.len(), 2);
    assert!(output1.modified_segment_ids.remove(segment0));
    let segment1 = output1.modified_segment_ids.iter().next().unwrap();

    let output2 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries[2].clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output2.modified_segment_ids.len(), 1);
    assert!(output2.modified_segment_ids.contains(segment0));

    let output3 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries[3].clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output3.modified_segment_ids.len(), 1);
    assert!(output3.modified_segment_ids.contains(segment0));

    compact_segment_wal(
        fixture.compact_segment_wal_input(*segment0),
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    compact_segment_wal(
        fixture.compact_segment_wal_input(*segment1),
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();

    let compacted_segments0 = make_compacted_wal_entries(vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(1),
            id: "row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: TransactionId(2),
            id: "row1".to_string(),
            root_span_id: "r0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(6),
            _xact_id: TransactionId(3),
            id: "row3".to_string(),
            root_span_id: "r0".to_string(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(
        &fixture.read_segment_docs(*segment0).await,
        &compacted_segments0,
    );

    let compacted_segments1 = make_compacted_wal_entries(vec![WalEntry {
        _pagination_key: PaginationKey(3),
        _xact_id: TransactionId(1),
        id: "row2".to_string(),
        root_span_id: "r1".to_string(),
        ..Default::default()
    }]);
    assert_hashmap_eq(
        &fixture.read_segment_docs(*segment1).await,
        &compacted_segments1,
    );
}

#[tokio::test]
async fn test_process_object_wal_change_root_span_id() {
    let fixture = TestFixture::new();
    let wal_entries: Vec<(TransactionId, Vec<WalEntry>)> = vec![
        (
            TransactionId(0),
            vec![WalEntry {
                _pagination_key: PaginationKey(0),
                _xact_id: TransactionId(0),
                id: "row0".to_string(),
                root_span_id: "r0".to_string(),
                ..Default::default()
            }],
        ),
        (
            TransactionId(1),
            vec![
                // Should end up in the original segment.
                WalEntry {
                    _pagination_key: PaginationKey(1),
                    _xact_id: TransactionId(1),
                    id: "row0".to_string(),
                    ..Default::default()
                },
                // Should end up in a different segment.
                WalEntry {
                    _pagination_key: PaginationKey(2),
                    _xact_id: TransactionId(1),
                    id: "row1".to_string(),
                    root_span_id: "r1".to_string(),
                    ..Default::default()
                },
            ],
        ),
        (
            TransactionId(2),
            vec![
                // Publish a new trace for row0. Should end up in the first segment.
                WalEntry {
                    _pagination_key: PaginationKey(3),
                    _xact_id: TransactionId(2),
                    id: "row0".to_string(),
                    root_span_id: "r0_new".to_string(),
                    ..Default::default()
                },
                // New row on the new trace should also go into the first segment.
                WalEntry {
                    _pagination_key: PaginationKey(4),
                    _xact_id: TransactionId(2),
                    id: "row2".to_string(),
                    root_span_id: "r0_new".to_string(),
                    ..Default::default()
                },
                // The conflict between the row segment and the root_span_id segment should be
                // resolved in favor of the row segment (the second one).
                WalEntry {
                    _pagination_key: PaginationKey(5),
                    _xact_id: TransactionId(2),
                    _is_merge: Some(true),
                    id: "row1".to_string(),
                    root_span_id: "r0_new".to_string(),
                    ..Default::default()
                },
            ],
        ),
    ];

    // Process each transaction, one at a time.
    let output0 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries[0].clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output0.modified_segment_ids.len(), 1);
    let segment0 = output0.modified_segment_ids.iter().next().unwrap();

    let mut output1 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries[1].clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output1.modified_segment_ids.len(), 2);
    assert!(output1.modified_segment_ids.remove(segment0));
    let segment1 = output1.modified_segment_ids.iter().next().unwrap();

    let output2 = process_object_wal_loaded_for_testing(
        ProcessObjectWalLoadedInput {
            orig_input: fixture.process_object_wal_input(),
            xact_wal_entries: make_xact_wal_entry_variants(vec![wal_entries[2].clone()]),
        },
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output2.modified_segment_ids.len(), 2);
    assert!(output2.modified_segment_ids.contains(segment0));
    assert!(output2.modified_segment_ids.contains(segment1));

    compact_segment_wal(
        fixture.compact_segment_wal_input(*segment0),
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    compact_segment_wal(
        fixture.compact_segment_wal_input(*segment1),
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();

    let compacted_segments0 = make_compacted_wal_entries(vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(2),
            id: "row0".to_string(),
            root_span_id: "r0_new".to_string(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(4),
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            root_span_id: "r0_new".to_string(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(
        &fixture.read_segment_docs(*segment0).await,
        &compacted_segments0,
    );

    let compacted_segments1 = make_compacted_wal_entries(vec![WalEntry {
        _pagination_key: PaginationKey(2),
        _xact_id: TransactionId(2),
        id: "row1".to_string(),
        root_span_id: "r1".to_string(),
        ..Default::default()
    }]);
    assert_hashmap_eq(
        &fixture.read_segment_docs(*segment1).await,
        &compacted_segments1,
    );
}

#[tokio::test]
async fn test_basic_process_wal() {
    let fixture = TestFixture::new();
    fixture.write_object_wal_entries(basic_wal_entries()).await;

    // Running process one time with xact id bounds should process just the first transaction. This
    // should make up a single segment for both rows.
    let output = process_object_wal(
        fixture.process_object_wal_input(),
        ProcessObjectWalOptionalInput {
            start_xact_id: Some(TransactionId(0)),
            end_xact_id: Some(TransactionId(0)),
            ..Default::default()
        },
        BASIC_PROCESS_OBJECT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output.modified_segment_ids.len(), 1);

    // Check that the last compacted xact id is the first.
    assert_eq!(
        fixture.read_global_last_processed_xact_id().await,
        Some(TransactionId(0))
    );

    // Running again should process the next transaction, with two modified segments.
    let output = process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        BASIC_PROCESS_OBJECT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output.modified_segment_ids.len(), 2);
    assert_eq!(
        fixture.read_global_last_processed_xact_id().await,
        Some(TransactionId(1))
    );

    let all_segment_ids = fixture
        .config
        .global_store
        .list_segment_ids_global(None)
        .await
        .unwrap();

    // Before running compaction, there should be no compacted entries.
    assert!(&fixture
        .read_multi_segment_docs(&all_segment_ids)
        .await
        .is_empty());

    for segment_id in all_segment_ids.iter() {
        compact_segment_wal(
            fixture.compact_segment_wal_input(*segment_id),
            Default::default(),
            BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
        )
        .await
        .unwrap();
    }

    assert_hashmap_eq(
        &fixture.read_multi_segment_docs(&all_segment_ids).await,
        &basic_wal_entries_compacted(),
    );
}

#[tokio::test]
async fn test_process_wal_out_of_order() {
    let fixture = TestFixture::new();

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            ..Default::default()
        },
    ];
    let grouped_wal_entries = group_wal_entries(wal_entries.clone());

    fixture.write_object_wal_entries(wal_entries.clone()).await;
    let segment_wal = fixture.make_segment_wal();

    let process_wal_options = ProcessObjectWalOptions {
        max_rows_per_segment: 1000,
        ..Default::default()
    };

    // Process transactions 1-2 first.
    let output = process_object_wal(
        fixture.process_object_wal_input(),
        ProcessObjectWalOptionalInput {
            start_xact_id: Some(TransactionId(1)),
            end_xact_id: Some(TransactionId(2)),
            ..Default::default()
        },
        process_wal_options.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output.modified_segment_ids.len(), 1);
    let segment_id = output.modified_segment_ids.iter().next().unwrap();
    let wal_scope = WALScope::Segment(*segment_id);

    assert_eq!(
        fixture.read_global_last_processed_xact_id().await,
        Some(TransactionId(2))
    );
    assert_eq!(
        grouped_wal_entries[1..].to_vec(),
        collect_wal_stream(wal_stream(
            segment_wal
                .wal_metadata_stream(wal_scope, Default::default())
                .await
                .unwrap(),
            Default::default()
        ))
        .await
        .unwrap()
    );

    // Process transactions 0-1.
    let output = process_object_wal(
        fixture.process_object_wal_input(),
        ProcessObjectWalOptionalInput {
            start_xact_id: Some(TransactionId(0)),
            end_xact_id: Some(TransactionId(1)),
            ..Default::default()
        },
        process_wal_options.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output.modified_segment_ids.len(), 1);
    assert_eq!(
        output.modified_segment_ids.iter().next().unwrap(),
        segment_id
    );
    assert_eq!(
        fixture.read_global_last_processed_xact_id().await,
        Some(TransactionId(2))
    );
    assert_eq!(
        grouped_wal_entries,
        collect_wal_stream(wal_stream(
            segment_wal
                .wal_metadata_stream(wal_scope, Default::default())
                .await
                .unwrap(),
            Default::default()
        ))
        .await
        .unwrap()
    );

    // Processing again should not do anything.
    let output = process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        process_wal_options.clone(),
    )
    .await
    .unwrap();
    assert_eq!(output.modified_segment_ids.len(), 0);
    assert_eq!(
        fixture.read_global_last_processed_xact_id().await,
        Some(TransactionId(2))
    );
    assert_eq!(
        grouped_wal_entries,
        collect_wal_stream(wal_stream(
            segment_wal
                .wal_metadata_stream(wal_scope.clone(), Default::default())
                .await
                .unwrap(),
            Default::default()
        ))
        .await
        .unwrap()
    );
}

#[tokio::test]
async fn test_compact_with_deletes() {
    let fixture = TestFixture::new();

    let wal_entries = vec![
        WalEntry {
            root_span_id: "0".to_string(),
            _xact_id: TransactionId(0),
            ..Default::default()
        },
        WalEntry {
            root_span_id: "1".to_string(),
            _xact_id: TransactionId(1),
            _is_merge: Some(true),
            ..Default::default()
        },
        WalEntry {
            root_span_id: "2".to_string(),
            _xact_id: TransactionId(2),
            _is_merge: Some(true),
            _object_delete: Some(true),
            data: json!({
                "field1": "foo",
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            root_span_id: "3".to_string(),
            _xact_id: TransactionId(3),
            _is_merge: Some(true),
            data: json!({
                "field2": 100,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ];

    // For each transaction, we run one step of process-and-compact up to that transaction ID and
    // then process-and-compact the rest. We should end up with the same result each time.
    for split_ind in 1..wal_entries.len() {
        let segment_id = Uuid::new_v4();
        fixture.initialize_segment_metadata(segment_id).await;
        fixture
            .write_wal_to_segment(segment_id, wal_entries[..split_ind].to_vec())
            .await;
        compact_segment_wal(
            fixture.compact_segment_wal_input(segment_id),
            Default::default(),
            BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
        )
        .await
        .unwrap();

        // If we are on the delete entry, also check that there is no entry in the segment.
        if split_ind == 3 {
            assert!(fixture.read_segment_docs(segment_id).await.is_empty());
        }

        fixture
            .write_wal_to_segment(segment_id, wal_entries[split_ind..].to_vec())
            .await;
        compact_segment_wal(
            fixture.compact_segment_wal_input(segment_id),
            Default::default(),
            BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
        )
        .await
        .unwrap();

        assert_hashmap_eq(
            &fixture.read_segment_docs(segment_id).await,
            &make_compacted_wal_entries(vec![WalEntry {
                root_span_id: "3".to_string(),
                _xact_id: TransactionId(3),
                data: json!({
                    "field2": 100,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            }]),
        );
    }
}

#[tokio::test]
async fn test_process_object_wal_loaded_allocation_heuristic() {
    let fixture = TestFixture::new();
    let process_wal_options1 = ProcessObjectWalLoadedOptions {
        max_rows_per_segment: 1,
    };
    let process_wal_options2 = ProcessObjectWalLoadedOptions {
        max_rows_per_segment: 2,
    };

    // This test validates our heuristic for assigning new rows to existing segments. We should
    // choose to assign to segments in order of greatest min_pagination_key to least. We test this
    // by creating two segments with one row each. Then we bump up the max_rows_per_segment to 2,
    // and try adding a new row. It should end up in the later segment. Then adding another row
    // should end up in the earlier segment. Finally, adding a new row should create a new segment.

    let make_wal_entries = |idx: usize| -> Vec<(TransactionId, Vec<WalEntry>)> {
        vec![(
            TransactionId(idx as u64),
            vec![WalEntry {
                _pagination_key: PaginationKey(idx as u64),
                _xact_id: TransactionId(idx as u64),
                id: format!("row{}", idx),
                root_span_id: format!("r{}", idx),
                ..Default::default()
            }],
        )]
    };

    let wal_entries0 = make_wal_entries(0);
    let wal_entries1 = make_wal_entries(1);
    let wal_entries2 = make_wal_entries(2);
    let wal_entries3 = make_wal_entries(3);
    let wal_entries4 = make_wal_entries(4);

    let run_process = |xact_wal_entries: Vec<(TransactionId, Vec<WalEntry>)>,
                       process_wal_options: ProcessObjectWalLoadedOptions| {
        let orig_input = fixture.process_object_wal_input();
        async move {
            let output = process_object_wal_loaded_for_testing(
                ProcessObjectWalLoadedInput {
                    orig_input,
                    xact_wal_entries: make_xact_wal_entry_variants(xact_wal_entries),
                },
                Default::default(),
                process_wal_options,
            )
            .await
            .unwrap();
            *output.modified_segment_ids.iter().next().unwrap()
        }
    };

    let seg0 = run_process(wal_entries0.clone(), process_wal_options1.clone()).await;
    let seg1 = run_process(wal_entries1.clone(), process_wal_options1.clone()).await;
    assert_ne!(seg0, seg1);

    // Should add to seg1.
    let seg2 = run_process(wal_entries2.clone(), process_wal_options2.clone()).await;
    assert_eq!(seg2, seg1);

    // Should add to seg0.
    let seg3 = run_process(wal_entries3.clone(), process_wal_options2.clone()).await;
    assert_eq!(seg3, seg0);

    // Should add to a new segment.
    let seg4 = run_process(wal_entries4.clone(), process_wal_options2.clone()).await;
    assert_ne!(seg4, seg0);
    assert_ne!(seg4, seg1);
}

#[derive(Debug, Copy, Clone)]
enum PosioningDeletesRepairMode {
    WipeOpstampFile,
    Recompact,
}

// In the past we have observed crashes where an un-comitted `.del` file from a previous compaction
// breaks the next compaction when it tries to create a file with the same opstamp. We reproduce
// the issue here and make sure it doesn't crash.
//
// This harness induces the issue and validates two different ways of fixing the issue: using the
// `wipe_opstamp_file` option, and using the `recompact` option.
async fn test_tantivy_index_poisoning_deletes_harness(mode: PosioningDeletesRepairMode) {
    let fixture = TestFixture::new();
    // Add several rows to ensure that deleting one row doesn't wipe the whole segment.
    fixture
        .write_object_wal_entries(vec![
            WalEntry {
                _xact_id: TransactionId(0),
                id: "row0".to_string(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(0),
                id: "row1".to_string(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(0),
                id: "row2".to_string(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(0),
                id: "row3".to_string(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(0),
                id: "row4".to_string(),
                ..Default::default()
            },
        ])
        .await;
    let segment_id = process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap()
    .modified_segment_ids
    .into_iter()
    .next()
    .unwrap();
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    let orig_tantivy_segments = {
        let meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        meta.segments
            .into_iter()
            .map(|s| s.segment_id)
            .collect::<HashSet<_>>()
    };

    {
        // Construct the tantivy index wrapper with a cold directory, so that we don't pollute the
        // cached directory namespace with files that we'll later delete. The cached directory
        // seems bad at handling files created with `open_write` that are later deleted and then
        // re-written, but this should not really happen in prod, since each compaction gets its
        // own cached directory.
        let schema = make_full_schema(&fixture.schema).unwrap();
        let tantivy_index_wrapper = ReadWriteTantivyIndexWrapper::new(
            // If we replace this with fixture.config.index.directory.clone(),
            // this test should fail intermittently.
            //fixture.config.index.directory.clone(),
            crate::directory::AsyncDirectoryArc::new(
                crate::directory::object_store_directory::ObjectStoreDirectory::new(
                    fixture.config.index.store.clone(),
                ),
            ),
            schema,
            fixture.index_prefix(),
            &TantivyIndexScope::Segment(segment_id),
        )
        .await
        .unwrap();
        let full_row_id_field = tantivy_index_wrapper
            .tantivy_schema()
            .get_field("_full_row_id")
            .unwrap();
        await_spawn_blocking!(move || {
            let make_term = |row_id: &str| {
                tantivy::schema::Term::from_field_text(
                    full_row_id_field,
                    &FullRowIdOwned {
                        id: row_id.to_string(),
                        ..Default::default()
                    }
                    .to_string(),
                )
            };
            let mut writer = tantivy_index_wrapper
                .make_writer_blocking(&Default::default())
                .unwrap();
            writer.delete_term(make_term("row0"));
            writer.delete_term(make_term("row1"));
            writer.commit().unwrap();
            writer.delete_term(make_term("row2"));
            writer.commit().unwrap();
        })
        .unwrap()
        .unwrap();
    }

    fixture
        .write_object_wal_entries(vec![WalEntry {
            _xact_id: TransactionId(1),
            _object_delete: Some(true),
            id: "row3".to_string(),
            ..Default::default()
        }])
        .await;
    process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    match mode {
        PosioningDeletesRepairMode::WipeOpstampFile => {
            // In this mode, run compaction with the default options. The "opstamp file" deletion
            // should kick in and let compaction proceed successfully.
            compact_segment_wal(
                fixture.compact_segment_wal_input(segment_id),
                Default::default(),
                COMPACT_SEGMENT_WAL_NO_MERGES_OPTS.clone(),
            )
            .await
            .unwrap();
        }
        PosioningDeletesRepairMode::Recompact => {
            // To force recompaction, allow 0 retries for opstamp deletion, so we have to fall back
            // to recompaction. This should result in the segment ids all being different.
            compact_segment_wal(
                fixture.compact_segment_wal_input(segment_id),
                CompactSegmentWalOptionalInput {
                    testing_override_missing_del_file_retries: Some(0),
                    ..Default::default()
                },
                COMPACT_SEGMENT_WAL_NO_MERGES_OPTS.clone(),
            )
            .await
            .unwrap();
        }
    };

    let new_tantivy_segments = {
        let meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        meta.segments
            .into_iter()
            .map(|s| s.segment_id)
            .collect::<HashSet<_>>()
    };

    match mode {
        PosioningDeletesRepairMode::WipeOpstampFile => {
            // We should have preserved all the original segments.
            assert!(
                orig_tantivy_segments.is_subset(&new_tantivy_segments),
                "orig {:?} new: {:?}",
                orig_tantivy_segments,
                new_tantivy_segments
            );
        }
        PosioningDeletesRepairMode::Recompact => {
            // We should have no segments in common.
            assert!(
                orig_tantivy_segments.is_disjoint(&new_tantivy_segments),
                "orig {:?} new: {:?}",
                orig_tantivy_segments,
                new_tantivy_segments
            );
        }
    };

    let all_docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(
        all_docs
            .keys()
            .map(|x| x.id.clone())
            .collect::<HashSet<_>>(),
        ["row0", "row1", "row2", "row4"]
            .iter()
            .map(|x| x.to_string())
            .collect()
    );
}

#[tokio::test]
async fn test_tantivy_index_poisoning_deletes_with_wipe_opstamp() {
    test_tantivy_index_poisoning_deletes_harness(PosioningDeletesRepairMode::WipeOpstampFile).await;
}

#[tokio::test]
async fn test_tantivy_index_poisoning_deletes_with_recompact() {
    test_tantivy_index_poisoning_deletes_harness(PosioningDeletesRepairMode::Recompact).await;
}

#[tokio::test]
async fn test_recompact_corrupted_index_missing_del() {
    let fixture = TestFixture::new();

    // Write several wal entries to create a segment.
    let segment_id = {
        let mut wal_entries = vec![];
        for i in 0..10 {
            wal_entries.push(WalEntry {
                id: format!("row{}", i),
                _xact_id: TransactionId(0),
                ..Default::default()
            });
        }
        fixture.write_object_wal_entries(wal_entries).await;
        process_object_wal(
            fixture.process_object_wal_input(),
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap()
        .modified_segment_ids
        .into_iter()
        .next()
        .unwrap()
    };
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();
    let orig_tantivy_segments = {
        let meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        meta.segments
            .into_iter()
            .map(|s| s.segment_id)
            .collect::<HashSet<_>>()
    };
    assert_eq!(orig_tantivy_segments.len(), 1);

    // Now write a single wal entry update, which should create a delete entry in that segment.
    fixture
        .write_object_wal_entries(vec![WalEntry {
            id: format!("row{}", 0),
            _xact_id: TransactionId(1),
            ..Default::default()
        }])
        .await;
    process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Now delete all `.del` files in the segment.
    {
        let segment_dir = TantivyIndexScope::Segment(segment_id).path(&fixture.config.index.prefix);
        let segment_dir_path =
            object_store::path::Path::from(segment_dir.to_string_lossy().into_owned());
        let mut files_to_delete = fixture.config.index.store.list(Some(&segment_dir_path));
        while let Some(file) = files_to_delete.next().await {
            let file = file.unwrap();
            if file.location.to_string().ends_with(".del") {
                fixture
                    .config
                    .index
                    .store
                    .delete(&file.location)
                    .await
                    .unwrap();
            }
        }
    }

    // Now if we write a new wal entry and try to compact, it should succeed,
    // but will recompact the segment.
    fixture
        .write_object_wal_entries(vec![WalEntry {
            id: format!("row{}", 10),
            _xact_id: TransactionId(2),
            ..Default::default()
        }])
        .await;
    process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Check that the set of segments is disjoint from the original set.
    let new_tantivy_segments = {
        let meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        meta.segments
            .into_iter()
            .map(|s| s.segment_id)
            .collect::<HashSet<_>>()
    };
    assert_eq!(new_tantivy_segments.len(), 1);
    assert!(
        orig_tantivy_segments.is_disjoint(&new_tantivy_segments),
        "orig {:?} new: {:?}",
        orig_tantivy_segments,
        new_tantivy_segments
    );
}

#[tokio::test]
async fn test_pagination_key_statistics() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;
    fixture
        .write_wal_to_segment(segment_id, basic_wal_entries())
        .await;

    // First compact with testing_skip_field_statistics = true
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            testing_skip_field_statistics: true,
            ..Default::default()
        },
        Default::default(),
    )
    .await
    .unwrap();

    // Verify no field statistics exist
    let field_statistics = fixture
        .config
        .global_store
        .query_field_statistics(&[segment_id], &FIELD_STATISTICS_FIELD_NAMES)
        .await
        .unwrap();
    assert!(field_statistics.is_empty());

    // Re-compact without skipping field statistics
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Verify field statistics now exist and match expected values
    let field_statistics = fixture
        .config
        .global_store
        .query_field_statistics(&[segment_id], &FIELD_STATISTICS_FIELD_NAMES)
        .await
        .unwrap();
    assert_eq!(
        field_statistics[&segment_id],
        [
            (
                "_pagination_key".to_string(),
                SegmentFieldStatistics::new(0, 3).unwrap()
            ),
            (
                "created".to_string(),
                SegmentFieldStatistics::new(
                    MonotonicallyMappableToU64::to_u64(1000_i64),
                    MonotonicallyMappableToU64::to_u64(4000_i64)
                )
                .unwrap()
            ),
            (
                XACT_ID_FIELD.to_string(),
                SegmentFieldStatistics::new(
                    MonotonicallyMappableToU64::to_u64(0_u64),
                    MonotonicallyMappableToU64::to_u64(1_u64),
                )
                .unwrap(),
            ),
        ]
        .into_iter()
        .collect()
    );
}

#[tokio::test]
async fn test_skip_metadata_update_for_standalone_comments() {
    let fixture = TestFixture::new();

    // Create WAL entries with a mix of regular entries and standalone comments
    let regular_entries = vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(1),
            id: "row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(2),
            id: "row1".to_string(),
            ..Default::default()
        },
    ];

    // Create a batch of standalone comments at a higher transaction ID
    let comment_entries = vec![
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: TransactionId(3),
            _is_standalone_comment: Some(true),
            id: "row0".to_string(),
            comments: WalEntryComments(vec![WalEntryComment {
                _xact_id: TransactionId(3),
                id: "comment0".to_string(),
                ..Default::default()
            }]),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(3),
            _is_standalone_comment: Some(true),
            id: "row1".to_string(),
            comments: WalEntryComments(vec![WalEntryComment {
                _xact_id: TransactionId(3),
                id: "comment1".to_string(),
                ..Default::default()
            }]),
            ..Default::default()
        },
    ];

    // Write the regular entries first
    fixture
        .write_object_wal_entries(regular_entries.clone())
        .await;

    // Process the WAL
    process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Check that the last processed xact id is 2
    assert_eq!(
        fixture.read_global_last_processed_xact_id().await,
        Some(TransactionId(2))
    );

    let segment_ids = fixture
        .config
        .global_store
        .list_segment_ids(&[FullObjectId::default()], None)
        .await
        .unwrap()
        .remove(0);
    assert!(!segment_ids.is_empty());
    let segment_id = segment_ids[0];

    // Compact the WAL so that the next compaction is only comments.
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Now write the comment entries
    fixture
        .write_object_wal_entries(comment_entries.clone())
        .await;

    // Process the WAL again
    process_object_wal(
        fixture.process_object_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Check that the last processed xact id is still 2, not updated to 3
    assert_eq!(
        fixture.read_global_last_processed_xact_id().await,
        Some(TransactionId(2))
    );

    // Verify that the comment entries are actually present in the WAL
    // even though the metadata wasn't updated
    let wal = fixture.make_object_wal();
    let wal_scope = WALScope::ObjectId(FullObjectId::default(), fixture.wal_token().await);
    let wal_entries = collect_wal_stream(wal_stream(
        wal.wal_metadata_stream(wal_scope, Default::default())
            .await
            .unwrap(),
        Default::default(),
    ))
    .await
    .unwrap();

    // Find the entries with xact_id 3 (the comments)
    let xact_3_entries: Vec<_> = wal_entries
        .iter()
        .filter(|(xact_id, _)| *xact_id == TransactionId(3))
        .collect();

    // Verify there is exactly one transaction with ID 3
    assert_eq!(xact_3_entries.len(), 1);

    // Verify the transaction contains the comment entries
    let comment_entries_in_wal = &xact_3_entries[0].1;
    assert_eq!(comment_entries_in_wal.len(), 2);
    assert!(comment_entries_in_wal
        .iter()
        .all(|entry| entry._is_standalone_comment.unwrap_or(false)));

    // Compact the segment
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();

    // Check the last compacted xact id
    let segment_metadata = fixture
        .config
        .global_store
        .query_segment_metadatas(&[segment_id])
        .await
        .unwrap()
        .remove(0);

    // It should be 2, not 3, since transaction 3 only contained standalone comments
    assert_eq!(
        segment_metadata
            .last_compacted_index_meta
            .map(|meta| meta.xact_id),
        Some(TransactionId(2))
    );

    // But the comments should be in the compacted WAL.
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(
            regular_entries
                .iter()
                .chain(comment_entries.iter())
                .cloned()
                .collect(),
        ),
    );
}

#[tokio::test]
async fn test_compact_segment_wal_updated_schema() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;
    fixture
        .write_wal_to_segment(segment_id, basic_wal_entries())
        .await;
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &basic_wal_entries_compacted(),
    );

    // Now create add a field to the schema, write a new WAL entry with the new field, and compact
    // it. Check that we can recover the original entries as well as the new one.
    let new_schema = {
        let orig_schema = basic_schema();
        let mut new_fields = orig_schema.fields().clone();
        let field1 = new_fields[0].clone();
        new_fields.push(Field {
            name: "field4".to_string(),
            tantivy: vec![TantivyField {
                name: "field4".to_string(),
                field_ts: u64::MAX - 1,
                ..field1.tantivy[0].clone()
            }],
        });
        Schema::new(orig_schema.name().clone(), new_fields, Default::default()).unwrap()
    };

    let new_entry = WalEntry {
        _pagination_key: PaginationKey(10),
        _xact_id: TransactionId(2),
        id: "row_new".to_string(),
        data: json!({
            "field4": "new",
        })
        .as_object()
        .unwrap()
        .clone(),
        ..Default::default()
    };
    fixture
        .write_wal_to_segment(segment_id, vec![new_entry.clone()])
        .await;
    compact_segment_wal(
        CompactSegmentWalInput {
            schema: make_full_schema(&new_schema).unwrap(),
            ..fixture.compact_segment_wal_input(segment_id)
        },
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    {
        let schema = make_full_schema(&new_schema).unwrap();
        let tantivy_index_wrapper = make_readonly_tantivy_index_wrapper(
            fixture.config.global_store.clone(),
            &fixture.config.index,
            schema,
            &[segment_id],
        )
        .await
        .unwrap();
        let mut expected = basic_wal_entries_compacted();
        expected.insert(
            new_entry.full_row_id().to_owned(),
            IndexDocument {
                wal_entry: new_entry.clone(),
            }
            .to_sanitized()
            .unwrap(),
        );
        assert_hashmap_eq(
            &fixture
                .read_segment_docs_with_index_wrapper(tantivy_index_wrapper)
                .await,
            &expected,
        );
    }
}

#[tokio::test]
async fn test_compact_segment_wal_reject_invalid_schema_update() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let base_schema = {
        let orig_schema = basic_schema();
        let mut new_fields = orig_schema.fields().clone();
        let field1 = new_fields[0].clone();
        new_fields.push(Field {
            name: "field4".to_string(),
            tantivy: vec![TantivyField {
                name: "field4".to_string(),
                field_ts: u64::MAX - 1,
                ..field1.tantivy[0].clone()
            }],
        });
        Schema::new(orig_schema.name().clone(), new_fields, Default::default()).unwrap()
    };

    let base_entry = WalEntry {
        _pagination_key: PaginationKey(10),
        _xact_id: TransactionId(2),
        id: "base_row".to_string(),
        data: json!({
            "field4": "data",
        })
        .as_object()
        .unwrap()
        .clone(),
        ..Default::default()
    };

    fixture
        .write_wal_to_segment(segment_id, vec![base_entry.clone()])
        .await;

    compact_segment_wal(
        CompactSegmentWalInput {
            schema: make_full_schema(&base_schema).unwrap(),
            ..fixture.compact_segment_wal_input(segment_id)
        },
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();
    assert_hashmap_eq(
        &{
            let schema = make_full_schema(&base_schema).unwrap();
            let tantivy_index_wrapper = make_readonly_tantivy_index_wrapper(
                fixture.config.global_store.clone(),
                &fixture.config.index,
                schema,
                &[segment_id],
            )
            .await
            .unwrap();
            fixture
                .read_segment_docs_with_index_wrapper(tantivy_index_wrapper)
                .await
        },
        &make_compacted_wal_entries(vec![base_entry.clone()]),
    );

    // If we change the field name to something else, the compaction should get rejected.
    let modified_schema_bad = {
        let orig_schema = base_schema.clone();
        let mut new_fields = orig_schema.fields().clone();
        new_fields.last_mut().unwrap().name = "field5".to_string();
        new_fields.last_mut().unwrap().tantivy[0].name = "field5".to_string();
        Schema::new(orig_schema.name().clone(), new_fields, Default::default()).unwrap()
    };

    let modified_entry_bad = WalEntry {
        _pagination_key: PaginationKey(11),
        _xact_id: TransactionId(3),
        id: "modified_entry".to_string(),
        data: json!({
            "field5": "data",
        })
        .as_object()
        .unwrap()
        .clone(),
        ..Default::default()
    };

    fixture
        .write_wal_to_segment(segment_id, vec![modified_entry_bad.clone()])
        .await;
    assert!(compact_segment_wal(
        CompactSegmentWalInput {
            schema: make_full_schema(&modified_schema_bad).unwrap(),
            ..fixture.compact_segment_wal_input(segment_id)
        },
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .is_err());

    // But as a special case, if the schema is named "audit_data" or "_async_scoring_state", we
    // will re-compact it with the new field. This should have the effect of clearing out the data
    // field in all the wal entries.
    for field_name in ["audit_data", "_async_scoring_state"] {
        let modified_schema_ok = {
            let orig_schema = base_schema.clone();
            let mut new_fields = orig_schema.fields().clone();
            new_fields.last_mut().unwrap().name = field_name.to_string();
            new_fields.last_mut().unwrap().tantivy[0].name = field_name.to_string();
            Schema::new(orig_schema.name().clone(), new_fields, Default::default()).unwrap()
        };
        let orig_tantivy_segments = {
            let meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
            meta.segments
                .into_iter()
                .map(|s| s.segment_id)
                .collect::<HashSet<_>>()
        };
        compact_segment_wal(
            CompactSegmentWalInput {
                schema: make_full_schema(&modified_schema_ok).unwrap(),
                ..fixture.compact_segment_wal_input(segment_id)
            },
            Default::default(),
            BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
        )
        .await
        .unwrap();
        let new_tantivy_segments = {
            let meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
            meta.segments
                .into_iter()
                .map(|s| s.segment_id)
                .collect::<HashSet<_>>()
        };
        assert!(
            orig_tantivy_segments.is_disjoint(&new_tantivy_segments),
            "orig {:?} new: {:?}",
            orig_tantivy_segments,
            new_tantivy_segments
        );

        let mut expected_docs = vec![base_entry.clone(), modified_entry_bad.clone()];
        for doc in expected_docs.iter_mut() {
            doc.data.clear();
        }
        assert_hashmap_eq(
            &{
                let schema = make_full_schema(&modified_schema_ok).unwrap();
                let tantivy_index_wrapper = make_readonly_tantivy_index_wrapper(
                    fixture.config.global_store.clone(),
                    &fixture.config.index,
                    schema,
                    &[segment_id],
                )
                .await
                .unwrap();
                fixture
                    .read_segment_docs_with_index_wrapper(tantivy_index_wrapper)
                    .await
            },
            &make_compacted_wal_entries(expected_docs),
        );
    }
}

#[tokio::test]
async fn test_compact_segment_wal_update_compacted_when_recompacting() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries0 = vec![WalEntry {
        _pagination_key: PaginationKey(0),
        _xact_id: TransactionId(1),
        id: "row0".to_string(),
        ..Default::default()
    }];
    fixture
        .write_wal_to_segment(segment_id, wal_entries0.clone())
        .await;

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        BASIC_COMPACT_SEGMENT_WAL_OPTIONS.clone(),
    )
    .await
    .unwrap();

    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(wal_entries0.clone()),
    );

    // Everything should have been compacted.
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_min_uncompacted_xact_id(&[segment_id])
            .await
            .unwrap()
            .remove(0),
        None
    );
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_max_compacted_xact_id(&[segment_id])
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );

    // Now add a WAL entry at TransactionId(0) and re-run compaction. But use a target batch size
    // of 1, so that we process just one WAL entry per batch.
    let wal_entries1 = vec![WalEntry {
        _pagination_key: PaginationKey(1),
        _xact_id: TransactionId(0),
        id: "row1".to_string(),
        ..Default::default()
    }];
    fixture
        .write_wal_to_segment(segment_id, wal_entries1.clone())
        .await;

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        CompactSegmentWalOptions {
            wal_stream_options: CompactSegmentWalStreamOptions {
                compact_segment_wal_target_batch_size: 1,
                ..Default::default()
            },
            ..Default::default()
        },
    )
    .await
    .unwrap();

    // Everything should have been compacted.
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_min_uncompacted_xact_id(&[segment_id])
            .await
            .unwrap()
            .remove(0),
        None
    );
    assert_eq!(
        fixture
            .config
            .global_store
            .query_segment_wal_max_compacted_xact_id(&[segment_id])
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );

    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(vec![wal_entries0[0].clone(), wal_entries1[0].clone()]),
    );
}

#[tokio::test]
async fn test_compact_segment_wal_clear_and_recompact() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries0 = vec![WalEntry {
        _pagination_key: PaginationKey(0),
        _xact_id: TransactionId(1),
        id: "row0".to_string(),
        ..Default::default()
    }];
    fixture
        .write_wal_to_segment(segment_id, wal_entries0.clone())
        .await;

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(wal_entries0.clone()),
    );

    // Grab the set of tantivy segments before we clear and recompact.
    let tantivy_meta_before = {
        let index_wrapper = fixture.make_tantivy_index_wrapper(segment_id).await;
        let index_meta = await_spawn_blocking!(move || { index_wrapper.index.load_metas() })
            .unwrap()
            .unwrap()
            .unwrap();
        index_meta
            .segments
            .into_iter()
            .map(|s| s.id())
            .collect::<HashSet<_>>()
    };

    clear_compacted_index(ClearCompactedIndexInput {
        segment_id,
        global_store: fixture.config.global_store.clone(),
        locks_manager: &*fixture.config.locks_manager,
    })
    .await
    .unwrap();

    // Now recompact and check we're still good.
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(wal_entries0.clone()),
    );

    // Make sure the set of tantivy index segments is completely different.
    let tantivy_meta_after = {
        let index_wrapper = fixture.make_tantivy_index_wrapper(segment_id).await;
        let index_meta = await_spawn_blocking!(move || { index_wrapper.index.load_metas() })
            .unwrap()
            .unwrap()
            .unwrap();
        index_meta
            .segments
            .into_iter()
            .map(|s| s.id())
            .collect::<HashSet<_>>()
    };

    assert!(tantivy_meta_before.is_disjoint(&tantivy_meta_after));

    // Delete the index meta file and read the segment docs again. It should still work
    // because `read_segment_docs` uses the global store index meta to read from the
    // tantivy index, not the meta.json in the object store.
    let index_directory = fixture.config.index.directory.clone();
    let index_path = TantivyIndexScope::Segment(segment_id).path(&fixture.config.index.prefix);
    let meta_json_path = index_path.join("meta.json");
    index_directory.async_delete(&meta_json_path).await.unwrap();
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(wal_entries0.clone()),
    );
}

#[tokio::test]
async fn test_compact_segment_wal_empty_segment_recompute_field_statistics() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries0 = vec![WalEntry {
        _xact_id: TransactionId(0),
        id: "row0".to_string(),
        ..Default::default()
    }];
    fixture
        .write_wal_to_segment(segment_id, wal_entries0.clone())
        .await;

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            testing_skip_field_statistics: true,
            ..Default::default()
        },
        Default::default(),
    )
    .await
    .unwrap();
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &make_compacted_wal_entries(wal_entries0.clone()),
    );

    // Now write a deletion entry, and clear the compacted index to force full
    // recompaction. Then re-compact, still skipping field statistics.
    let wal_entries1 = vec![WalEntry {
        _xact_id: TransactionId(1),
        id: "row0".to_string(),
        _object_delete: Some(true),
        ..Default::default()
    }];
    fixture
        .write_wal_to_segment(segment_id, wal_entries1.clone())
        .await;
    clear_compacted_index(ClearCompactedIndexInput {
        segment_id,
        global_store: fixture.config.global_store.clone(),
        locks_manager: &*fixture.config.locks_manager,
    })
    .await
    .unwrap();

    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            testing_skip_field_statistics: true,
            ..Default::default()
        },
        Default::default(),
    )
    .await
    .unwrap();
    assert_hashmap_eq(
        &fixture.read_segment_docs(segment_id).await,
        &HashMap::new(),
    );

    // The segment should have no field statistics.
    let field_statistics = fixture
        .config
        .global_store
        .query_field_statistics(&[segment_id], &FIELD_STATISTICS_FIELD_NAMES)
        .await
        .unwrap();
    assert!(field_statistics.iter().all(|(_, stats)| stats.is_empty()));

    // But if we recompact the empty segment, it should get statistics.
    compact_segment_wal(
        fixture.compact_segment_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await
    .unwrap();
    let field_statistics = fixture
        .config
        .global_store
        .query_field_statistics(&[segment_id], &FIELD_STATISTICS_FIELD_NAMES)
        .await
        .unwrap();
    let segment_field_statistics = field_statistics.get(&segment_id).unwrap();
    for field_name in FIELD_STATISTICS_FIELD_NAMES {
        assert!(
            segment_field_statistics.get(field_name).is_some(),
            "field_name: {}",
            field_name
        );
    }
}

// Note: this test is intended to demonstrate corrupting the segments by
// inserting the same data into two new segments concurrently. It shouldn't
// happen if our locking implementation works reliably, but in the past we've
// seen issues with flaky redis locks. So preserving this test to encode the
// current state of affairs.
#[tokio::test]
async fn test_process_segment_wal_no_locking_multiple_new_segments() {
    let fixture = TestFixture::new();
    let wal_entries = group_wal_entries(vec![WalEntry {
        _pagination_key: PaginationKey(0),
        _xact_id: TransactionId(0),
        created: DateTime::<Utc>::from_timestamp_nanos(1000),
        id: "row0".to_string(),
        data: json!({
            "field1": "foo",
            "field3": json!({ "input": "bar" }),
        })
        .as_object()
        .unwrap()
        .clone(),
        ..Default::default()
    }]);
    let mut config = fixture.config.clone();
    config.locks_manager = Arc::new(NoopGlobalLocksManager::default());

    // Spawn two process tasks concurrently. Make sure both of them finish
    // collecting segment infos before continuing.
    let (after_collect_segment_infos0_sw, after_collect_segment_infos0_ws) = two_way_sync_point();
    let (after_collect_segment_infos1_sw, after_collect_segment_infos1_ws) = two_way_sync_point();

    let process_output0 = {
        let config = config.clone();
        let xact_wal_entries = make_xact_wal_entry_variants(wal_entries.clone());
        let optional_input = ProcessObjectWalLoadedOptionalInput {
            testing_sync_points: ProcessObjectWalLoadedTestingSyncPoints {
                after_collect_segment_infos: Some(after_collect_segment_infos0_sw),
                ..Default::default()
            },
            ..Default::default()
        };
        let options = BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone();
        tokio::spawn(async move {
            let input = ProcessObjectWalLoadedInput {
                orig_input: ProcessObjectWalInput {
                    object_id: FullObjectId::default(),
                    config: &config,
                },
                xact_wal_entries,
            };
            process_object_wal_loaded_for_testing(input, optional_input, options).await
        })
    };
    let process_output1 = {
        let config = config.clone();
        let xact_wal_entries = make_xact_wal_entry_variants(wal_entries.clone());
        let optional_input = ProcessObjectWalLoadedOptionalInput {
            testing_sync_points: ProcessObjectWalLoadedTestingSyncPoints {
                after_collect_segment_infos: Some(after_collect_segment_infos1_sw),
                ..Default::default()
            },
            ..Default::default()
        };
        let options = BASIC_PROCESS_OBJECT_WAL_LOADED_OPTIONS.clone();
        tokio::spawn(async move {
            let input = ProcessObjectWalLoadedInput {
                orig_input: ProcessObjectWalInput {
                    object_id: FullObjectId::default(),
                    config: &config,
                },
                xact_wal_entries,
            };
            process_object_wal_loaded_for_testing(input, optional_input, options).await
        })
    };

    // Wait for both the sync points before continuing.
    let after_collect_segment_infos0_send = after_collect_segment_infos0_ws.wait().await;
    let after_collect_segment_infos1_send = after_collect_segment_infos1_ws.wait().await;
    after_collect_segment_infos0_send.send();
    after_collect_segment_infos1_send.send();

    let (res0, res1) = join!(process_output0, process_output1);
    let segment_id0 = *res0
        .unwrap()
        .unwrap()
        .modified_segment_ids
        .iter()
        .next()
        .unwrap();
    let segment_id1 = *res1
        .unwrap()
        .unwrap()
        .modified_segment_ids
        .iter()
        .next()
        .unwrap();
    assert_ne!(segment_id0, segment_id1);

    let live_segment_ids = fixture
        .config
        .global_store
        .list_segment_ids(&[FullObjectId::default()], None)
        .await
        .unwrap()
        .remove(0);
    assert_eq!(
        live_segment_ids.into_iter().collect::<HashSet<_>>(),
        [segment_id0, segment_id1].into_iter().collect()
    );

    // But if we try to query the row ID memberships across segments, we should get an error.
    let result = fixture
        .config
        .global_store
        .query_id_segment_membership(
            IdSegmentMembershipType::RowId,
            &[segment_id0, segment_id1],
            &[wal_entries[0].1[0].full_row_id()],
        )
        .await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_process_segment_wal_empty() {
    let fixture = TestFixture::new();
    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;
    fixture
        .run_process_segment_wal(
            ProcessSegmentWalInput {
                orig_input: fixture.process_object_wal_input(),
                segment_id,
                xact_wal_entries: vec![],
                is_new_segment: false,
            },
            Default::default(),
        )
        .await
        .unwrap();

    let check_segment = || async {
        assert_eq!(fixture.read_segment_wal_entries(segment_id).await, vec![],);
        assert!(fixture.read_segment_docs(segment_id).await.is_empty());
        assert_eq!(
            fixture
                .config
                .global_store
                .query_segment_wal_entries_batch(segment_id, None, None, None)
                .await
                .unwrap(),
            vec![],
        );
    };
    check_segment().await;

    fixture
        .run_process_segment_wal(
            ProcessSegmentWalInput {
                orig_input: fixture.process_object_wal_input(),
                segment_id,
                xact_wal_entries: vec![(TransactionId(0), vec![])],
                is_new_segment: false,
            },
            Default::default(),
        )
        .await
        .unwrap();
    check_segment().await;
}
