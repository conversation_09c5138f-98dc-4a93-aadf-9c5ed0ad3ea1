use async_stream::stream;
use async_trait::async_trait;
use async_util::single_item_stream::SingleItemStream;
use downcast_rs::{impl_downcast, Downcast};
use futures::{future::join_all, stream::BoxStream, StreamExt};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tantivy::common::OwnedBytes;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tracing::{instrument, Instrument};
use util::{
    anyhow::{anyhow, Context, Result},
    chrono::{DateTime, Utc},
    itertools::Itertools,
    new_id,
    serde_json::{self, Map, Value},
    system_types::{
        FullObjectId, FullObjectIdOwned, FullRowId, FullRowIdOwned, ObjectIdOwned, ObjectType,
    },
    uuid::{self, Uuid},
    xact::{make_pagination_key, PaginationKey, PaginationKeyCounter, TransactionId},
};

use crate::{
    global_store::GlobalStore,
    limits::global_limits,
    static_sync_runtime::STATIC_SYNC_RUNTIME,
    wal_entry::{AuditDataEntries, WalEntry, WalEntryComments, WalEntrySystemFields},
    wal_stats::InFlightBytesGuard,
    xact_manager::TransactionManager,
};

#[derive(Debug, Clone, Copy)]
pub enum WALScope<'a> {
    ObjectId(FullObjectId<'a>, Uuid),
    Segment(Uuid),
}

impl<'a> WALScope<'a> {
    pub fn lock_name(&self) -> String {
        match self {
            WALScope::ObjectId(object_id, token) => {
                format!("object_wal_lock_{}_{}", token, object_id)
            }
            WALScope::Segment(segment_id) => format!("segment_wal_lock_{}", segment_id),
        }
    }
}

// Trait for an object that is tied to the lifetime of a particular WalData, which tracks the "raw
// bytes size" of the WAL entry. It may perform cleanup operations when the WalData is dropped. See
// wal_stats::InFlightBytesGuard for an implementation.
pub trait WalBytesGuard: Send + Sync {
    fn num_bytes(&self) -> usize;
}

pub struct TrivialWalBytesGuard {}

impl WalBytesGuard for TrivialWalBytesGuard {
    fn num_bytes(&self) -> usize {
        0
    }
}

// A variant between a fully-parsed WalEntry and a partially-parsed
// WalEntrySystemFields, plus the unparsed bytes of the full entry.
#[derive(Debug, Clone)]
pub enum WalEntryVariant {
    Full(WalEntry),
    SystemFields {
        system_fields: WalEntrySystemFields,
        full_bytes: OwnedBytes,
    },
}

impl Default for WalEntryVariant {
    fn default() -> Self {
        WalEntryVariant::Full(Default::default())
    }
}

impl WalEntryVariant {
    pub fn to_wal_entry(self) -> Result<WalEntry> {
        match self {
            WalEntryVariant::Full(wal_entry) => Ok(wal_entry),
            WalEntryVariant::SystemFields {
                system_fields,
                full_bytes,
            } => {
                let value = util::json::deserialize_json_no_recursion_limit(full_bytes.as_slice())
                    .with_context(|| {
                        format!(
                            "failed to deserialize json data for system fields wal entry {:?}",
                            system_fields
                        )
                    })?;
                WalEntry::new(value)
            }
        }
    }

    pub fn id(&self) -> &str {
        match self {
            WalEntryVariant::Full(wal_entry) => &wal_entry.id,
            WalEntryVariant::SystemFields { system_fields, .. } => &system_fields.id,
        }
    }

    pub fn full_row_id(&self) -> FullRowId {
        match self {
            WalEntryVariant::Full(wal_entry) => wal_entry.full_row_id(),
            WalEntryVariant::SystemFields { system_fields, .. } => system_fields.full_row_id(),
        }
    }

    pub fn full_root_span_id(&self) -> FullRowId {
        match self {
            WalEntryVariant::Full(wal_entry) => wal_entry.full_root_span_id(),
            WalEntryVariant::SystemFields { system_fields, .. } => {
                system_fields.full_root_span_id()
            }
        }
    }

    pub fn full_object_id(&self) -> FullObjectId {
        match self {
            WalEntryVariant::Full(wal_entry) => wal_entry.full_object_id(),
            WalEntryVariant::SystemFields { system_fields, .. } => system_fields.full_object_id(),
        }
    }

    pub fn is_standalone_comment(&self) -> bool {
        match self {
            WalEntryVariant::Full(wal_entry) => wal_entry._is_standalone_comment.unwrap_or(false),
            WalEntryVariant::SystemFields { system_fields, .. } => {
                system_fields._is_standalone_comment.unwrap_or(false)
            }
        }
    }

    pub fn xact_id(&self) -> TransactionId {
        match self {
            WalEntryVariant::Full(wal_entry) => wal_entry._xact_id,
            WalEntryVariant::SystemFields { system_fields, .. } => system_fields._xact_id,
        }
    }

    pub fn root_span_id(&self) -> &str {
        match self {
            WalEntryVariant::Full(wal_entry) => &wal_entry.root_span_id,
            WalEntryVariant::SystemFields { system_fields, .. } => &system_fields.root_span_id,
        }
    }

    pub fn pagination_key(&self) -> PaginationKey {
        match self {
            WalEntryVariant::Full(wal_entry) => wal_entry._pagination_key,
            WalEntryVariant::SystemFields { system_fields, .. } => system_fields._pagination_key,
        }
    }
}

// When reading WAL entries, the user can specify a preference for the type of WalEntryVariant
// they want to read.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum WalEntryVariantPreference {
    // If specified, then all WalEntryVariants are guaranteed to be full WalEntries.
    Full,
    // If specified, then WalEntryVariants will be WalEntrySystemFields if they
    // can be efficiently parsed, otherwise they will be full WalEntries.
    SystemFields,
}

// Trait for a metadata entry for a single transaction's worth of data from the WAL. Concrete
// implementations of this trait may include extra metadata which callers can utilize if they know
// the underlying implementation.
#[async_trait]
pub trait WalMetadata: 'static + Send + Sync {
    // The transaction ID these WAL entries are for.
    fn xact_id(&self) -> TransactionId;

    // The approximate size of the WAL data in bytes.
    fn num_bytes(&self) -> usize;

    // Read the WAL entries for this transaction.
    async fn read_wal_entries(
        &self,
        preference: WalEntryVariantPreference,
    ) -> Result<Vec<WalEntryVariant>>;
}

// Useful for functions which want to accept either a concrete WalMetadata implementation or a
// dynamic one.
pub trait AsDynWalMetadata: 'static + Send + Sync {
    fn as_dyn_wal_metadata(&self) -> &dyn WalMetadata;
}

impl<T: WalMetadata> AsDynWalMetadata for T {
    fn as_dyn_wal_metadata(&self) -> &dyn WalMetadata {
        self
    }
}

impl AsDynWalMetadata for Box<dyn WalMetadata> {
    fn as_dyn_wal_metadata(&self) -> &dyn WalMetadata {
        self.as_ref()
    }
}

#[derive(Clone, Debug, Default)]
pub struct WalMetadataStreamOptionalInput {
    pub start_xact_id: Option<TransactionId>,
    pub end_xact_id: Option<TransactionId>,
}

#[derive(Clone, Debug)]
pub struct DeleteUpToXactIdInput<'a> {
    pub scope: WALScope<'a>,
    pub min_retained_xact_id: TransactionId,
    pub dry_run: bool,
}

#[derive(Clone, Debug)]
pub struct DeleteUpToXactIdOptions {
    pub batch_size: i64,
    pub max_num_rows: u64,
    pub deletion_log_batch_size: usize,
}

#[derive(Clone, Copy, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct DeleteFromWalStats {
    #[serde(default)]
    pub planned_num_deletes: u64,
    #[serde(default)]
    pub num_deletes: u64,
    #[serde(default)]
    pub total_bytes: u64,
}

impl std::ops::Add for DeleteFromWalStats {
    type Output = Self;

    fn add(self, other: Self) -> Self {
        Self {
            planned_num_deletes: self.planned_num_deletes + other.planned_num_deletes,
            num_deletes: self.num_deletes + other.num_deletes,
            total_bytes: self.total_bytes + other.total_bytes,
        }
    }
}

impl std::iter::Sum for DeleteFromWalStats {
    fn sum<I: Iterator<Item = Self>>(iter: I) -> Self {
        iter.fold(Self::default(), |acc, x| acc + x)
    }
}

#[async_trait]
pub trait Wal: Send + Sync + Downcast {
    /// Insert a batch of WalEntries into the WAL at the given scope.
    async fn insert<'a>(&self, scope: WALScope<'a>, wal_entries: Vec<WalEntry>) -> Result<()>;

    /// Return a stream of WalMetadatas at the given scope. This stream can be passed to
    /// the generic `wal_stream` function to convert the metadatas into a stream of WalDatas.
    ///
    /// The caller may optionally specify a start/end transaction ID range (inclusive).
    async fn wal_metadata_stream<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<Box<dyn WalMetadata>>>>;

    async fn status(&self) -> Result<String>;

    /// Delete WAL entries with transaction IDs less than min_retained_xact_id. This may or may not
    /// actually delete the stored files, but the entries will no longer appear in
    /// `wal_stream` after this function completes.
    async fn delete_up_to_xact_id<'a>(
        &self,
        input: DeleteUpToXactIdInput<'a>,
        options: &DeleteUpToXactIdOptions,
    ) -> Result<DeleteFromWalStats>;

    /// For testing: if the WAL exists on the local filesystem, delete the directory.
    fn remove_local(&self) -> Result<()>;
}

impl_downcast!(Wal);

/// Merge a set of WalMetadata streams into a single stream, in order of xact ID.
pub fn merge_wal_metadata_streams<T: AsDynWalMetadata + Send + Sync>(
    wal_metadata_streams: impl IntoIterator<Item = BoxStream<'static, Result<T>>>,
) -> BoxStream<'static, Result<T>> {
    async_util::stream_util::merge_ordered_streams(wal_metadata_streams, |a, b| {
        match (a.as_ref(), b.as_ref()) {
            (Ok(a), Ok(b)) => a
                .as_dyn_wal_metadata()
                .xact_id()
                .cmp(&b.as_dyn_wal_metadata().xact_id()),
            // We want errors to come first.
            (Ok(_), Err(_)) => std::cmp::Ordering::Less,
            (Err(_), Ok(_)) => std::cmp::Ordering::Greater,
            (Err(_), Err(_)) => std::cmp::Ordering::Equal,
        }
    })
}

// When streaming WAL entries, the user can specify their
// WalEntryVariantPreference as a compile-time option in order to get a nice
// output type.
pub trait WalEntryVariantPreferenceT: 'static + Send + Sync + std::fmt::Debug {
    type EntryType: Send + Sync;

    fn preference() -> WalEntryVariantPreference;
    fn convert_to_entry_type(entry: WalEntryVariant) -> Result<Self::EntryType>;
    fn convert_to_wal_entry(entry: Self::EntryType) -> Result<WalEntry>;
}

#[derive(Debug)]
pub struct PreferWalEntry;

impl WalEntryVariantPreferenceT for PreferWalEntry {
    type EntryType = WalEntry;

    fn preference() -> WalEntryVariantPreference {
        WalEntryVariantPreference::Full
    }
    fn convert_to_entry_type(entry: WalEntryVariant) -> Result<Self::EntryType> {
        entry.to_wal_entry()
    }
    fn convert_to_wal_entry(entry: Self::EntryType) -> Result<WalEntry> {
        Ok(entry)
    }
}

#[derive(Debug)]
pub struct PreferWalEntrySystemFields;

impl WalEntryVariantPreferenceT for PreferWalEntrySystemFields {
    type EntryType = WalEntryVariant;

    fn preference() -> WalEntryVariantPreference {
        WalEntryVariantPreference::SystemFields
    }
    fn convert_to_entry_type(entry: WalEntryVariant) -> Result<Self::EntryType> {
        Ok(entry)
    }
    fn convert_to_wal_entry(entry: Self::EntryType) -> Result<WalEntry> {
        entry.to_wal_entry()
    }
}

#[derive(Debug, Clone, Copy, Default, PartialEq, Eq)]
pub enum WalDataDetail {
    #[default]
    Normal,
    ExhaustedMaxNumBytes,
}

// A batch of WalEntries returned by the wal_stream function.
pub struct WalData<EntryType, T: AsDynWalMetadata> {
    pub entries: Vec<(TransactionId, Vec<EntryType>, Vec<T>)>,
    pub bytes_guard: Box<dyn WalBytesGuard>,
    pub detail: WalDataDetail,
}

#[derive(Debug)]
pub struct WalStreamOptions {
    // Target size of each batch of WAL entries to stream in parallel, in bytes. Operations like
    // WAL processing and compaction operate one batch at a time, and batches can be loaded in
    // parallel up to wal_max_inflight. It is automatically capped to wal_max_inflight.
    //
    // It's a u32 because the permit we can request in a Semaphore is a u32.
    pub target_batch_size: u32,

    // Optionally specify a maximum amount of data to read from the WAL in
    // number of bytes. Will read wal entries until their size exceeds this
    // limit, so the memory limit can be exceeded by at most one transaction's
    // worth.
    //
    // However if the limit is set to 0, then we will read nothing.
    pub max_num_bytes: Option<usize>,
}

pub fn default_target_batch_size() -> u32 {
    // 500MB
    500 * 1024 * 1024
}

impl Default for WalStreamOptions {
    fn default() -> Self {
        Self {
            target_batch_size: default_target_batch_size(),
            max_num_bytes: None,
        }
    }
}

/// Iterate over a WalMetadata stream to produce a stream of WalDatas. Each WalData containins a
/// batch of transactions, where each transaction is a tuple of the transaction ID the sequence of
/// WAL entries for that transaction, and the constituent WalMetadatas that went into it.
///
/// The stream may terminate in error if there is an issue reading the WAL. It is a
/// SingleItemStream, which means only one entry may be read from the stream at a time, and it must
/// be dropped before the next entry is read.
///
/// IMPORTANT: do not wait on multiple wal_streams in the same coroutine. In general it is only
/// guaranteed that one WAL stream within a process will make progress due to the memory limit
/// bounds. So as long as each wal stream is being awaited by a separate coroutine, the system
/// should make progress, but if a single coroutine tries to wait on multiple streams (e.g. trying
/// to merge outputs from multiple WAL streams), the system may get blocked. Instead, use
/// merge_wal_metadata_streams to merge the metadata streams before passing them to wal_stream.
#[instrument(skip(wal_metadata_stream))]
pub fn wal_stream_generic<P: WalEntryVariantPreferenceT, T: AsDynWalMetadata + Send + Sync>(
    mut wal_metadata_stream: BoxStream<'static, Result<T>>,
    preference: P,
    options: WalStreamOptions,
) -> SingleItemStream<BoxStream<'static, Result<WalData<P::EntryType, T>>>> {
    let ret = stream! {
      // We stream the WalMetadatas in parallel to increase throughput, but bounded by a maximum
      // WAL capacity in MB. We accomplish this as follows:
      //
      // 1. Create an unbounded channel, where each item is a background task responsible for
      //    reading a batch of WAL entries.
      //
      // 2. In a background "producer" task, iterate over the input wal_metadata_stream:
      //
      //      - Group the metadata entries into batches of roughly `options.target_batch_size`
      //      bytes. We must make sure each batch includes the full set of WAL entries available
      //      for a transaction, so it may end up exceeding the strict limit, but in practice not
      //      by a lot.
      //
      //      - Once we have a full batch, acquire an InFlightBytesGuard permit for the batch
      //      size, which will block if we already have too many WAL entries in flight across
      //      the process.
      //
      //      - Once we have the permit, convert the batch into a background task that reads
      //      all WAL files in the batch in parallel and returns a Vec<(TransactionId,
      //      Vec<WalEntry>, Vec<T>)>. Push this background task into the channel.
      //
      // Once the stream of metadatas has been exhausted, the producer task ends and the sender is
      // dropped, which closes it.
      //
      // 3. In the foreground "receiver" task, keep popping background tasks off the channel,
      //    and yield each one in turn to the consumer of the stream. Once the receiver
      //    finishes, we can expect the producer task to be finished, so we await that and
      //    finish the stream.

      // Note that the channel is typed to WalData<WalEntryVariant, T>, but we
      // still use the runtime-typed preference when processing the metadata.
      // This lets us avoid too much compile-time code forking at lower levels
      // of implementation, while still preserving a nice type signature for the
      // end user.
      let (sender, mut receiver) = tokio::sync::mpsc::unbounded_channel::<
          tokio::task::JoinHandle::<Result<WalData<WalEntryVariant, T>>>>();

      let producer_task: tokio::task::JoinHandle::<Result<()>> = {
          // Run this on the STATIC_SYNC_RUNTIME to ensure parallelization.
          STATIC_SYNC_RUNTIME.spawn(async move {
              let mut cur_wal_metadatas: Vec<T> = Vec::new();
              let mut cur_wal_metadatas_size = 0;
              let mut total_num_wal_metadatas = 0;
              let mut total_num_bytes = 0;
              let mut read_entire_wal_stream = true;
              while let Some(metadata) = wal_metadata_stream.next().await {
                  if let Some(max_num_bytes) = options.max_num_bytes {
                      if max_num_bytes == 0 || total_num_bytes > max_num_bytes {
                          read_entire_wal_stream = false;
                          break;
                      }
                  }
                  let metadata = metadata?;
                  let this_num_bytes = metadata.as_dyn_wal_metadata().num_bytes();
                  process_wal_metadata(&mut cur_wal_metadatas, &mut cur_wal_metadatas_size, metadata, P::preference(), options.target_batch_size).await?.map(|x| sender.send(x)).transpose()?;
                  total_num_wal_metadatas += 1;
                  total_num_bytes += this_num_bytes;
              }
              flush_cur_wal_metadatas(cur_wal_metadatas, cur_wal_metadatas_size, P::preference()).await?.map(|x| sender.send(x)).transpose()?;
              // If we exhausted the max_num_bytes before reaching the end of
              // the stream, send a sentinel WalData with the detail set to
              // ExhaustedMaxNumBytes.
              if !read_entire_wal_stream {
                sender.send(tokio::spawn(async {
                    Ok(WalData {
                      entries: Vec::new(),
                      bytes_guard: Box::new(TrivialWalBytesGuard {}),
                      detail: WalDataDetail::ExhaustedMaxNumBytes,
                    })
                  }))?;
              }
              tracing::debug!(num_wal_metadatas = total_num_wal_metadatas, num_bytes = total_num_bytes, "Read WAL metadatas");
              Ok(())
          }.instrument(tracing::info_span!("stream WAL metadatas")))
      };
      while let Some(data_handle) = receiver.recv().await {
          // Extract the runtime-typed WalData from the JoinHandle, and convert
          // it to the compile-time-typed form. There should be no errors here
          // because the compile-time typed form should agree with how we parsed
          // it in the producer task.
          yield data_handle.await?.map(|data| {
            let WalData { entries, bytes_guard, detail } = data;
            WalData {
                entries: entries.into_iter().map(|(xact_id, entries, wal_metadatas)|
                    (xact_id, entries.into_iter().map(|entry| P::convert_to_entry_type(entry).unwrap()).collect(), wal_metadatas)).collect(),
                bytes_guard,
                detail,
            }
          });
      }
      // Close out the producer task.
      producer_task.await??;
    }.instrument(tracing::info_span!("stream WAL")).boxed();
    SingleItemStream::new(ret)
}

pub fn wal_stream(
    wal_metadata_stream: BoxStream<'static, Result<Box<dyn WalMetadata>>>,
    options: WalStreamOptions,
) -> SingleItemStream<BoxStream<'static, Result<WalData<WalEntry, Box<dyn WalMetadata>>>>> {
    wal_stream_generic(wal_metadata_stream, PreferWalEntry, options)
}

/// Insert a batch of values into the WAL. The values will be grouped by their object ID and
/// written to their respective object-scoped WAL. Returns the transaction ID of the inserted
/// values.
pub async fn wal_insert_unnormalized(
    wal: &dyn Wal,
    global_store: &dyn GlobalStore,
    values: Vec<Value>,
    xact_manager: &dyn TransactionManager,
) -> Result<TransactionId> {
    let xact_id = xact_manager.next_xact_id().await?;
    let now_ts_millis = util::now_ts_millis();
    let mut normalized_wal_entries = values
        .into_iter()
        .enumerate()
        .map(|(idx, value)| {
            normalize_row(
                xact_id,
                PaginationKeyCounter::WithinXactRowNum(u16::try_from(idx)?),
                value,
                now_ts_millis,
            )
        })
        .collect::<Result<Vec<_>>>()?;
    normalized_wal_entries.sort_by_key(|entry| entry._xact_id);
    wal_insert_unmerged(wal, global_store, normalized_wal_entries).await?;
    Ok(xact_id)
}

pub async fn wal_insert_normalized(
    wal: &dyn Wal,
    global_store: &dyn GlobalStore,
    values: Vec<Value>,
) -> Result<()> {
    let wal_entries = values
        .into_iter()
        .map(WalEntry::new)
        .collect::<Result<Vec<_>>>()?;
    wal_insert_unmerged(wal, global_store, wal_entries).await
}

pub async fn wal_insert_unmerged(
    wal: &dyn Wal,
    global_store: &dyn GlobalStore,
    values: Vec<WalEntry>,
) -> Result<()> {
    let merged_wal_entries = (move || -> Result<Vec<WalEntry>> {
        let mut merged_wal_entries = Vec::new();
        let mut row_id_to_merged_wal_entry_idx: HashMap<FullRowIdOwned, usize> = HashMap::new();
        for wal_entry in values {
            let full_row_id = wal_entry.full_row_id().to_owned();
            match row_id_to_merged_wal_entry_idx.get(&full_row_id) {
                Some(idx) => {
                    let existing_entry: &mut WalEntry = &mut merged_wal_entries[*idx];
                    existing_entry.merge(wal_entry)?;
                }
                None => {
                    row_id_to_merged_wal_entry_idx.insert(full_row_id, merged_wal_entries.len());
                    merged_wal_entries.push(wal_entry);
                }
            }
        }
        Ok(merged_wal_entries)
    })()?;
    let object_wal_entries: Vec<(FullObjectIdOwned, Vec<WalEntry>)> = merged_wal_entries
        .into_iter()
        .map(|wal_entry| (wal_entry.full_object_id().to_owned(), wal_entry))
        .into_group_map()
        .into_iter()
        .collect();
    let object_wal_tokens = {
        let object_id_refs: Vec<FullObjectId> = object_wal_entries
            .iter()
            .map(|(object_id, _)| object_id.as_ref())
            .collect();
        global_store
            .query_object_metadatas(&object_id_refs)
            .await?
            .into_iter()
            .map(|metadata| metadata.wal_token)
            .collect::<Vec<_>>()
    };
    join_all(
        object_wal_entries
            .into_iter()
            .zip(object_wal_tokens.into_iter())
            .map(|((object_id, wal_entries), wal_token)| async move {
                wal.insert(
                    WALScope::ObjectId(object_id.as_ref(), wal_token),
                    wal_entries,
                )
                .await
            }),
    )
    .await
    .into_iter()
    .collect::<Result<()>>()?;
    Ok(())
}

pub fn fill_default_object_id(
    mut value: serde_json::Value,
    default_object_id: FullObjectId<'_>,
) -> serde_json::Value {
    let obj_value = value.as_object_mut().unwrap();
    if !obj_value.contains_key("_object_type") {
        obj_value.insert(
            "_object_type".to_string(),
            serde_json::to_value(default_object_id.object_type).unwrap(),
        );
    }
    if !obj_value.contains_key("_object_id") {
        obj_value.insert(
            "_object_id".to_string(),
            serde_json::to_value(default_object_id.object_id).unwrap(),
        );
    }
    value
}

#[derive(Debug, Clone, Deserialize)]
struct UnNormalizedWalEntry {
    id: Option<String>,
    created: Option<DateTime<Utc>>,
    _is_standalone_comment: Option<bool>,
    _is_merge: Option<bool>,
    _merge_paths: Option<Vec<Vec<String>>>,
    _object_delete: Option<bool>,
    pub _object_type: ObjectType,
    pub _object_id: ObjectIdOwned,
    pub root_span_id: Option<String>,
    pub span_id: Option<String>,
    pub span_parents: Option<Vec<String>>,
    pub comments: Option<Value>,
    pub audit_data: Option<Value>,
    pub _self_audit_data: Option<Map<String, Value>>,
    #[serde(flatten)]
    data: Map<String, Value>,
}

// If we change any logic here, we should make sure that it doesn't do anything
// that isn't covered by parsing into `RawWalEntry`. See comment in
// brainstore/storage/src/raw_wal_entry.rs.
pub fn normalize_row(
    default_xact_id: TransactionId,
    pagination_key_counter: PaginationKeyCounter,
    value: Value,
    now_ts_millis: i64,
) -> Result<WalEntry> {
    let mut obj = serde_json::from_value::<UnNormalizedWalEntry>(value)
        .with_context(|| "Failed to deserialize un-normalized JSON row")?;

    let _xact_id = match obj.data.remove("_xact_id") {
        Some(xact_id_value) => serde_json::from_value(xact_id_value.clone())
            .with_context(|| format!("Failed to deserialize _xact_id field: {}", xact_id_value))?,
        None => default_xact_id,
    };

    let pagination_key = match obj.data.remove("_pagination_key") {
        Some(pagination_key_value) => serde_json::from_value(pagination_key_value.clone())
            .with_context(|| {
                format!(
                    "Failed to deserialize _pagination_key field: {}",
                    pagination_key_value
                )
            })?,
        None => make_pagination_key(_xact_id, pagination_key_counter),
    };

    let root_span_id = obj
        .root_span_id
        .unwrap_or_else(|| uuid::Uuid::new_v4().to_string());

    let span_id = obj
        .span_id
        .unwrap_or_else(|| uuid::Uuid::new_v4().to_string());

    let span_parents = obj.span_parents.unwrap_or_default();

    Ok(WalEntry {
        id: obj.id.unwrap_or_else(new_id),
        created: obj
            .created
            .or_else(|| DateTime::from_timestamp_millis(now_ts_millis))
            .ok_or_else(|| anyhow!("Invalid now_ts timestamp {}", now_ts_millis))?,
        _pagination_key: pagination_key,
        _xact_id,
        _object_type: obj._object_type,
        _object_id: obj._object_id,
        _self_audit_data: obj._self_audit_data,
        _is_standalone_comment: obj._is_standalone_comment,
        _is_merge: obj._is_merge,
        _merge_paths: obj._merge_paths,
        _replace_sticky_system_fields: None,
        _object_delete: obj._object_delete,
        // Note: we need to fill in a default root_span_id and span_id here, because if this row is
        // used for query processing and it's a new row, we need to make sure we return a
        // consistent value for these fields, even after it gets compacted.
        root_span_id,
        span_id,
        span_parents,
        comments: obj
            .comments
            .map(WalEntryComments::new)
            .transpose()?
            .unwrap_or_default(),
        audit_data: obj
            .audit_data
            .map(AuditDataEntries::new)
            .transpose()?
            .unwrap_or_default(),
        data: obj.data,
    })
}

// Reconciles the entries in multiple WAL files on the same xact id.
fn group_and_dedup_wal_entries<T: AsDynWalMetadata + Send + Sync>(
    entries: impl IntoIterator<Item = (T, Vec<WalEntryVariant>)>,
) -> Result<Vec<(TransactionId, Vec<WalEntryVariant>, Vec<T>)>> {
    let mut xact_wal_entries: HashMap<
        TransactionId,
        (HashMap<FullRowIdOwned, WalEntryVariant>, Vec<T>),
    > = HashMap::new();
    for (wal_metadata, wal_entries_vec) in entries {
        let xact_id = wal_metadata.as_dyn_wal_metadata().xact_id();
        let (wal_entries_map, wal_metadatas) = xact_wal_entries.entry(xact_id).or_default();
        for entry in wal_entries_vec {
            use std::collections::hash_map::Entry;
            let map_entry = wal_entries_map.entry(entry.full_row_id().to_owned());
            match map_entry {
                // Make sure to merge in any standalone comments, rather than
                // de-duplicating with them. This requires working with the full
                // WAL entries.
                Entry::Occupied(mut occupied) => {
                    let existing_entry = occupied.get_mut();
                    if existing_entry.is_standalone_comment() || entry.is_standalone_comment() {
                        let mut full_entry = std::mem::take(existing_entry).to_wal_entry()?;
                        // The merge shouldn't fail because we're joining on the FullRowId.
                        full_entry.merge(entry.to_wal_entry()?).unwrap();
                        *existing_entry = WalEntryVariant::Full(full_entry);
                    }
                }
                Entry::Vacant(vacant) => {
                    vacant.insert(entry);
                }
            }
        }
        wal_metadatas.push(wal_metadata);
    }
    let mut ret: Vec<(TransactionId, Vec<WalEntryVariant>, Vec<T>)> = xact_wal_entries
        .into_iter()
        .map(|(xact_id, (entries_map, metadatas))| {
            (xact_id, entries_map.into_values().collect(), metadatas)
        })
        .collect();
    ret.sort_by_key(|(xact_id, _, _)| *xact_id);
    Ok(ret)
}

// These functions use the runtime-specified preference for reading the data,
// but always deal with WalData<WalEntryVariant, T> types for code simplicity.
async fn process_wal_metadata<T: AsDynWalMetadata + Send + Sync>(
    cur_wal_metadatas: &mut Vec<T>,
    cur_wal_metadatas_size: &mut usize,
    wal_metadata: T,
    preference: WalEntryVariantPreference,
    target_batch_size: u32,
) -> Result<Option<JoinHandle<Result<WalData<WalEntryVariant, T>>>>> {
    let target_batch_size = std::cmp::min(
        target_batch_size as usize,
        global_limits().wal_max_inflight_bytes.capacity(),
    );

    // If we have reached the target batch size and the new wal metadata is on a different
    // transaction, we can launch a task for our collected byte ranges. Otherwise, don't launch a
    // task.
    let task = if *cur_wal_metadatas_size >= target_batch_size
        && cur_wal_metadatas
            .last()
            .expect("cur_wal_metadatas is empty. This should never happen.")
            .as_dyn_wal_metadata()
            .xact_id()
            != wal_metadata.as_dyn_wal_metadata().xact_id()
    {
        flush_cur_wal_metadatas(
            std::mem::take(cur_wal_metadatas),
            std::mem::take(cur_wal_metadatas_size),
            preference,
        )
        .await?
    } else {
        None
    };

    // Process the new byte range before returning our task.
    let num_bytes = wal_metadata.as_dyn_wal_metadata().num_bytes();
    cur_wal_metadatas.push(wal_metadata);
    *cur_wal_metadatas_size += num_bytes;
    Ok(task)
}

async fn flush_cur_wal_metadatas<T: AsDynWalMetadata + Send + Sync>(
    wal_metadatas: Vec<T>,
    wal_metadatas_size: usize,
    preference: WalEntryVariantPreference,
) -> Result<Option<JoinHandle<Result<WalData<WalEntryVariant, T>>>>> {
    let num_wal_metadatas = wal_metadatas.len();
    if num_wal_metadatas == 0 {
        assert_eq!(wal_metadatas_size, 0);
        return Ok(None);
    }
    let bytes_guard = InFlightBytesGuard::new(wal_metadatas_size).await?;
    // Run this on the STATIC_SYNC_RUNTIME to ensure parallelization.
    let task = STATIC_SYNC_RUNTIME.spawn(
        async move {
            let all_wal_entries = join_all(wal_metadatas.iter().map(|wal_metadata| {
                wal_metadata
                    .as_dyn_wal_metadata()
                    .read_wal_entries(preference)
            }))
            .await
            .into_iter()
            .collect::<Result<Vec<Vec<WalEntryVariant>>>>()?;
            let entries =
                group_and_dedup_wal_entries(wal_metadatas.into_iter().zip(all_wal_entries))?;
            Ok(WalData {
                entries,
                bytes_guard: Box::new(bytes_guard),
                detail: WalDataDetail::Normal,
            })
        }
        .instrument(tracing::info_span!(
            "read WAL entries",
            num_wal_metadatas = num_wal_metadatas,
            wal_metadatas_size = wal_metadatas_size
        )),
    );
    Ok(Some(task))
}
