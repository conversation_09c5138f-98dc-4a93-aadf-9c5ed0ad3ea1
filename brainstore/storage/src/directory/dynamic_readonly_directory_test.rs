use std::{collections::HashSet, path::PathBuf};

use tantivy::{
    collector::DocSetCollector, query::QueryParser, schema::Value, IndexBuilder, TantivyDocument,
};
use util::{anyhow::Result, uuid::Uuid};

use crate::test_util::TmpDirStore;

use super::dynamic_readonly_directory::DynamicReadonlyDirectory;

struct TestFixture {
    tmp_dir_store: TmpDirStore,
    runtime: tokio::runtime::Runtime,
}

impl TestFixture {
    pub fn new() -> Self {
        let tmp_dir_store = TmpDirStore::new();
        let runtime = tokio::runtime::Runtime::new().unwrap();
        TestFixture {
            tmp_dir_store,
            runtime,
        }
    }

    pub fn make_index(&self, builder: tantivy::IndexBuilder) -> (PathBuf, tantivy::Index) {
        let path = self
            .tmp_dir_store
            .tmp_dir
            .path()
            .join(Uuid::new_v4().to_string());
        std::fs::create_dir(&path).unwrap();
        let index = builder.create_in_dir(&path).unwrap();
        (path, index)
    }

    pub fn make_directory(
        &self,
        schema: tantivy::schema::Schema,
        index_paths: Vec<PathBuf>,
    ) -> Result<DynamicReadonlyDirectory> {
        self.runtime
            .block_on(DynamicReadonlyDirectory::from_directory_and_paths(
                Some(schema),
                self.tmp_dir_store.store_info.directory.clone(),
                index_paths,
            ))
    }

    pub fn make_directory_index_reader<T: tantivy::Directory>(
        &self,
        directory: T,
    ) -> tantivy::IndexReader {
        let index = tantivy::Index::open(directory).unwrap();
        index.reader().unwrap()
    }
}

fn make_schema0() -> tantivy::schema::Schema {
    let mut schema_builder = tantivy::schema::SchemaBuilder::new();
    schema_builder.add_text_field("id", tantivy::schema::STRING | tantivy::schema::STORED);
    schema_builder.add_text_field("a", tantivy::schema::TEXT | tantivy::schema::STORED);
    schema_builder.build()
}

fn make_schema0_doc(id: &str, a: &str) -> tantivy::TantivyDocument {
    let schema = make_schema0();
    let id_field = schema.get_field("id").unwrap();
    let a_field = schema.get_field("a").unwrap();
    let mut doc = tantivy::TantivyDocument::new();
    doc.add_text(id_field, id);
    doc.add_text(a_field, a);
    doc
}

fn make_schema1() -> tantivy::schema::Schema {
    let mut schema_builder = tantivy::schema::SchemaBuilder::new();
    schema_builder.add_text_field("id", tantivy::schema::STRING | tantivy::schema::STORED);
    schema_builder.add_text_field("b", tantivy::schema::TEXT | tantivy::schema::STORED);
    schema_builder.build()
}

fn make_schema01() -> tantivy::schema::Schema {
    let mut schema_builder = tantivy::schema::SchemaBuilder::new();
    schema_builder.add_text_field("id", tantivy::schema::STRING | tantivy::schema::STORED);
    schema_builder.add_text_field("a", tantivy::schema::TEXT | tantivy::schema::STORED);
    schema_builder.add_text_field("b", tantivy::schema::TEXT | tantivy::schema::STORED);
    schema_builder.build()
}

fn make_schema01_doc(id: &str, b: &str) -> tantivy::TantivyDocument {
    let schema = make_schema01();
    let id_field = schema.get_field("id").unwrap();
    let b_field = schema.get_field("b").unwrap();
    let mut doc = tantivy::TantivyDocument::new();
    doc.add_text(id_field, id);
    doc.add_text(b_field, b);
    doc
}

fn make_index_writer(index: &tantivy::Index) -> tantivy::IndexWriter {
    index.writer(100_000_000).unwrap()
}

fn query_doc_ids(
    directory: &DynamicReadonlyDirectory,
    reader: &tantivy::IndexReader,
    query: &str,
) -> HashSet<String> {
    let schema = directory.schema();
    let id_field = schema.get_field("id").unwrap();
    let query_parser = QueryParser::new(
        schema.clone(),
        vec![],
        tantivy::tokenizer::TokenizerManager::default(),
    );
    let query_obj = query_parser.parse_query(query).unwrap();
    let doc_addrs = reader
        .searcher()
        .search(&query_obj, &DocSetCollector {})
        .unwrap();
    doc_addrs
        .into_iter()
        .map(|doc_address| {
            let doc: tantivy::TantivyDocument = reader.searcher().doc(doc_address).unwrap();
            doc.get_first(id_field)
                .unwrap()
                .as_str()
                .unwrap()
                .to_string()
        })
        .collect()
}

fn make_str_set(strings: &[&str]) -> HashSet<String> {
    strings.iter().map(|s| s.to_string()).collect()
}

#[test]
fn test_basic() {
    let fixture = TestFixture::new();
    let schema = make_schema0();

    let (index0_path, index0) = fixture.make_index(IndexBuilder::new().schema(schema.clone()));
    {
        let mut writer = make_index_writer(&index0);
        writer
            .add_document(make_schema0_doc("index0_0", "hello"))
            .unwrap();
        writer
            .add_document(make_schema0_doc("index0_1", "goodbye"))
            .unwrap();
        writer.commit().unwrap();
    }

    let (index1_path, index1) = fixture.make_index(IndexBuilder::new().schema(schema.clone()));
    {
        let mut writer = make_index_writer(&index1);
        writer
            .add_document(make_schema0_doc("index1_0", "hello"))
            .unwrap();
        writer
            .add_document(make_schema0_doc("index1_1", "yes"))
            .unwrap();
        writer.commit().unwrap();
    }

    // Run some queries using a DynamicReadonlyDirectory.
    let dynamic_directory = fixture
        .make_directory(schema.clone(), vec![index0_path, index1_path])
        .unwrap();
    let dynamic_reader = fixture.make_directory_index_reader(dynamic_directory.clone());

    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "a:hello");
    assert_eq!(results, make_str_set(&["index0_0", "index1_0"]));
    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "a:goodbye");
    assert_eq!(results, make_str_set(&["index0_1"]));
    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "a:yes");
    assert_eq!(results, make_str_set(&["index1_1"]));
}

#[test]
fn test_merge_with_different_consistent_schemas() {
    let fixture = TestFixture::new();
    let schema0 = make_schema0();
    let schema01 = make_schema01();

    let (index0_path, index0) = fixture.make_index(IndexBuilder::new().schema(schema0.clone()));
    {
        let mut writer = make_index_writer(&index0);
        writer
            .add_document(make_schema0_doc("index0_0", "hello"))
            .unwrap();
        writer
            .add_document(make_schema0_doc("index0_1", "goodbye"))
            .unwrap();
        writer.commit().unwrap();
    }

    let (index1_path, index1) = fixture.make_index(IndexBuilder::new().schema(schema01.clone()));
    {
        let mut writer = make_index_writer(&index1);
        writer
            .add_document(make_schema01_doc("index1_0", "hello"))
            .unwrap();
        writer
            .add_document(make_schema01_doc("index1_1", "yes"))
            .unwrap();
        writer.commit().unwrap();
    }

    // Run some queries using a DynamicReadonlyDirectory.
    let dynamic_directory = fixture
        .make_directory(schema01.clone(), vec![index0_path, index1_path])
        .unwrap();
    let dynamic_reader = fixture.make_directory_index_reader(dynamic_directory.clone());

    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "a:hello");
    assert_eq!(results, make_str_set(&["index0_0"]));
    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "b:hello");
    assert_eq!(results, make_str_set(&["index1_0"]));
    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "a:goodbye");
    assert_eq!(results, make_str_set(&["index0_1"]));
    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "b:yes");
    assert_eq!(results, make_str_set(&["index1_1"]));
}

#[test]
fn test_deletes() {
    let fixture = TestFixture::new();
    let schema = make_schema0();
    let (index_path, index) = fixture.make_index(IndexBuilder::new().schema(schema.clone()));
    {
        // Add multiple documents in batch, so we create a single segment with many documents.
        let mut tantivy_ops: Vec<tantivy::indexer::UserOperation<TantivyDocument>> = Vec::new();
        {
            let mut writer = make_index_writer(&index);
            for i in 0..100 {
                tantivy_ops.push(tantivy::indexer::UserOperation::Add(make_schema0_doc(
                    &format!("index_{}", i),
                    "hello",
                )));
            }
            writer.run(tantivy_ops).unwrap();
            writer.commit().unwrap();
        }
    }
    {
        // Deleting one document with a no merge policy should create a .del file.
        let mut writer = make_index_writer(&index);
        writer.set_merge_policy(Box::new(tantivy::merge_policy::NoMergePolicy));
        writer.delete_term(tantivy::Term::from_field_text(
            schema.get_field("id").unwrap(),
            "index_0",
        ));
        writer.commit().unwrap();
    }

    let dynamic_directory = fixture
        .make_directory(schema.clone(), vec![index_path.clone()])
        .unwrap();
    let dynamic_reader = fixture.make_directory_index_reader(dynamic_directory.clone());

    // List all files recursively in the index_path
    let mut files = Vec::new();
    for entry in std::fs::read_dir(index_path).unwrap() {
        let entry = entry.unwrap();
        let path = entry.path();
        if path.is_dir() {
            for subentry in std::fs::read_dir(&path).unwrap() {
                let subentry = subentry.unwrap();
                files.push(subentry.path());
            }
        } else {
            files.push(path);
        }
    }
    let mut found_del_file = false;
    for file in files {
        if file.to_str().unwrap().ends_with(".del") {
            found_del_file = true;
        }
    }
    assert!(found_del_file);

    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "id:index_0");
    assert_eq!(results, make_str_set(&[]));
    let results = query_doc_ids(&dynamic_directory, &dynamic_reader, "*");
    assert_eq!(results.len(), 99);
}

#[test]
fn test_merge_with_inconsistent_schemas() {
    let fixture = TestFixture::new();
    let (index0_path, _index0) = fixture.make_index(IndexBuilder::new().schema(make_schema0()));
    let (index1_path, _index1) = fixture.make_index(IndexBuilder::new().schema(make_schema1()));
    // Constructing the DynamicReadonlyDirectory should fail.
    assert!(fixture
        .make_directory(make_schema1(), vec![index0_path, index1_path])
        .is_err());
}

#[test]
fn test_merge_with_inconsistent_index_settings() {
    let fixture = TestFixture::new();
    let schema = make_schema0();
    let settings0 = tantivy::IndexSettings::default();
    let settings1 = tantivy::IndexSettings {
        docstore_blocksize: 100,
        ..Default::default()
    };
    let (index0_path, _index0) = fixture.make_index(
        IndexBuilder::new()
            .settings(settings0)
            .schema(schema.clone()),
    );
    let (index1_path, _index1) = fixture.make_index(
        IndexBuilder::new()
            .settings(settings1)
            .schema(schema.clone()),
    );
    assert!(fixture
        .make_directory(schema.clone(), vec![index0_path, index1_path])
        .is_err());
}

#[test]
fn test_empty_directory() {
    let fixture = TestFixture::new();
    let schema = make_schema0();
    let dir = fixture.make_directory(schema.clone(), vec![]).unwrap();
    assert_eq!(dir.schema(), &schema);
    assert!(dir.index_path_to_info().is_empty());
}
