use std::{
    collections::HashMap,
    sync::{Arc, Mutex},
};

use async_stream::stream;
use async_trait::async_trait;
use clap::Parser;
use futures::{future::join_all, stream::BoxStream, StreamExt};
use serde::{Deserialize, Serialize};
use tracing::{instrument, Instrument};
use util::{
    anyhow::{anyhow, Context, Result},
    chrono::{DateTime, Utc},
    json::deserialize_json_no_recursion_limit,
    system_types::ObjectType,
    url::Url,
    xact::{PaginationKeyCounter, TransactionId},
};

use crate::{
    directory::AsyncDirectoryArc,
    legacy_sanitizers::{normalize_dt_field, normalize_list_field, normalize_str_field},
    limits::global_limits,
    postgres_pool::PostgresPool,
    postgres_query_util::{make_object_id_column_expr, make_object_id_value_expr},
    wal::{
        DeleteFromWalStats, DeleteUpToXactIdInput, DeleteUpToXactIdOptions, WALScope, Wal,
        WalEntryVariant, WalEntryVariantPreference, WalMetadata, WalMetadataStreamOptionalInput,
    },
    wal_entry::{WalEntry, WalEntrySystemFields},
};

pub const POSTGRES_WAL_TABLE: &str = "logs";
pub const POSTGRES_WAL_TABLE2: &str = "logs2";
pub const POSTGRES_WAL_COMMENTS_TABLE: &str = "comments";

lazy_static::lazy_static! {
    static ref WAL_POOLS: Mutex<HashMap<Url, PostgresPool>> = Mutex::new(HashMap::new());
}

pub struct PostgresWAL {
    inner: Arc<PostgresWALInner>,
    table_name: String,
    table2_name: String,
    comments_table_name: String,
}

impl PostgresWAL {
    pub fn new(
        url: &Url,
        table_name: &str,
        table2_name: &str,
        comments_table_name: &str,
        directory: AsyncDirectoryArc,
    ) -> Result<Self> {
        Ok(Self {
            inner: Arc::new(PostgresWALInner::new(url, directory)?),
            table_name: table_name.to_string(),
            table2_name: table2_name.to_string(),
            comments_table_name: comments_table_name.to_string(),
        })
    }
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct PostgresWalStreamOpts {
    #[arg(
        long,
        help = "The minimum (inclusive) sequence id to insert",
        env = "BRAINSTORE_START_SEQUENCE_ID"
    )]
    pub start_sequence_id: Option<u64>,

    #[arg(
        long,
        help = "The maximum (inclusive) sequence id to insert",
        env = "BRAINSTORE_END_SEQUENCE_ID"
    )]
    pub end_sequence_id: Option<u64>,

    #[arg(
        long,
        help = "Read from logs2 instead of standard logs. Cannot be used with --read-comments.",
        env = "BRAINSTORE_POSTGRES_WAL_READ_LOGS2"
    )]
    pub read_logs2: Option<bool>,

    #[arg(
        long,
        help = "Read comments instead of standard logs. Cannot be used with --read-logs2.",
        env = "BRAINSTORE_POSTGRES_WAL_READ_COMMENTS"
    )]
    pub read_comments: Option<bool>,
}

#[async_trait]
impl Wal for PostgresWAL {
    async fn insert<'a>(&self, scope: WALScope<'a>, wal_entries: Vec<WalEntry>) -> Result<()> {
        self.inner.insert(scope, wal_entries).await
    }

    #[instrument(err, skip(self), name = "postgres_wal_metadata_stream")]
    async fn wal_metadata_stream<'a>(
        &self,
        _scope: WALScope<'a>,
        _optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<Box<dyn WalMetadata>>>> {
        todo!("Don't call PostgresWAL::wal_metadata_stream. Use PostgresWalStreamBounded")
    }

    async fn status(&self) -> Result<String> {
        let client = self.inner.pool.get_client().await?;
        match client.query("SELECT 1", &[]).await?.into_iter().next() {
            Some(_) => Ok("PostgresWAL is ok".into()),
            None => Err(anyhow!("query returned no rows")),
        }
    }

    fn remove_local(&self) -> Result<()> {
        Ok(())
    }

    async fn delete_up_to_xact_id<'a>(
        &self,
        input: DeleteUpToXactIdInput<'a>,
        options: &DeleteUpToXactIdOptions,
    ) -> Result<DeleteFromWalStats> {
        let logs_result = self
            .inner
            .delete_up_to_xact_id(&self.table_name, &input, options)
            .await?;
        let logs2_result = self
            .inner
            .delete_up_to_xact_id(&self.table2_name, &input, options)
            .await?;

        let total_result = logs_result + logs2_result;

        tracing::info!(
            planned_num_deletes = total_result.planned_num_deletes,
            num_deletes = total_result.num_deletes,
            dry_run = input.dry_run,
            "Ran retention on postgres WAL",
        );

        Ok(total_result)
    }
}

pub struct PostgresWalStreamBounded {
    inner: Arc<dyn Wal>,
    opts: PostgresWalStreamOpts,
}

impl PostgresWalStreamBounded {
    pub fn new(inner: Arc<dyn Wal>, opts: PostgresWalStreamOpts) -> Self {
        if opts.read_comments.unwrap_or(false) && opts.read_logs2.unwrap_or(false) {
            panic!("Cannot read comments and logs2 at the same time");
        }
        Self { inner, opts }
    }
}

enum PostgresWalUnnormalizedRowData {
    String(String),
    RowRef(RowRef),
}

impl PostgresWalUnnormalizedRowData {
    fn len(&self) -> usize {
        match self {
            PostgresWalUnnormalizedRowData::String(s) => s.len(),
            PostgresWalUnnormalizedRowData::RowRef(row_ref) => {
                (row_ref.byte_range_end - row_ref.byte_range_start) as usize
            }
        }
    }
}
struct PostgresWalUnnormalizedRow {
    data: PostgresWalUnnormalizedRowData,
    audit_data: Option<String>,
    row_created: DateTime<Utc>,
    sequence_id: u64,
}

// This actually contains the whole data since we don't want to separately query for just sequence
// IDs from the DB.
struct PostgresWalMetadata {
    xact_id: TransactionId,
    total_num_bytes: usize,
    object_type: ObjectType,
    object_id: String,
    read_comments: bool,
    rows: Vec<PostgresWalUnnormalizedRow>,
    directory: AsyncDirectoryArc,
}

#[async_trait]
impl WalMetadata for PostgresWalMetadata {
    fn xact_id(&self) -> TransactionId {
        self.xact_id
    }

    fn num_bytes(&self) -> usize {
        self.total_num_bytes
    }

    async fn read_wal_entries(
        &self,
        preference: WalEntryVariantPreference,
    ) -> Result<Vec<WalEntryVariant>> {
        let row_data = join_all(self.rows.iter().map(|row| async {
            let data_json = match &row.data {
                PostgresWalUnnormalizedRowData::String(s) => {
                    deserialize_json_no_recursion_limit(s.as_bytes()).with_context(|| {
                        format!(
                            "failed to deserialize json data from postgres (sequence_id: {})",
                            row.sequence_id
                        )
                    })?
                }
                PostgresWalUnnormalizedRowData::RowRef(row_ref) => {
                    let key_path = std::path::PathBuf::from(row_ref.key.clone());
                    let file_handle = self
                        .directory
                        .async_get_file_handle(&key_path, None)
                        .await?;
                    let read_bytes = file_handle
                        .async_read_bytes(
                            row_ref.byte_range_start as usize..row_ref.byte_range_end as usize,
                        )
                        .await?;
                    // If we prefer system fields, we deserialize into a WalEntrySystemFields
                    // struct and store the full bytes for later. Skip this for comments, because
                    // `normalize_row` is necessary to transform the comment data.
                    if !self.read_comments && preference == WalEntryVariantPreference::SystemFields
                    {
                        let system_fields: WalEntrySystemFields =
                            serde_json::from_slice(&read_bytes).with_context(|| {
                                format!(
                                "failed to deserialize json data from postgres (sequence_id: {})",
                                row.sequence_id
                            )
                            })?;
                        return Ok(Some(WalEntryVariant::SystemFields {
                            system_fields,
                            full_bytes: read_bytes,
                        }));
                    }
                    deserialize_json_no_recursion_limit(&read_bytes).with_context(|| {
                        format!(
                            "failed to deserialize json data from postgres (sequence_id: {})",
                            row.sequence_id
                        )
                    })?
                }
            };
            let audit_data_json = match &row.audit_data {
                Some(s) => {
                    let value =
                        deserialize_json_no_recursion_limit(s.as_bytes()).with_context(|| {
                            format!(
                            "failed to deserialize json audit data from postgres (sequence_id: {})",
                            row.sequence_id
                        )
                        })?;
                    Some(value)
                }
                None => None,
            };
            // For the pagination key, we must use the bottom 32 bits of the
            // sequence id as an independent counter, because we are not
            // guaranteed to get all rows for a given xact_id in a single query.
            // We assume that the likelihood of one transaction spanning more
            // than 4 billion rows is low enough that this is not a problem.
            //
            // Note that RowRef-type rows should already have a
            // pagination_key filled in, so this one won't be used.
            let wal_entry = normalize_row(
                data_json,
                audit_data_json,
                self.xact_id.0,
                row.row_created,
                PaginationKeyCounter::IndependentCounter((row.sequence_id & 0xffffffff) as u32),
                &self.object_type,
                &self.object_id,
                self.read_comments,
            )?;
            Ok::<_, util::anyhow::Error>(wal_entry.map(WalEntryVariant::Full))
        }))
        .await;
        row_data
            .into_iter()
            .filter_map(|row| row.transpose())
            .collect::<Result<Vec<_>>>()
    }
}

#[async_trait]
impl Wal for PostgresWalStreamBounded {
    async fn insert<'a>(&self, scope: WALScope<'a>, wal_entries: Vec<WalEntry>) -> Result<()> {
        self.inner.insert(scope, wal_entries).await
    }

    #[instrument(
        err,
        skip(self),
        name = "postgres_wal_metadata_stream_bounded",
        fields(scope = ?scope, opts = ?self.opts, optional_input = ?optional_input)
    )]
    async fn wal_metadata_stream<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<Box<dyn WalMetadata>>>> {
        let inner_pg = match self.inner.as_ref().downcast_ref::<PostgresWAL>() {
            Some(inner_pg) => inner_pg,
            None => return Err(util::anyhow::anyhow!("Inner is not a PostgresWALInner")),
        };

        let concrete_stream = inner_pg
            .inner
            .wal_metadata_stream_concrete(
                scope,
                optional_input,
                if self.opts.read_comments.unwrap_or(false) {
                    &inner_pg.comments_table_name
                } else if self.opts.read_logs2.unwrap_or(false) {
                    &inner_pg.table2_name
                } else {
                    &inner_pg.table_name
                },
                &self.opts,
            )
            .await?;
        let ret = concrete_stream
            .map(|x| x.map(|x| Box::new(x) as Box<dyn WalMetadata>))
            .boxed();
        Ok(ret)
    }

    async fn status(&self) -> Result<String> {
        self.inner.status().await
    }

    fn remove_local(&self) -> Result<()> {
        self.inner.remove_local()
    }

    async fn delete_up_to_xact_id<'a>(
        &self,
        input: DeleteUpToXactIdInput<'a>,
        options: &DeleteUpToXactIdOptions,
    ) -> Result<DeleteFromWalStats> {
        self.inner.delete_up_to_xact_id(input, options).await
    }
}

#[derive(Serialize, Deserialize, Parser, Debug, Clone)]
pub struct PostgresWALConfig {}

struct PostgresWALInner {
    pool: PostgresPool,
    directory: AsyncDirectoryArc,
}

impl PostgresWALInner {
    pub fn new(url: &Url, directory: AsyncDirectoryArc) -> Result<Self> {
        let pool = WAL_POOLS
            .lock()
            .unwrap()
            .entry(url.clone())
            .or_insert_with(|| {
                PostgresPool::new(url, global_limits().global_store_pool_size).unwrap()
            })
            .clone();
        Ok(Self { pool, directory })
    }

    async fn insert<'a>(&self, _scope: WALScope<'a>, _wal_entries: Vec<WalEntry>) -> Result<()> {
        todo!("Inserting into Postgres WAL is not yet implemented")
    }

    #[instrument(err, skip(self), name = "postgres_wal_inner_delete_up_to_xact_id")]
    async fn delete_up_to_xact_id<'a>(
        &self,
        table_name: &str,
        input: &DeleteUpToXactIdInput<'a>,
        options: &DeleteUpToXactIdOptions,
    ) -> Result<DeleteFromWalStats> {
        let object_id = match input.scope {
            WALScope::ObjectId(object_id, _) => object_id,
            WALScope::Segment(_segment_id) => {
                return Err(anyhow!(
                    "Deleting from Postgres WAL by segment ID is not supported"
                ));
            }
        };

        let batch_size = options.batch_size;
        let object_id_value_expr = make_object_id_value_expr(&object_id, "$1")?;
        let xact_id_param = input.min_retained_xact_id.0 as i64;

        if input.dry_run {
            // Query plan (dry run):
            //
            // EXPLAIN WITH target_ids AS (
            //     SELECT DISTINCT id FROM logs
            //     WHERE make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) = make_object_id('550e8400-e29b-41d4-a716-446655440000', NULL, NULL, NULL, 'g')
            //       AND NOT _object_delete
            //       AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) < (ROW(123456789, ''::text)::_xact_id_root_span_id)
            //     LIMIT 5000
            // )
            // SELECT COUNT(*)
            // FROM target_ids t
            // JOIN LATERAL (
            //     SELECT l2.sequence_id
            //     FROM logs l2
            //     WHERE make_object_id(l2.project_id, l2.experiment_id, l2.dataset_id, l2.prompt_session_id, l2.log_id) = make_object_id('550e8400-e29b-41d4-a716-446655440000', NULL, NULL, NULL, 'g')
            //       AND l2.id = t.id
            //       AND l2._xact_id < 123456789
            //     ORDER BY l2._xact_id DESC
            // ) s ON TRUE;
            //
            // Aggregate  (cost=492.91..492.92 rows=1 width=8)
            //   CTE target_ids
            //     ->  Limit  (cost=153.25..154.61 rows=136 width=6)
            //           ->  HashAggregate  (cost=153.25..154.61 rows=136 width=6)
            //                 Group Key: logs.id
            //                 ->  Index Scan using logs_make_object_id_row_idx on logs  (cost=0.55..152.89 rows=146 width=6)
            //                       Index Cond: ((make_object_id(...) = 'global_log:550e8400-e29b-41d4-a716-446655440000'::text) AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id < '(123456789,"")'::_xact_id_root_span_id))
            //   ->  Nested Loop  (cost=0.42..337.96 rows=136 width=0)
            //         ->  CTE Scan on target_ids t  (cost=0.00..2.72 rows=136 width=32)
            //         ->  Index Scan using logs_make_object_id_id__xact_id_idx on logs l2  (cost=0.42..2.44 rows=1 width=12)
            //               Index Cond: ((make_object_id(...) = 'global_log:550e8400-e29b-41d4-a716-446655440000'::text) AND (id = t.id) AND (_xact_id < 123456789))
            let query = format!(
                r#"
                WITH target_ids AS (
                    SELECT DISTINCT id FROM {table}
                    WHERE {object_id_col} = {object_id_val}
                      AND NOT _object_delete
                      AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) < (ROW($2, ''::text)::_xact_id_root_span_id)
                    LIMIT $3
                )
                SELECT COUNT(*)
                FROM target_ids t
                JOIN LATERAL (
                    SELECT l2.sequence_id
                    FROM {table} l2
                    WHERE {object_id_col_l2} = {object_id_val}
                      AND l2.id = t.id
                      AND l2._xact_id < $2
                    ORDER BY l2._xact_id DESC
                ) s ON TRUE
                "#,
                table = table_name,
                object_id_col = make_object_id_column_expr(table_name),
                object_id_col_l2 = make_object_id_column_expr("l2"),
                object_id_val = object_id_value_expr,
            );
            let conn = self.pool.get_client().await?;
            let count_row = conn
                .query_one(
                    &query,
                    &[
                        &object_id.object_id.as_str(),
                        &xact_id_param,
                        &(options.max_num_rows as i64),
                    ],
                )
                .instrument(tracing::info_span!(
                    "delete_up_to_xact_id query",
                    dry_run = true,
                    table = table_name,
                    object_id = ?object_id,
                    xact_id = xact_id_param,
                    max_num_rows = options.max_num_rows,
                ))
                .await?;
            let total_rows: i64 = count_row.get(0);
            let planned_num_deletes = total_rows as u64;

            Ok(DeleteFromWalStats {
                planned_num_deletes,
                num_deletes: 0,
                total_bytes: 0,
            })
        } else {
            // Query plan:
            //
            // EXPLAIN WITH target_ids AS (
            //     SELECT DISTINCT id FROM logs
            //     WHERE make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) = make_object_id('550e8400-e29b-41d4-a716-446655440000', NULL, NULL, NULL, 'g')
            //       AND NOT _object_delete
            //       AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) < (ROW(123456789, ''::text)::_xact_id_root_span_id)
            //     LIMIT 5000
            // )
            // DELETE FROM logs l
            // USING target_ids t
            // JOIN LATERAL (
            //     SELECT l2.sequence_id
            //     FROM logs l2
            //     WHERE make_object_id(l2.project_id, l2.experiment_id, l2.dataset_id, l2.prompt_session_id, l2.log_id) = make_object_id('550e8400-e29b-41d4-a716-446655440000', NULL, NULL, NULL, 'g')
            //       AND t.id = l2.id
            //       AND l2._xact_id < 123456789
            //     ORDER BY l2._xact_id DESC
            // ) s ON TRUE
            // WHERE l.sequence_id = s.sequence_id;
            //
            // Delete on logs l  (cost=155.47..500.83 rows=0 width=0)
            //   CTE target_ids
            //     ->  Limit  (cost=153.25..154.61 rows=136 width=6)
            //           ->  HashAggregate  (cost=153.25..154.61 rows=136 width=6)
            //                 Group Key: logs.id
            //                 ->  Index Scan using logs_make_object_id_row_idx on logs  (cost=0.55..152.89 rows=146 width=6)
            //                       Index Cond: ((make_object_id(...) = 'global_log:550e8400-e29b-41d4-a716-446655440000'::text) AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id < '(123456789,"")'::_xact_id_root_span_id))
            //   ->  Nested Loop  (cost=0.85..346.22 rows=136 width=90)
            //         ->  Nested Loop  (cost=0.42..337.96 rows=136 width=88)
            //               ->  CTE Scan on target_ids t  (cost=0.00..2.72 rows=136 width=88)
            //               ->  Subquery Scan on s  (cost=0.42..2.45 rows=1 width=32)
            //                     ->  Index Scan using logs_make_object_id_id__xact_id_idx on logs l2  (cost=0.42..2.44 rows=1 width=12)
            //                           Index Cond: ((make_object_id(...) = 'global_log:550e8400-e29b-41d4-a716-446655440000'::text) AND (id = t.id) AND (_xact_id < 123456789))
            //         ->  Memoize  (cost=0.43..2.45 rows=1 width=10)
            //               Cache Key: s.sequence_id
            //               Cache Mode: logical
            //               ->  Index Scan using logs_pkey on logs l  (cost=0.42..2.44 rows=1 width=10)
            //                     Index Cond: (sequence_id = s.sequence_id)
            let mut total_rows = 0u64;
            loop {
                let query = format!(
                    r#"
                    WITH target_ids AS (
                        SELECT DISTINCT id FROM {table}
                        WHERE {object_id_col} = {object_id_val}
                          AND NOT _object_delete
                          AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) < (ROW($2, ''::text)::_xact_id_root_span_id)
                        LIMIT $3
                    )
                    DELETE FROM {table} l
                    USING target_ids t
                    JOIN LATERAL (
                        SELECT l2.sequence_id
                        FROM {table} l2
                        WHERE {object_id_col_l2} = {object_id_val}
                          AND t.id = l2.id
                          AND l2._xact_id < $2
                        ORDER BY l2._xact_id DESC
                    ) s ON TRUE
                    WHERE l.sequence_id = s.sequence_id
                    "#,
                    table = table_name,
                    object_id_col = make_object_id_column_expr(table_name),
                    object_id_col_l2 = make_object_id_column_expr("l2"),
                    object_id_val = object_id_value_expr,
                );
                let conn = self.pool.get_client().await?;
                let num_rows = conn
                    .execute(
                        &query,
                        &[&object_id.object_id.as_str(), &xact_id_param, &batch_size],
                    )
                    .instrument(tracing::info_span!(
                        "delete_up_to_xact_id query",
                        dry_run = false,
                        table = table_name,
                        object_id = ?object_id,
                        xact_id = xact_id_param,
                        batch_size = batch_size,
                        max_num_rows = options.max_num_rows,
                        total_rows = total_rows,
                    ))
                    .await?;
                if num_rows == 0 {
                    break;
                }

                total_rows += num_rows;
                if total_rows >= options.max_num_rows {
                    break;
                }
            }

            Ok(DeleteFromWalStats {
                planned_num_deletes: total_rows,
                num_deletes: total_rows,
                total_bytes: 0,
            })
        }
    }

    async fn wal_metadata_stream_concrete<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
        table_name: &str,
        opts: &PostgresWalStreamOpts,
    ) -> Result<BoxStream<'static, Result<PostgresWalMetadata>>> {
        let read_comments = opts.read_comments.unwrap_or(false);
        let object_id = match scope {
            WALScope::ObjectId(object_id, _) => object_id,
            WALScope::Segment(_segment_id) => {
                todo!("Streaming individual segments from Postgres WAL is not implemented")
            }
        };

        let has_audit_data = !read_comments;
        let mut query = format!(
            r#"
                SELECT data::text, {}_xact_id, sequence_id::bigint, row_created FROM "{}"
                WHERE {} = {}
            "#,
            if has_audit_data {
                "audit_data::text, "
            } else {
                ""
            },
            table_name,
            make_object_id_column_expr(table_name),
            make_object_id_value_expr(&object_id, "$1::text")?,
        );
        let object_id_str = object_id.object_id.as_str();

        if let Some(start_xact_id) = optional_input.start_xact_id {
            query = format!("{} AND _xact_id >= {}", query, start_xact_id.0);
        }
        if let Some(end_xact_id) = optional_input.end_xact_id {
            query = format!("{} AND _xact_id <= {}", query, end_xact_id.0);
        }

        if let Some(start_sequence_id) = opts.start_sequence_id {
            query = format!("{} AND sequence_id >= {}", query, start_sequence_id);
        }

        if let Some(end_sequence_id) = opts.end_sequence_id {
            query = format!("{} AND sequence_id <= {}", query, end_sequence_id);
        }

        // Order by _xact_id first to make sure rows are grouped by _xact_id, and next by
        // sequence_id to ensure that the stream order is deterministic.
        query = format!("{} ORDER BY _xact_id ASC, sequence_id ASC", query);

        let conn = self.pool.get_client().await?;
        let rows = conn
            // TODO: Escape the logs table name properly
            .query_raw(&query, &[&object_id_str])
            .instrument(tracing::info_span!(
                "run query",
                query = query,
                object_id = object_id_str,
                start_sequence_id = opts.start_sequence_id,
                end_sequence_id = opts.end_sequence_id
            ))
            .await?;
        tracing::debug!(
            start_sequence_id = opts.start_sequence_id,
            end_sequence_id = opts.end_sequence_id,
            "PostgresWAL finished running query"
        );

        let object_type = object_id.object_type;
        let object_id = object_id.object_id.to_string();
        let directory = self.directory.clone();
        let start_sequence_id = opts.start_sequence_id;
        let end_sequence_id = opts.end_sequence_id;
        let ret = stream! {
            tokio::pin!(rows);

            let mut cur_metadata: Option<PostgresWalMetadata> = None;
            let mut total_num_rows = 0;

            while let Some(row) = rows.next().await {
                let row = row?;
                let xact_id: u64 = {
                    let x: i64 = row.get("_xact_id");
                    x as u64
                };
                let sequence_id: u64 = {
                    let x: i64 = row.get("sequence_id");
                    x as u64
                };
                let row_created: DateTime<Utc> = row.get("row_created");

                cur_metadata = match cur_metadata {
                    None => None,
                    Some(cur_metadata_inner) => {
                        if cur_metadata_inner.xact_id.0 != xact_id {
                            yield Ok(cur_metadata_inner);
                            None
                        } else {
                            Some(cur_metadata_inner)
                        }
                    }
                };

                let data = {
                    let data: String = row.get("data");
                    make_unnormalized_row_data(data, sequence_id)?
                };
                let audit_data = if has_audit_data {
                    let data: Option<String> = row.get("audit_data");
                    data
                } else {
                    None
                };
                if cur_metadata.is_none() {
                    cur_metadata = Some(PostgresWalMetadata {
                        xact_id: TransactionId(xact_id),
                        total_num_bytes: 0,
                        object_type,
                        object_id: object_id.clone(),
                        read_comments,
                        rows: vec![],
                        directory: directory.clone(),
                    });
                }
                let cur_metadata_inner = cur_metadata.as_mut().unwrap();
                cur_metadata_inner.total_num_bytes += data.len();
                cur_metadata_inner.rows.push(PostgresWalUnnormalizedRow {
                    data,
                    audit_data,
                    row_created,
                    sequence_id,
                });
                total_num_rows += 1;
            }
            tracing::debug!(total_num_rows = total_num_rows, start_sequence_id = start_sequence_id, end_sequence_id = end_sequence_id, "Finished streaming rows");
            if let Some(cur_metadata) = cur_metadata {
                yield Ok(cur_metadata);
            }
        }
        .instrument(tracing::info_span!("produce PostgresWAL entries", start_sequence_id = start_sequence_id, end_sequence_id = end_sequence_id))
        .boxed();
        Ok(ret)
    }
}

// Keep this in sync with the RowRef type in local/js/api-schema/insert_schemas.ts.
#[derive(Serialize, Deserialize, Debug, Clone)]
struct RowRef {
    key: String,
    byte_range_start: u64,
    byte_range_end: u64,
}

// We parse the data String into this partial struct to try to extract just the
// _row_ref field, if one exists.
#[derive(Deserialize)]
struct MaybeRowRef {
    // Struct field matches ROW_REF_FIELD in local/js/constants.ts.
    _row_ref: Option<RowRef>,
}

// Some rows in postgres now merely contain a reference to a data blob in the
// (realtime) object WAL. In this case, we resolve the row ref without parsing
// the entire data and return the RowRef. Otherwise, we return the original data
// String.
fn make_unnormalized_row_data(
    data: String,
    sequence_id: u64,
) -> Result<PostgresWalUnnormalizedRowData> {
    let maybe_row_ref: MaybeRowRef = serde_json::from_str(&data).with_context(|| {
        format!(
            "failed to deserialize optional row ref from postgres (sequence_id: {})",
            sequence_id
        )
    })?;
    match maybe_row_ref._row_ref {
        Some(row_ref) => Ok(PostgresWalUnnormalizedRowData::RowRef(row_ref)),
        None => Ok(PostgresWalUnnormalizedRowData::String(data)),
    }
}

// Keep this in sync with the normalization logic in api-ts/src/brainstore/wal.ts.
fn normalize_row(
    mut data_json: serde_json::Value,
    audit_data_json: Option<serde_json::Value>,
    xact_id: u64,
    row_created: DateTime<Utc>,
    pagination_key_counter: PaginationKeyCounter,
    object_type: &ObjectType,
    object_id: &str,
    is_comments_row: bool,
) -> Result<Option<WalEntry>> {
    let data_json_mut = data_json.as_object_mut().expect("data is not an object");
    data_json_mut.remove("_xact_id");
    data_json_mut.insert(
        "_object_type".to_owned(),
        serde_json::Value::String(object_type.to_string()),
    );
    data_json_mut.insert(
        "_object_id".to_owned(),
        serde_json::Value::String(object_id.to_string()),
    );

    // Add the audit data if it exists.
    if let Some(audit_data_json) = audit_data_json {
        if audit_data_json.as_object().is_none() {
            tracing::warn!("audit data is not an object object_type {}, object_id {}, xact_id {}, skipping audit data", object_type, object_id, xact_id);
        } else {
            data_json_mut.insert("_self_audit_data".to_owned(), audit_data_json);
        }
    }

    // This must be kept in sync with api-ts/src/schema.ts:getStorageToLogicalMap
    if let Some(inputs) = data_json_mut.remove("inputs") {
        data_json_mut.insert("input".to_owned(), inputs);
    }
    if matches!(object_type, ObjectType::Dataset) {
        if let Some(output) = data_json_mut.remove("output") {
            data_json_mut.insert("expected".to_owned(), output);
        }
    }

    // Apply legacy sanitizers.

    if normalize_dt_field(data_json_mut, "created").is_err() {
        // If we could not parse the `created` field, fall back to `row_created`.
        data_json_mut.insert(
            "created".to_owned(),
            serde_json::Value::String(row_created.to_rfc3339()),
        );
    }
    normalize_str_field(data_json_mut, "id")?;
    normalize_list_field(data_json_mut, "tags")?;

    if is_comments_row {
        // For comments, we use the origin ID as the main ID, and move the whole `data` object into
        // `{ comments: [ data ] }`.
        let _object_type = match data_json_mut.remove("_object_type").unwrap() {
            serde_json::Value::String(s) => s,
            _ => unreachable!(),
        };
        let _object_id = match data_json_mut.remove("_object_id").unwrap() {
            serde_json::Value::String(s) => s,
            _ => unreachable!(),
        };
        let origin_id =
            (|data_json_mut: &serde_json::Map<String, serde_json::Value>| -> Result<String> {
                Ok(data_json_mut
                    .get("origin")
                    .ok_or_else(|| anyhow!("missing origin"))?
                    .as_object()
                    .ok_or_else(|| anyhow!("origin is not an object"))?
                    .get("id")
                    .ok_or_else(|| anyhow!("missing origin id"))?
                    .as_str()
                    .ok_or_else(|| anyhow!("origin id is not a string"))?
                    .to_owned())
            })(data_json_mut);
        let origin_id = match origin_id {
            Ok(origin_id) => origin_id,
            Err(e) => {
                tracing::warn!(error = ?e, "error getting origin id at object_type {}, object_id {}, xact_id {}, skipping comment", _object_type, _object_id, xact_id);
                return Ok(None);
            }
        };

        data_json_mut.insert(
            "_xact_id".to_owned(),
            serde_json::Value::Number(xact_id.into()),
        );
        let orig_data = std::mem::take(&mut data_json);
        data_json = serde_json::json!({
            "id": origin_id,
            "_object_type": _object_type,
            "_object_id": _object_id,
            "_is_standalone_comment": true,
            "comments": [orig_data],
        });
    }

    let xact_id = TransactionId(xact_id);
    let wal_entry = crate::wal::normalize_row(xact_id, pagination_key_counter, data_json, 0)?;

    Ok(Some(wal_entry))
}
