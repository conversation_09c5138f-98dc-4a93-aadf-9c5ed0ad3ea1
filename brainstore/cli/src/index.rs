use std::{collections::HashSet, str::FromStr, sync::Arc};

use clap::{Parser, Subcommand};

use async_util::spawn_blocking_util::spawn_blocking_with_async_timeout;
use futures::future::join_all;
use query::interpreter::context::InterpreterOpts;
use serde::{Deserialize, Serialize};
use serde_json::json;
use storage::{
    index_document::make_full_schema,
    index_wal_reader::{IndexWalReaderInput, IndexWalReaderOptionalInput},
    merge::{
        MergeTantivySegmentsInput, MergeTantivySegmentsOptionalInput, MergeTantivySegmentsOptions,
    },
    optimize_tantivy_index::{
        OptimizationLoopType, OptimizeAllObjectsOptions, OptimizeObjectOptions,
    },
    segment_batches::SegmentBatchingSortSpec,
    tantivy_index::TantivyIndexWriterOpts,
};
use tantivy::directory::DirectoryClone;
use tracing::{instrument, Instrument};
use util::{
    anyhow::{self, Context, Result},
    ptree::MakePTree,
    system_types::{make_object_schema, FullObjectIdOwned},
    tracer::{trace_if, trace_if_async, EnterTraceGuard},
};

use crate::{
    base::{self, AppState, CLIArgs, SegmentIdArgs},
    btql::default_num_query_threads,
};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum IndexCommands {
    Merge(CLIArgs<MergeIndexFullArgs>),
    Delete(CLIArgs<DeleteIndexArgs>),
    Optimize(CLIArgs<OptimizeIndexFullArgs>),
    OptimizeLoop(CLIArgs<OptimizeAllObjectsFullArgs>),
    OpenReader(CLIArgs<OpenReaderArgs>),
}

pub fn base_args(args: &IndexCommands) -> &base::BaseArgs {
    match args {
        IndexCommands::Merge(a) => &a.base,
        IndexCommands::Delete(a) => &a.base,
        IndexCommands::Optimize(a) => &a.base,
        IndexCommands::OptimizeLoop(a) => &a.base,
        IndexCommands::OpenReader(a) => &a.base,
    }
}

pub fn verbose(args: &IndexCommands) -> u8 {
    match args {
        IndexCommands::Merge(a) => a.base.verbose,
        IndexCommands::Delete(a) => a.base.verbose,
        IndexCommands::Optimize(a) => a.base.verbose,
        IndexCommands::OptimizeLoop(a) => a.base.verbose,
        IndexCommands::OpenReader(a) => a.base.verbose,
    }
}

pub async fn main(args: IndexCommands) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        IndexCommands::Merge(a) => merge_index(app_state, a.args).await,
        IndexCommands::Delete(a) => delete_index(app_state, a.args).await,
        IndexCommands::Optimize(a) => optimize_index(app_state, a.args).await,
        IndexCommands::OptimizeLoop(a) => optimize_all_objects(app_state, a.args).await,
        IndexCommands::OpenReader(a) => open_reader_command(a).await,
    }
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct MergeIndexFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub merge_input: MergeIndexInputArgs,

    #[command(flatten)]
    #[serde(default)]
    pub writer_opts: TantivyIndexWriterOpts,

    #[command(flatten)]
    #[serde(default)]
    pub process_wal_opts: storage::process_wal::ProcessObjectWalOptions,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct MergeIndexInputArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub segment_id_args: base::SegmentIdArgs,

    #[arg(short, long, default_value_t = false)]
    #[serde(default)]
    pub dry_run: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub merge_opts: storage::merge::MergeOpts,

    #[arg(
        short,
        long,
        default_value_t = false,
        help = "If true, then try acquiring a lock for the segment, and bail out quickly if we can't."
    )]
    #[serde(default)]
    pub try_acquire: bool,

    #[arg(
        short,
        long,
        help = "For testing, add a sleep to the merge to extend its length, once it's acquired the lock."
    )]
    #[serde(default)]
    pub testing_sleep_ms: Option<usize>,
}

#[instrument(err, name = "merge_index", skip(app_state))]
pub async fn merge_index(
    app_state: Arc<AppState>,
    args: MergeIndexFullArgs,
) -> Result<util::Value> {
    let segment_ids = args
        .merge_input
        .segment_id_args
        .segment_ids(app_state.config.global_store.as_ref())
        .await?;
    let segment_id_to_object_id = args
        .merge_input
        .segment_id_args
        .segment_id_to_object_id(&segment_ids, app_state.config.global_store.as_ref())
        .await?;

    let span = tracing::Span::current();

    let results = join_all(segment_ids.iter().map(|segment_id| {
        let object_id = &segment_id_to_object_id[segment_id];
        let config = app_state.config.clone();
        let merge_opts = args.merge_input.merge_opts.clone();
        let writer_opts = args.writer_opts.clone();
        let process_wal_opts = args.process_wal_opts.clone();
        let _guard = span.enter();

        async move {
            storage::merge::merge_tantivy_segments(
                MergeTantivySegmentsInput {
                    segment_id: *segment_id,
                    config: config.clone(),
                    dry_run: args.merge_input.dry_run,
                    try_acquire: args.merge_input.try_acquire,
                },
                MergeTantivySegmentsOptionalInput {
                    use_status_updater: true,
                    override_object_type_label: Some(object_id.object_type.to_string()),
                    testing_sleep_ms: args.merge_input.testing_sleep_ms,
                    ..Default::default()
                },
                MergeTantivySegmentsOptions {
                    merge_opts: merge_opts.clone(),
                    writer_opts: writer_opts.clone(),
                    process_wal_opts: process_wal_opts.clone(),
                },
            )
            .await
        }
        .instrument(tracing::info_span!("merge_index", segment_id = ?segment_id))
    }))
    .instrument(tracing::Span::current())
    .await
    .into_iter()
    .collect::<Result<Vec<_>>>()?;

    let result_map = segment_ids
        .into_iter()
        .zip(results)
        .map(|(segment_id, result)| (segment_id.to_string(), json!(result)))
        .collect::<serde_json::Map<String, _>>();

    Ok(util::Value::Object(result_map))
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct DeleteIndexArgs {
    #[arg(short, long, env = "BRAINSTORE_OBJECT_IDS")]
    pub object_ids: Vec<String>,
}

#[instrument(err, skip(app_state))]
pub async fn delete_index(app_state: Arc<AppState>, args: DeleteIndexArgs) -> Result<util::Value> {
    let object_ids = args
        .object_ids
        .iter()
        .map(|id| FullObjectIdOwned::from_str(id))
        .collect::<Result<Vec<_>>>()?;

    let object_id_refs = object_ids.iter().map(|id| id.as_ref()).collect::<Vec<_>>();

    storage::delete_objects::delete_objects(storage::delete_objects::DeleteObjectInput {
        object_ids: &object_id_refs,
        global_store: app_state.config.global_store.as_ref(),
    })
    .await?;

    Ok(util::Value::Null)
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct OptimizeIndexInput {
    #[command(flatten)]
    #[serde(flatten)]
    pub segment_id_args: SegmentIdArgs,

    /// If true, will not actually perform the optimization steps, but will instead return the steps
    /// that would be performed.
    #[arg(long, default_value_t = false)]
    #[serde(default)]
    pub dry_run: bool,

    #[arg(
        long,
        default_value_t = false,
        help = "Recompact segments from scratch rather than continuing from the last processed transaction"
    )]
    #[serde(default)]
    pub recompact: bool,

    #[arg(long, default_value_t = false)]
    #[serde(default)]
    #[serde(rename = "async")]
    pub run_async: bool,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct OptimizeIndexFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub optimize_input: OptimizeIndexInput,

    #[command(flatten)]
    #[serde(flatten)]
    pub optimize_options: OptimizeObjectOptions,
}

#[instrument(err, name = "optimize_index", skip(app_state))]
pub async fn optimize_index(
    app_state: Arc<AppState>,
    args: OptimizeIndexFullArgs,
) -> Result<util::Value> {
    let segment_ids = args
        .optimize_input
        .segment_id_args
        .segment_ids(app_state.config.global_store.as_ref())
        .await?;

    let segment_id_to_object_id = args
        .optimize_input
        .segment_id_args
        .segment_id_to_object_id(&segment_ids, app_state.config.global_store.as_ref())
        .await?;

    let object_ids = segment_id_to_object_id
        .values()
        .cloned()
        .collect::<HashSet<_>>();
    if object_ids.len() == 0 {
        return Ok(util::Value::Null);
    } else if object_ids.len() > 1 {
        return Err(anyhow::anyhow!(
            "Cannot optimize multiple objects at once. Found {} objects: {:?}",
            object_ids.len(),
            object_ids
        ));
    }
    let object_id = object_ids.into_iter().next().unwrap();

    let schema = make_full_schema(&match &app_state.config_schema {
        Some(schema) => schema.clone(),
        None => make_object_schema(object_id.object_type)?,
    })?;

    let output = storage::optimize_tantivy_index::optimize_segments(
        storage::optimize_tantivy_index::OptimizeObjectInput {
            segment_ids: &segment_ids,
            config: app_state.config.clone(),
            schema,
            dry_run: args.optimize_input.dry_run,
            recompact: args.optimize_input.recompact,
            run_async: args.optimize_input.run_async,
            file_cache_opts: app_state.base_args.file_cache_opts.clone(),
            storage_config: app_state.storage_config.clone(),
        },
        args.optimize_options,
    )
    .await?;

    Ok(json!(output))
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct OptimizeAllObjectsInput {
    #[arg(
        long,
        default_value = "long-term",
        env = "BRAINSTORE_OPTIMIZE_ALL_OBJECTS_LOOP_TYPE",
        help = "The type of optimization loop to run. Can be 'long_term' or 'short_term'."
    )]
    pub loop_type: OptimizationLoopType,

    #[arg(
        long,
        env = "BRAINSTORE_OPTIMIZE_ALL_OBJECTS_ITERATIONS",
        help = "The maximum number of times to run the optimization loop."
    )]
    #[serde(default)]
    pub iterations: Option<u64>,

    #[arg(
        long,
        env = "BRAINSTORE_OPTIMIZE_ALL_OBJECTS_IGNORE_OLD_SEGMENT_OBJECT_IDS",
        help = "Comma-separated list of object IDs to skip old segments for during optimization"
    )]
    #[serde(default)]
    pub ignore_old_segment_object_ids: Option<String>,
}

impl Default for OptimizeAllObjectsInput {
    fn default() -> Self {
        Self {
            loop_type: OptimizationLoopType::LongTerm,
            iterations: None,
            ignore_old_segment_object_ids: None,
        }
    }
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct OptimizeAllObjectsFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub input: OptimizeAllObjectsInput,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: OptimizeAllObjectsOptions,
}

#[instrument(err, skip(app_state))]
pub async fn optimize_all_objects(
    app_state: Arc<AppState>,
    args: OptimizeAllObjectsFullArgs,
) -> Result<util::Value> {
    // Parse comma-separated list of object ID patterns to skip. Error out if the parsing fails.
    let ignore_old_segment_object_ids: HashSet<FullObjectIdOwned> = args
        .input
        .ignore_old_segment_object_ids
        .as_ref()
        .map(|pattern| {
            let res: Result<HashSet<FullObjectIdOwned>> = pattern
                .split(',')
                .map(|s| s.trim())
                .map(|s| s.parse::<FullObjectIdOwned>())
                .collect();
            res.with_context(|| format!("invalid object ID pattern: {}", pattern))
        })
        .transpose()?
        .unwrap_or_default();
    if !ignore_old_segment_object_ids.is_empty() {
        log::info!(
            "[optimize_all_objects] ignoring old segments from object IDs: {:#?}",
            ignore_old_segment_object_ids
        );
    }

    let input = storage::optimize_tantivy_index::OptimizeAllObjectsInput {
        config: app_state.config.clone(),
        schema: app_state.config_schema.clone(),
        loop_config: args.input.loop_type.to_config(),
        max_iterations: args.input.iterations,
        file_cache_opts: app_state.base_args.file_cache_opts.clone(),
        storage_config: app_state.storage_config.clone(),
        ignore_old_segment_object_ids,
    };

    storage::optimize_tantivy_index::run_optimization_loop(input, args.options.clone()).await?;

    Ok(util::Value::Null)
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct OpenReaderArgs {
    #[arg(help = "The object id to open the reader for")]
    pub object_id: FullObjectIdOwned,

    #[arg(
        long,
        default_value_t = default_num_query_threads(),
        help = "The number of threads to use (defaults to 64)",
        env = "BRAINSTORE_BTQL_NUM_THREADS"
    )]
    #[serde(default = "default_num_query_threads")]
    pub num_threads: usize,

    #[command(flatten)]
    #[serde(flatten)]
    pub interpreter_opts: InterpreterOpts,

    #[arg(long, default_value_t = false)]
    #[serde(default)]
    pub load_segment_footer: bool,

    #[arg(
        long,
        default_value_t = 1,
        help = "The number of times to run the open reader command"
    )]
    pub iters: usize,
}

#[instrument(err, level = "info", skip(args))]
async fn open_reader_command(args: CLIArgs<OpenReaderArgs>) -> Result<util::Value> {
    let app_state = AppState::new(&args.base)?;

    for _ in 0..args.args.iters {
        let object_id = args.args.object_id.clone();
        let object_ids = vec![object_id.clone()];
        let schema = make_full_schema(&match &app_state.config_schema {
            Some(schema) => schema.clone(),
            None => make_object_schema(object_id.object_type)?,
        })?;

        let executor = Arc::new(query::interpreter::context::Executor::multi_thread(
            args.args.num_threads,
            "btql",
        )?);

        let expr_ctx = btql::interpreter::context::ExprContext::new_with_executor(
            executor.clone(),
            args.args.interpreter_opts.tz_offset.clone(),
        );

        let ctx = query::interpreter::InterpreterContext::new(
            app_state.config.clone(),
            schema,
            executor,
            tokio::runtime::Handle::current(),
            args.args.interpreter_opts.clone(),
            expr_ctx,
            None,
            None,
        );

        let tracer = Some(ctx.root_tracer.clone());

        let filters = vec![];
        let sort = Some(SegmentBatchingSortSpec {
            field: "_pagination_key".to_string(),
            descending: true,
        });
        let index_wal_reader = trace_if_async(
            log::Level::Info,
            &tracer,
            "Open index wal reader",
            |child| {
                storage::index_wal_reader::IndexWalReader::new(
                    IndexWalReaderInput {
                        config_with_store: &app_state.config,
                        full_schema: ctx.schema.clone(),
                        object_ids: &object_ids,
                        filters: &filters,
                        sort: &sort,
                        partition_all: false,
                        sampling: None,
                    },
                    IndexWalReaderOptionalInput {
                        tantivy_executor: Some(ctx.executor.clone()),
                        tracer: child,
                        ..Default::default()
                    },
                    ctx.opts.index_wal_reader_opts.clone(),
                )
            },
        )
        .await?;

        let handle = ctx.handle.clone();
        let executor = ctx.executor.clone();

        let directory = app_state.config.index.directory.clone();
        directory.reset_timing();

        spawn_blocking_with_async_timeout(
            &handle,
            move || {
                let (directory, _search_query) = index_wal_reader.directory_with_sort_filters(
                    0,
                    Box::new(tantivy::query::AllQuery),
                    None,
                );
                if args.args.load_segment_footer {
                    let start = std::time::Instant::now();
                    directory.load_segment_footer_blocking(tracer.clone())?;
                    log::info!("Loaded segment footer in {:?}", start.elapsed());
                }

                let mut tantivy_index = tantivy::Index::open(directory.box_clone())?;
                tantivy_index.set_shared_multithread_executor(executor.clone())?;
                trace_if(log::Level::Info, &tracer, "Open reader", |child| {
                    child.increment_counter(
                        "num_chunks",
                        tantivy_index.searchable_segment_metas()?.len() as u64,
                    );
                    let reader_builder = tantivy_index.reader_builder();
                    let reader = reader_builder.try_into()?;
                    Ok::<_, util::anyhow::Error>(reader)
                })?;
                Ok::<_, util::anyhow::Error>(())
            },
            Default::default(),
            || "execute tantivy search".into(),
        )
        .await???;

        let execution_tree = ctx.finish().into_tree();
        log::info!("Execution tree:\n{}", execution_tree.format_tree().unwrap());

        if log::log_enabled!(log::Level::Debug) {
            eprintln!("\n----------DONE------------------\n");
            eprintln!("Index timers (after opening reader)");
            storage::instrumented::format_timers(directory.as_dyn_instrumented());
            if log::log_enabled!(log::Level::Debug) {
                storage::instrumented::format_cache_metrics(directory.as_dyn_instrumented());
            }
        }
    }

    Ok(util::Value::Null)
}
