[{"error": null, "query": "/*!result -- Basic rate sampling (25% of 100 = ~25 rows)\nlength >= 20 and length <= 35\n*/\nselect: id | sample: 25% seed 1", "result_rows": [{"id": 100}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 21}, {"id": 22}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 33}, {"id": 3}, {"id": 40}, {"id": 41}, {"id": 48}, {"id": 49}, {"id": 4}, {"id": 50}, {"id": 51}, {"id": 53}, {"id": 58}, {"id": 68}, {"id": 88}, {"id": 96}, {"id": 97}], "skip": false}, {"error": null, "query": "/*!result -- Basic count sampling (exactly 10 rows)\nlength == 10\n*/\nselect: id | sample: 10 seed 2", "result_rows": [{"id": 100}, {"id": 13}, {"id": 22}, {"id": 25}, {"id": 41}, {"id": 66}, {"id": 6}, {"id": 73}, {"id": 88}, {"id": 96}], "skip": false}, {"error": null, "query": "/*!result -- Rate sampling (30% of 100 = ~30 rows)\nlength >= 20 and length <= 40\n*/\nselect: id | sample: 30% seed 3", "result_rows": [{"id": 13}, {"id": 16}, {"id": 17}, {"id": 22}, {"id": 25}, {"id": 26}, {"id": 28}, {"id": 2}, {"id": 31}, {"id": 35}, {"id": 37}, {"id": 39}, {"id": 43}, {"id": 44}, {"id": 46}, {"id": 48}, {"id": 52}, {"id": 55}, {"id": 57}, {"id": 59}, {"id": 61}, {"id": 67}, {"id": 68}, {"id": 69}, {"id": 75}, {"id": 7}, {"id": 81}, {"id": 82}, {"id": 86}, {"id": 98}], "skip": false}, {"error": null, "query": "/*!result -- Simple rate sampling (60% of 100 = ~60 rows)\nlength >= 50 and length <= 70\n*/\nselect: id | sample: 60% seed 4", "result_rows": [{"id": 100}, {"id": 12}, {"id": 15}, {"id": 17}, {"id": 18}, {"id": 1}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 30}, {"id": 31}, {"id": 32}, {"id": 35}, {"id": 37}, {"id": 39}, {"id": 42}, {"id": 43}, {"id": 45}, {"id": 47}, {"id": 48}, {"id": 50}, {"id": 51}, {"id": 52}, {"id": 53}, {"id": 56}, {"id": 57}, {"id": 58}, {"id": 60}, {"id": 61}, {"id": 62}, {"id": 63}, {"id": 64}, {"id": 65}, {"id": 68}, {"id": 69}, {"id": 6}, {"id": 70}, {"id": 74}, {"id": 76}, {"id": 78}, {"id": 81}, {"id": 83}, {"id": 84}, {"id": 86}, {"id": 87}, {"id": 89}, {"id": 8}, {"id": 92}, {"id": 93}, {"id": 94}, {"id": 95}, {"id": 96}, {"id": 97}, {"id": 98}, {"id": 99}, {"id": 9}], "skip": false}, {"error": null, "query": "/*!result -- Higher rate sampling (80% of 100 = ~80 rows)\nlength >= 70 and length <= 90\n*/\nselect: id | sample: 80% seed 5", "result_rows": [{"id": 100}, {"id": 10}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 29}, {"id": 2}, {"id": 30}, {"id": 31}, {"id": 33}, {"id": 34}, {"id": 36}, {"id": 37}, {"id": 38}, {"id": 39}, {"id": 3}, {"id": 40}, {"id": 43}, {"id": 44}, {"id": 45}, {"id": 46}, {"id": 47}, {"id": 48}, {"id": 49}, {"id": 4}, {"id": 51}, {"id": 52}, {"id": 54}, {"id": 55}, {"id": 56}, {"id": 57}, {"id": 59}, {"id": 5}, {"id": 60}, {"id": 61}, {"id": 62}, {"id": 63}, {"id": 64}, {"id": 65}, {"id": 66}, {"id": 67}, {"id": 68}, {"id": 69}, {"id": 6}, {"id": 72}, {"id": 73}, {"id": 74}, {"id": 75}, {"id": 76}, {"id": 77}, {"id": 79}, {"id": 7}, {"id": 81}, {"id": 82}, {"id": 83}, {"id": 84}, {"id": 85}, {"id": 86}, {"id": 88}, {"id": 89}, {"id": 90}, {"id": 91}, {"id": 94}, {"id": 95}, {"id": 96}, {"id": 97}, {"id": 98}, {"id": 99}, {"id": 9}], "skip": false}, {"error": null, "query": "/*!result -- Count sampling (exactly 20 rows)\nlength == 20\n*/\nselect: id | sample: 20 seed 6", "result_rows": [{"id": 18}, {"id": 22}, {"id": 23}, {"id": 32}, {"id": 43}, {"id": 45}, {"id": 47}, {"id": 49}, {"id": 51}, {"id": 54}, {"id": 59}, {"id": 65}, {"id": 67}, {"id": 6}, {"id": 70}, {"id": 77}, {"id": 79}, {"id": 7}, {"id": 80}, {"id": 94}], "skip": false}, {"error": null, "query": "/*!result -- Combined rate sampling (35% = ~35 rows)\nlength >= 25 and length <= 45\n*/\nselect: id | sample: 35% seed 7", "result_rows": [{"id": 100}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 17}, {"id": 18}, {"id": 21}, {"id": 24}, {"id": 25}, {"id": 28}, {"id": 29}, {"id": 38}, {"id": 3}, {"id": 43}, {"id": 47}, {"id": 49}, {"id": 4}, {"id": 50}, {"id": 51}, {"id": 53}, {"id": 57}, {"id": 58}, {"id": 66}, {"id": 72}, {"id": 76}, {"id": 77}, {"id": 80}, {"id": 81}, {"id": 83}, {"id": 86}, {"id": 87}, {"id": 94}, {"id": 95}, {"id": 9}], "skip": false}, {"error": null, "query": "/*!result -- Rate sampling with selection (45% = ~45 rows)\nlength >= 35 and length <= 55\n*/\nselect: id, scores.foo | sample: 45% seed 8", "result_rows": [{"foo": -0.601, "id": 16}, {"foo": -0.6542, "id": 14}, {"foo": -0.7074, "id": 12}, {"foo": -0.7606, "id": 10}, {"foo": -0.8404, "id": 7}, {"foo": -0.867, "id": 6}, {"foo": -0.9468, "id": 3}, {"foo": 0, "id": 21}, {"foo": 0.0523, "id": 26}, {"foo": 0.0667, "id": 28}, {"foo": 0.0889, "id": 31}, {"foo": 0.1045, "id": 33}, {"foo": 0.1123, "id": 34}, {"foo": 0.1189, "id": 35}, {"foo": 0.1345, "id": 37}, {"foo": 0.1489, "id": 39}, {"foo": 0.1645, "id": 41}, {"foo": 0.1867, "id": 44}, {"foo": 0.2189, "id": 46}, {"foo": 0.2223, "id": 47}, {"foo": 0.2312, "id": 50}, {"foo": 0.2867, "id": 53}, {"foo": 0.3434, "id": 56}, {"foo": 0.4001, "id": 59}, {"foo": 0.4379, "id": 61}, {"foo": 0.4568, "id": 62}, {"foo": 0.5135, "id": 65}, {"foo": 0.5702, "id": 68}, {"foo": 0.5891, "id": 69}, {"foo": 0.608, "id": 70}, {"foo": 0.6647, "id": 73}, {"foo": 0.6836, "id": 74}, {"foo": 0.797, "id": 80}, {"foo": 0.8159, "id": 81}, {"foo": 0.8378, "id": 82}, {"foo": 0.8756, "id": 84}, {"foo": 0.9392, "id": 88}, {"foo": 0.9551, "id": 89}, {"foo": 0.9665, "id": 92}, {"foo": 0.9703, "id": 93}, {"foo": 0.987, "id": 96}, {"foo": 0.998, "id": 97}, {"foo": 0.999, "id": 98}, {"foo": 0.9995, "id": 99}, {"foo": 1, "id": 100}], "skip": false}, {"error": null, "query": "/*!result -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)\nlength <= 20\n*/\nfilter: scores.foo > -0.5 | select: id | sample: 50% seed 9 | limit: 20", "result_rows": [{"id": 23}, {"id": 26}, {"id": 28}, {"id": 30}, {"id": 32}, {"id": 49}, {"id": 53}, {"id": 61}, {"id": 63}, {"id": 64}, {"id": 65}, {"id": 69}, {"id": 74}, {"id": 77}, {"id": 82}, {"id": 86}, {"id": 89}, {"id": 90}, {"id": 93}, {"id": 99}], "skip": false}]