This dataset contains traces that intentionally omit the root span.

- Each event has `id`, `root_span_id`, and `span_id` populated.
- All spans are non-root; there is no event where `span_id == root_span_id`.
- Useful for testing behavior when root spans are missing.

Files:

- `events.jsonl`: Minimal events with two traces, each missing the root span.
- `brainstore.yaml`: Basic store configuration mirroring the `summary` example.
- `params.yaml`: Matches the structure of the `summary` example.
