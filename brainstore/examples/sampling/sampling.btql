/*!result_meta -- Basic rate sampling (25% of 100 = ~25 rows)
length >= 20 and length <= 35
*/
from: experiment('singleton') | select: id | sample: 25% seed 1;

/*!result_meta -- Basic count sampling (exactly 10 rows)
length == 10
*/
from: experiment('singleton') | select: id | sample: 10 seed 2;

/*!result_meta -- Rate sampling (30% of 100 = ~30 rows)
length >= 20 and length <= 40
*/
from: experiment('singleton') | select: id | sample: 30% seed 3;

/*!result_meta -- Simple rate sampling (60% of 100 = ~60 rows)
length >= 50 and length <= 70
*/
from: experiment('singleton') | select: id | sample: 60% seed 4;

/*!result_meta -- Higher rate sampling (80% of 100 = ~80 rows)
length >= 70 and length <= 90
*/
from: experiment('singleton') | select: id | sample: 80% seed 5;

/*!result_meta -- Count sampling (exactly 20 rows)
length == 20
*/
from: experiment('singleton') | select: id | sample: 20 seed 6;

/*!result_meta -- Combined rate sampling (35% = ~35 rows)
length >= 25 and length <= 45
*/
from: experiment('singleton') | select: id | sample: 35% seed 7;

/*!result_meta -- Rate sampling with selection (45% = ~45 rows)
length >= 35 and length <= 55
*/
from: experiment('singleton') | select: id, scores.foo | sample: 45% seed 8;

/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)
length <= 20
*/
from: experiment('singleton') | filter: scores.foo > -0.5 | select: id | sample: 50% seed 9 | limit: 20;

/*!result_meta -- Basic rate sampling (25% of 37 = ~9 rows)
length >= 7 and length <= 12
*/
from: experiment('singleton') summary | select: id | sample: 25% seed 1;

/*!result_meta -- Basic count sampling (exactly 10 rows)
length == 10
*/
from: experiment('singleton') summary | select: id | sample: 10 seed 2;

/*!result_meta -- Rate sampling (30% of 37 = ~11 rows)
length >= 9 and length <= 14
*/
from: experiment('singleton') summary | select: id | sample: 30% seed 3;

/*!result_meta -- Simple rate sampling (60% of 37 = ~22 rows)
length >= 19 and length <= 25
*/
from: experiment('singleton') summary | select: id | sample: 60% seed 4;

/*!result_meta -- Higher rate sampling (80% of 37 = ~30 rows)
length >= 27 and length <= 32
*/
from: experiment('singleton') summary | select: id | sample: 80% seed 5;

/*!result_meta -- Count sampling (exactly 20 rows)
length == 20
*/
from: experiment('singleton') summary | select: id | sample: 20 seed 6;

/*!result_meta -- Combined rate sampling (35% of 37 = ~13 rows)
length >= 11 and length <= 16
*/
from: experiment('singleton') summary | select: id | sample: 35% seed 7;

/*!result_meta -- Rate sampling with selection (45% of 37 = ~17 rows)
length >= 14 and length <= 20
*/
from: experiment('singleton') summary | select: id, scores.foo | sample: 45% seed 8;

/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)
length <= 20
*/
from: experiment('singleton') summary | filter: scores.foo > -0.5 | select: id | sample: 50% seed 9 | limit: 20;

/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)
length == 0
*/
from: experiment('singleton') spans | select: id | sample: 0%;

/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)
length == 0
*/
from: experiment('singleton') summary | select: id | sample: 0%;
