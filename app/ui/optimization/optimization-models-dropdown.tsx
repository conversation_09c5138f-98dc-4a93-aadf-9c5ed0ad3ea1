import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
  DropdownMenuSubContent,
} from "#/ui/dropdown-menu";
import { Button, buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { type ModelDetails } from "#/ui/prompts/models";
import { PRIORITIZED_DEFAULT_MODELS } from "#/ui/optimization/global-chat-provider";
import { ModelDropdown } from "#/app/app/[org]/prompt/[prompt]/ModelDropdown";
import { useOrg } from "#/utils/user";
import {
  getModelIcon,
  ModelOptionLabel,
} from "#/app/app/[org]/prompt/[prompt]/model-icon";
import { useMemo, useState } from "react";
import { BasicTooltip } from "#/ui/tooltip";
import { ModelOptionTooltip } from "#/app/app/[org]/prompt/[prompt]/model-option-tooltip";
import { AlertTriangle, Check, Plus } from "lucide-react";
import { useFeatureFlags } from "#/lib/feature-flags";
import { AIProviderLogoStack } from "#/ui/prompts/empty";
import Link from "next/link";
import {
  getProviderConfig,
  providerReadableName,
} from "#/app/app/[org]/settings/secrets/utils";
import { pluralize } from "#/utils/plurals";

// This is a hack to handle the fact that if we do includes on model name for openAI, it will pull in a bunch of mini and pro versions.
// we check for azure as well since zaure providers openai models.
// We want to do includes for claude because there are multiple providers that offer the model but have different names
const isModelMatch = (
  modelName: string,
  defaultModel: string,
  provider: string,
) => {
  return provider === "openai" || provider === "azure"
    ? modelName === defaultModel
    : modelName.includes(defaultModel);
};

const isCurrentInGroup = (models: ModelDetails[], currentModel: string) => {
  return models.some(
    (m) =>
      m.modelName === currentModel ||
      (m.children?.some((c) => c.modelName === currentModel) ?? false),
  );
};

export const OptimizationModelsDropdown = ({
  currentModel,
  setCurrentModel,
  configuredModelsByProvider,
  size,
}: {
  currentModel: string;
  setCurrentModel: (model: string) => void;
  configuredModelsByProvider: Record<string, ModelDetails[]>;
  size?: "widget" | "full";
}) => {
  const { name: orgName } = useOrg();
  const { flags } = useFeatureFlags();
  const [open, setOpen] = useState(false);

  const allModels = useMemo<(ModelDetails & { provider: string })[]>(() => {
    return Object.entries(configuredModelsByProvider).flatMap(
      ([provider, models]) => models.map((model) => ({ ...model, provider })),
    );
  }, [configuredModelsByProvider]);

  const currentModelDetails = useMemo<
    (ModelDetails & { provider: string }) | null
  >(() => {
    for (const model of allModels) {
      if (model.modelName === currentModel) return model;
      if (model.children) {
        const child = model.children.find((c) => c.modelName === currentModel);
        if (child) {
          return {
            ...child,
            displayName: child.displayName || model.displayName,
            provider: model.provider,
          };
        }
      }
    }
    return null;
  }, [allModels, currentModel]);

  //This thing groups the models by provider and then by model name in the order of the PRIORITIZED_DEFAULT_MODELS
  const defaultPrioritizedModelsList = useMemo(() => {
    return PRIORITIZED_DEFAULT_MODELS.map((defaultModel) => {
      const filteredModels = allModels.filter((configuredModel) =>
        isModelMatch(
          configuredModel.modelName,
          defaultModel,
          configuredModel.provider,
        ),
      );
      const filteredModelsbyProviderMap = new Map<string, ModelDetails[]>();
      filteredModels.forEach((model) => {
        filteredModelsbyProviderMap.set(model.provider, [
          ...(filteredModelsbyProviderMap.get(model.provider) || []),
          model,
        ]);
      });

      const fileredModelsbyProvider = Array.from(
        filteredModelsbyProviderMap.entries(),
      ).map(([provider, models]) => ({
        provider,
        models,
      }));
      return fileredModelsbyProvider;
    }).filter((group) => group.length > 0);
  }, [allModels]);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="xs"
          className={cn(
            "bg-transparent h-6 px-1 py-0 text-xs text-primary-500 font-normal",
            size === "full" && "h-7 px-1.5 text-[12.5px]",
          )}
          isDropdown
        >
          {currentModelDetails ? (
            <span className="max-w-60 truncate">
              {currentModelDetails.displayName || currentModel}
            </span>
          ) : (
            <span className="flex max-w-60 items-center truncate text-bad-600">
              <AlertTriangle className="mr-1 size-3" /> Select a model
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {defaultPrioritizedModelsList.length === 0 ? (
          <ModelsDropdownEmptyState orgName={orgName} />
        ) : (
          <>
            <DropdownMenuLabel>
              {pluralize(
                defaultPrioritizedModelsList.length,
                "Model",
                "Models",
              )}
            </DropdownMenuLabel>
            {defaultPrioritizedModelsList.map((modelsByProviderList) => {
              if (modelsByProviderList.length === 0) {
                return null;
              }
              const ModelIcon = getModelIcon(
                modelsByProviderList[0].models[0].modelName,
              );
              return (
                <DropdownMenuSub
                  key={
                    modelsByProviderList[0].models[0].modelName +
                    modelsByProviderList[0].provider
                  }
                >
                  <DropdownMenuSubTrigger
                    onClick={() => {
                      //This is hard-coded but its because we just want users to be able to click the first model in the list and it to just work out of the box.
                      setCurrentModel(
                        modelsByProviderList[0].models[0].modelName,
                      );
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn("size-3 opacity-0", {
                        "opacity-100": isCurrentInGroup(
                          modelsByProviderList[0].models,
                          currentModel,
                        ),
                      })}
                    />
                    <ModelIcon size={16} />
                    {modelsByProviderList[0].models[0].displayName ||
                      modelsByProviderList[0].models[0].modelName}
                  </DropdownMenuSubTrigger>
                  <DropdownMenuSubContent>
                    {modelsByProviderList.map((modelsByProvider) => {
                      const ProviderIcon = getProviderConfig(
                        modelsByProvider.provider,
                      ).Icon;

                      return (
                        <DropdownMenuSub
                          key={modelsByProvider.models[0].modelName}
                        >
                          <DropdownMenuLabel className="flex items-center gap-2">
                            <ProviderIcon size={16} />
                            {providerReadableName(modelsByProvider.provider)}
                          </DropdownMenuLabel>
                          {modelsByProvider.models.map((m) => {
                            if (m.children && m.children.length > 0) {
                              return (
                                <DropdownMenuSub key={m.modelName}>
                                  <DropdownMenuSubTrigger
                                    onClick={() => {
                                      setCurrentModel(m.modelName);
                                      setOpen(false);
                                    }}
                                  >
                                    <Check
                                      className={cn("size-3 opacity-0", {
                                        "opacity-100": isCurrentInGroup(
                                          [m, ...m.children],
                                          currentModel,
                                        ),
                                      })}
                                    />
                                    <ModelOptionLabel
                                      model={m.modelName}
                                      displayName={m.displayName}
                                      deprecated={m.deprecated}
                                      experimental={m.experimental}
                                    />
                                  </DropdownMenuSubTrigger>
                                  <DropdownMenuSubContent>
                                    <DropdownMenuLabel>
                                      Base model
                                    </DropdownMenuLabel>
                                    <BasicTooltip
                                      side="right"
                                      className="rounded-md"
                                      tooltipContent={
                                        <ModelOptionTooltip
                                          model={{
                                            ...m,
                                            provider: modelsByProvider.provider,
                                          }}
                                        />
                                      }
                                    >
                                      <DropdownMenuCheckboxItem
                                        checked={currentModel === m.modelName}
                                        onSelect={() => {
                                          setCurrentModel(m.modelName);
                                          setOpen(false);
                                        }}
                                        className="focus:bg-primary-200"
                                      >
                                        <ModelOptionLabel
                                          model={m.modelName}
                                          displayName={m.displayName}
                                          deprecated={m.deprecated}
                                          experimental={m.experimental}
                                        />
                                      </DropdownMenuCheckboxItem>
                                    </BasicTooltip>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuLabel>
                                      Variations
                                    </DropdownMenuLabel>
                                    {m.children.map((c) => (
                                      <BasicTooltip
                                        key={c.modelName}
                                        side="right"
                                        className="rounded-md"
                                        tooltipContent={
                                          <ModelOptionTooltip
                                            model={{
                                              ...c,
                                              provider:
                                                modelsByProvider.provider,
                                            }}
                                          />
                                        }
                                      >
                                        <DropdownMenuCheckboxItem
                                          checked={currentModel === c.modelName}
                                          onSelect={() => {
                                            setCurrentModel(c.modelName);
                                            setOpen(false);
                                          }}
                                          className="focus:bg-primary-200"
                                        >
                                          <ModelOptionLabel
                                            model={c.modelName}
                                            displayName={c.displayName}
                                            deprecated={c.deprecated}
                                            experimental={c.experimental}
                                          />
                                        </DropdownMenuCheckboxItem>
                                      </BasicTooltip>
                                    ))}
                                  </DropdownMenuSubContent>
                                </DropdownMenuSub>
                              );
                            }
                            return (
                              <BasicTooltip
                                key={m.modelName}
                                side="right"
                                className="rounded-md"
                                tooltipContent={
                                  <ModelOptionTooltip
                                    model={{
                                      ...m,
                                      provider: modelsByProvider.provider,
                                    }}
                                  />
                                }
                              >
                                <DropdownMenuCheckboxItem
                                  key={m.modelName}
                                  checked={currentModel === m.modelName}
                                  onSelect={() => {
                                    setCurrentModel(m.modelName);
                                    setOpen(false);
                                  }}
                                >
                                  <ModelOptionLabel
                                    model={m.modelName}
                                    displayName={m.displayName}
                                    deprecated={m.deprecated}
                                    experimental={m.experimental}
                                  />
                                </DropdownMenuCheckboxItem>
                              </BasicTooltip>
                            );
                          })}
                        </DropdownMenuSub>
                      );
                    })}
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              );
            })}
          </>
        )}
        {flags.loopTryOtherModels && (
          <>
            <DropdownMenuSeparator />
            <OtherModelsDropdown
              orgName={orgName}
              configuredModelsByProvider={configuredModelsByProvider}
              currentModel={currentModel}
              setCurrentModel={setCurrentModel}
              setOpen={setOpen}
            />
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const ModelsDropdownEmptyState = ({ orgName }: { orgName: string }) => {
  return (
    <div className="flex flex-col gap-3 p-3 text-xs text-primary-500">
      <div className="max-w-48">
        No supported models found. Add an AI provider that includes any of the
        following models to get started.
      </div>
      <ul className="flex list-inside list-disc flex-col gap-1">
        {PRIORITIZED_DEFAULT_MODELS.map((model) => (
          <li key={model} className="flex items-center gap-2">
            <ModelOptionLabel
              model={model}
              displayName={model}
              deprecated={false}
              experimental={false}
            />
          </li>
        ))}
      </ul>
      <Link
        href={`/app/${orgName}/settings/secrets`}
        className={buttonVariants({ variant: "border", size: "xs" })}
      >
        <Plus className="size-3" />
        <div className="flex flex-1 items-center justify-between">
          <span className="mr-2 text-xs">Add provider</span>
          <AIProviderLogoStack
            iconSize={14}
            iconClassName="size-5 -mr-1.5"
            providerCount={2}
          />
        </div>
      </Link>
    </div>
  );
};

//This is when you have the internal feature flag on and will let you select any model from the list of models.
const OtherModelsDropdown = ({
  orgName,
  configuredModelsByProvider,
  currentModel,
  setCurrentModel,
  setOpen,
}: {
  orgName: string;
  configuredModelsByProvider: Record<string, ModelDetails[]>;
  currentModel: string;
  setCurrentModel: (model: string) => void;
  setOpen: (open: boolean) => void;
}) => {
  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger>Other models</DropdownMenuSubTrigger>
      <ModelDropdown
        isInSubMenu
        orgName={orgName}
        modelOptionsByProvider={configuredModelsByProvider}
        currentModel={currentModel}
        onChange={(model) => {
          setCurrentModel(model);
          setOpen(false);
        }}
      />
    </DropdownMenuSub>
  );
};
