import { isObject } from "braintrust/util";
import {
  type SpanData,
  type PreviewSpan,
  type Span,
  type Trace,
  isRootSpan,
} from "./graph";
import { useMemo, useCallback, useState, useContext } from "react";
import React from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchBtqlPaginated, useFetchBtqlOptions } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { type Expr } from "@braintrust/btql/parser";
import { z } from "zod";
import { Skeleton } from "#/ui/skeleton";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  TraceThreadViewContents,
  type TraceThreadViewComponentProps,
} from "./trace-thread-view-contents";
import { processFullSpanData, useRealtimeSpans } from "./use-load-full-spans";
import { makeFullSpanQueryKey } from "./loading/query-utils";
import { type TracePaginationProps } from "./use-related-traces";

const getThreadSpans = (rootSpans: Span[]): Span[] => {
  const spanMetadata: Span[] = [];

  const scanSpan = (span: Span) => {
    const isRoot = isRootSpan(span);
    const isLLM = span.data.span_attributes?.type === "llm";
    const isScore = span.data.span_attributes?.type === "score";

    if (isLLM || isScore || isRoot) {
      spanMetadata.push(span);
    }

    if (!isScore) {
      span.children.forEach(scanSpan);
    }
  };

  rootSpans.forEach(scanSpan);
  return spanMetadata;
};

export const TraceThreadView = (
  props: Omit<TraceThreadViewComponentProps, "traces"> & { trace: Trace },
) => {
  const { trace } = props;
  const threadSpans = useMemo(() => {
    return getThreadSpans([trace.root]);
  }, [trace.root]);

  const traces = useMemo(() => {
    return [trace];
  }, [trace]);
  return (
    <TraceThreadViewContents
      {...props}
      threadSpans={threadSpans}
      traces={traces}
    />
  );
};

const threadSpanSchema = z.object({
  id: z.string(),
  span_id: z.string(),
  input: z.any(),
  output: z.any(),
  metadata: z.any(),
});

const PAGE_SIZE = 5;

type ThreadSpanId = {
  id: string;
  children: string[];
};

export const PaginatedTraceThreadView = ({
  traces,
  objectType,
  objectId,
  setSelectedSpan,
  setViewType,
  containerRef,
  allAvailableModelCosts,
  tracePaginationProps,
}: {
  traces: Trace[];
  objectType: DataObjectType;
  objectId: string;
  tracePaginationProps?: TracePaginationProps;
} & TraceThreadViewComponentProps) => {
  const btqlOptions = useFetchBtqlOptions();
  const builder = useBtqlQueryBuilder({});
  const threadSpanPages = useMemo(() => {
    const ids: ThreadSpanId[] = [];

    // in-order traversal
    function visit(span: Span | PreviewSpan) {
      const isRoot = isRootSpan(span);
      const isLLM = span.data.span_attributes?.type === "llm";
      const isScore = span.data.span_attributes?.type === "score";
      if (isLLM || isRoot) {
        ids.push({ id: span.id, children: [] });
      }
      const idsCount = ids.length;

      if (isScore) {
        ids.push({
          id: span.id,
          // process children for scorer spans directly
          children: span.children.flatMap((c) =>
            c.data.span_attributes?.type === "llm" &&
            "purpose" in c.data.span_attributes &&
            c.data.span_attributes.purpose === "scorer"
              ? [c.id]
              : [],
          ),
        });
      }

      // skip recursing for scorer spans since we process the children directly
      if (!isScore) {
        span.children.forEach(visit);
      }

      if (isRoot && !isLLM && ids.length === idsCount) {
        // don't add the root span if there's no children spans added
        ids.pop();
      }
    }

    traces.forEach((t) => visit(t.root));
    const pages: ThreadSpanId[][] = [];
    for (let i = 0; i < ids.length; i += PAGE_SIZE) {
      pages.push(ids.slice(i, i + PAGE_SIZE));
    }

    return pages;
  }, [traces]);

  const [pageIndex, setPageIndex] = useState(0);
  const hasNextSpanPage = threadSpanPages.length - 1 > pageIndex;
  const hasNextTracePage = !!tracePaginationProps?.hasNextPage;
  const hasNextPage = hasNextSpanPage || hasNextTracePage;
  const fetchNextTracePage = tracePaginationProps?.fetchNextPage;
  const fetchNextPage = useCallback(() => {
    if (hasNextSpanPage) {
      setPageIndex((prev) => prev + 1);
    } else if (hasNextTracePage) {
      fetchNextTracePage?.();
    }
  }, [hasNextSpanPage, hasNextTracePage, fetchNextTracePage]);

  const { projectId } = useContext(ProjectContext);
  const queryClient = useQueryClient();
  const {
    data: queriedData,
    isPending,
    isFetching,
    isPlaceholderData,
    error,
  } = useQuery({
    queryKey: [
      "threadSpans",
      objectType,
      objectId,
      projectId,
      pageIndex,
      threadSpanPages,
      btqlOptions,
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const { rowIds, primaryRowIds } = threadSpanPages
        .slice(0, pageIndex + 1)
        .reduce(
          (acc: { rowIds: string[]; primaryRowIds: string[] }, page) => {
            page.forEach(({ id, children }) => {
              acc.rowIds.push(...[id, ...children.slice(0, 1)]);
              acc.primaryRowIds.push(id);
            });
            return acc;
          },
          { rowIds: [], primaryRowIds: [] },
        );
      const { existing, idsToFetch } = rowIds.reduce(
        (acc: { existing: SpanData[]; idsToFetch: string[] }, id) => {
          const existing = queryClient.getQueryData(
            makeFullSpanQueryKey(objectType, objectId!, projectId, id),
          );
          if (existing && isObject(existing) && "data" in existing) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            acc.existing.push(existing.data as SpanData);
          } else {
            acc.idsToFetch.push(id);
          }
          return acc;
        },
        { existing: [], idsToFetch: [] },
      );
      const res =
        idsToFetch.length > 0
          ? await fetchBtqlPaginated(
              {
                args: {
                  query: {
                    from: builder.from(
                      objectType,
                      objectId ? [objectId] : [],
                      "spans",
                    ),
                    select: [{ op: "star" }],
                    filter: builder.or(
                      ...idsToFetch.map(
                        (id): Expr => ({
                          op: "eq",
                          left: builder.ident("id"),
                          right: {
                            op: "literal",
                            value: id,
                          },
                        }),
                      ),
                    ),
                  },
                  brainstoreRealtime: true,
                  useColumnstore: false,
                },
                ...btqlOptions,
                schema: threadSpanSchema,
                signal,
              },
              PAGE_SIZE,
            )
          : undefined;
      const threadSpanIndex = Object.fromEntries(
        primaryRowIds.map((id, index) => [id, index]),
      );
      const queriedData = idsToFetch.flatMap((id) => {
        const span = processFullSpanData(objectType, id, res?.data ?? []);
        if (!span) {
          return [];
        }
        return span;
      });
      const { data, childData } = [...queriedData, ...existing].reduce(
        (
          acc: { data: SpanData[]; childData: Record<string, SpanData> },
          span,
        ) => {
          if (threadSpanIndex[span.id] != null) {
            acc.data.push(span);
          } else {
            acc.childData[span.id] = span;
          }
          return acc;
        },
        { data: [], childData: {} },
      );
      return {
        data: data.toSorted((a, b) => {
          const aIndex = threadSpanIndex[a.id];
          const bIndex = threadSpanIndex[b.id];
          return aIndex - bIndex;
        }),
        childData,
        realtimeState: res?.realtime_state,
      };
    },
    enabled: !!objectId && !!projectId,
    staleTime: Infinity,
  });

  const realtimeIdsToWatch = useMemo(
    () =>
      threadSpanPages.flatMap((page) =>
        page.flatMap((p) => [p.id, ...p.children].map((id) => id)),
      ),
    [threadSpanPages],
  );
  const allQueriedData = useMemo(() => {
    return [
      ...(queriedData?.data ?? []),
      ...Object.values(queriedData?.childData ?? {}),
    ].map((d) => ({
      data: d,
      customColumnsData: null,
      realtimeState: queriedData?.realtimeState,
    }));
  }, [queriedData]);
  const cachedData = useRealtimeSpans({
    idsToWatch: realtimeIdsToWatch,
    objectType,
    objectId,
    queriedSpanData: allQueriedData,
  });

  const threadSpans: Span[] = useMemo(() => {
    if (!queriedData) {
      return [];
    }

    const cachedSpanData = Object.fromEntries(
      cachedData.flatMap((d) => (d.data?.id ? [[d.data.id, d.data]] : [])),
    );
    const childData = queriedData.childData;
    return queriedData.data.flatMap((d) => {
      const previewSpan = traces.find((t) => t.spans[d.span_id])?.spans[
        d.span_id
      ];
      if (
        // switching traces can cause this to not match loaded data
        !previewSpan
      ) {
        return [];
      }

      const spanData = cachedSpanData[previewSpan.id] ?? d;

      // make some kind of semi-loaded span shape
      // preserve relevant children spans for LLM scorers
      return [
        {
          ...previewSpan,
          data: {
            ...previewSpan.data,
            ...spanData,
          },
          children: previewSpan.children.map((c) => {
            const childSpanData = cachedSpanData[c.id] ?? childData?.[c.id];
            return childSpanData
              ? {
                  ...c,
                  data: {
                    ...c.data,
                    ...childData[c.id],
                  },
                }
              : c;
          }),
        },
      ];
    });
  }, [queriedData, cachedData, traces]);

  return (
    <>
      {isPending ? (
        <div className="flex flex-col gap-3 px-3">
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
        </div>
      ) : (
        <TraceThreadViewContents
          traces={traces}
          threadSpans={threadSpans}
          queryProps={{
            error,
            isFetching,
            isPlaceholderData,
            hasNextPage,
            fetchNextPage,
          }}
          setSelectedSpan={setSelectedSpan}
          setViewType={setViewType}
          containerRef={containerRef}
          allAvailableModelCosts={allAvailableModelCosts}
        />
      )}
    </>
  );
};
