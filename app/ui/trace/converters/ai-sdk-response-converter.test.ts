/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, expect, test } from "vitest";
import {
  aiSDKResponseNormalizer,
  convertAISDKMessagesToChatCompletions,
  isAISDKSpan,
} from "./ai-sdk-response-converter";
import { spanAttributesSchema } from "@braintrust/local";
import { parseLLMSpanPart } from "#/ui/LLMView";

describe("ai-sdk-response-converter", () => {
  test("isAISDKSpan detects span names with ai-sdk prefix", () => {
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "ai-sdk.generateText",
        }),
      ),
    ).toBe(true);
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "ai-sdk.streamText",
        }),
      ),
    ).toBe(true);
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "openai.generateText",
        }),
      ),
    ).toBe(false);
    expect(
      isAISDKSpan(
        undefined,
        undefined,
        undefined,
        spanAttributesSchema.parse({
          name: "foo-ai-sdk",
        }),
      ),
    ).toBe(false);
  });

  test("converts top-level {type:'text', text:'...'} to assistant chat message (via normalizer)", () => {
    const input = undefined;
    const output = [
      {
        type: "text",
        text: "UTC time is 2025-09-10T18:48:44.342Z.",
      },
    ];

    const result = aiSDKResponseNormalizer.normalize(input, output);

    const normalizedOutput = result.output;
    expect(normalizedOutput).toEqual([
      {
        role: "assistant",
        content: "UTC time is 2025-09-10T18:48:44.342Z.",
      },
    ]);
  });

  test("passes through non-top-level-text outputs unchanged", () => {
    const passthrough = [{ role: "assistant", content: "hello" }];
    const result = aiSDKResponseNormalizer.normalize(undefined, passthrough);
    expect(result.output).toEqual(passthrough);
  });

  test("detect identifies ai-sdk spans using spanAttributes name", () => {
    const output = { type: "text", text: "x" };
    expect(
      aiSDKResponseNormalizer.detect(undefined, output, undefined, {
        name: "ai-sdk.generateText",
      }),
    ).toBe(true);
    expect(
      aiSDKResponseNormalizer.detect(undefined, output, undefined, {
        name: "other.generateText",
      }),
    ).toBe(false);
  });

  test("conforms string input to user message (normalize input)", () => {
    const input = "Say hello";
    const out = aiSDKResponseNormalizer.normalize(input, undefined);
    expect(out.input).toEqual([{ role: "user", content: "Say hello" }]);
  });

  test("conforms AI SDK tool-call/tool-result input to Chat Completions messages", () => {
    const input = [
      {
        role: "system",
        content: [{ type: "text", text: "When helpful, call tools." }],
      },
      {
        role: "user",
        content: [{ type: "text", text: "What time is it in UTC?" }],
      },
      {
        role: "assistant",
        content: [
          {
            type: "tool-call",
            toolName: "get_time_utc",
            input: {},
            toolCallId: "call_1",
          },
        ],
      },
      {
        role: "tool",
        content: [
          {
            type: "tool-result",
            toolName: "get_time_utc",
            toolCallId: "call_1",
            output: {
              type: "json",
              value: { now: "2025-09-05T18:08:31.181Z" },
            },
          },
        ],
      },
      {
        role: "assistant",
        content: [
          {
            type: "tool-call",
            toolName: "format_time",
            input: { iso: "2025-09-05T18:08:31.181Z", style: "short" },
            toolCallId: "call_2",
          },
        ],
      },
      {
        role: "tool",
        content: [
          {
            type: "tool-result",
            toolName: "format_time",
            toolCallId: "call_2",
            output: {
              type: "json",
              value: { formatted: "UTC time is Fri, 05 Sep 2025 18:08:31 GMT" },
            },
          },
        ],
      },
      {
        role: "assistant",
        content: [
          {
            type: "text",
            text: "UTC time is Fri, 05 Sep 2025 18:08:31 GMT",
          },
        ],
      },
    ];

    const converted = convertAISDKMessagesToChatCompletions(input)!;
    expect(Array.isArray(converted)).toBe(true);
    expect(converted.map((m: any) => m.role)).toEqual([
      "system",
      "user",
      "assistant",
      "tool",
      "assistant",
      "tool",
      "assistant",
    ]);
    // Check first assistant tool_call
    const firstAssistant: any = converted[2];
    expect(firstAssistant.tool_calls?.[0]).toEqual(
      expect.objectContaining({
        id: "call_1",
        type: "function",
        function: { name: "get_time_utc", arguments: "{}" },
      }),
    );
    // Check first tool result
    const firstTool: any = converted[3];
    expect(firstTool).toEqual(
      expect.objectContaining({
        role: "tool",
        tool_call_id: "call_1",
      }),
    );
    expect(typeof firstTool.content).toBe("string");
    expect(firstTool.content).toContain("now");
    // Check final assistant text
    const lastAssistant: any = converted[6];
    expect(lastAssistant.content).toContain("UTC time is");

    // Ensure LLMView parser accepts the converted messages
    const parsed = parseLLMSpanPart(converted);
    expect(Array.isArray(parsed)).toBe(true);
  });

  test("unwraps wrapped assistant message with tool_calls (choices-style)", () => {
    const wrapped = [
      {
        finish_reason: "tool_calls",
        index: 0,
        logprobs: null,
        message: {
          role: "assistant",
          tool_calls: [
            {
              function: {
                name: "weatherTool",
                arguments: '{"location":"Paris"}',
              },
              id: "call_OABVNrWrPVGl0sqizTiBQB3s",
              index: 0,
              type: "function",
            },
            {
              function: {
                name: "calculatorTool",
                arguments: '{"operation":"add","a":15,"b":27}',
              },
              id: "call_oY3X5Audd8hG4SfGAFZPul80",
              index: 1,
              type: "function",
            },
          ],
        },
      },
    ];

    const result = aiSDKResponseNormalizer.normalize(undefined, wrapped);
    expect(result.output).toEqual([
      {
        role: "assistant",
        content: "",
        tool_calls: [
          {
            id: "call_OABVNrWrPVGl0sqizTiBQB3s",
            type: "function",
            function: {
              name: "weatherTool",
              arguments: '{"location":"Paris"}',
            },
          },
          {
            id: "call_oY3X5Audd8hG4SfGAFZPul80",
            type: "function",
            function: {
              name: "calculatorTool",
              arguments: '{"operation":"add","a":15,"b":27}',
            },
          },
        ],
      },
    ]);
  });

  test("omits tool_calls when toolCallId is missing in content parts", () => {
    const input = [
      {
        role: "assistant",
        content: [
          { type: "tool-call", toolName: "fnWithoutId", input: { a: 1 } },
        ],
      },
    ];

    const result = aiSDKResponseNormalizer.normalize(input, undefined);
    expect(result.input).toEqual([
      {
        role: "assistant",
        content: "",
        // no tool_calls emitted because toolCallId was missing
      },
    ]);
  });

  test("deduplicates assistant-embedded tool_result when tool role exists", () => {
    const input = [
      {
        role: "assistant",
        content: [
          {
            type: "tool-result",
            toolCallId: "call_1",
            output: { type: "json", value: { ok: true } },
          },
        ],
      },
      {
        role: "tool",
        content: [
          {
            type: "tool-result",
            toolCallId: "call_1",
            output: { type: "json", value: { ok: true } },
          },
        ],
      },
    ];

    const result = aiSDKResponseNormalizer.normalize(input, undefined);
    // Expect one assistant (no tool_calls) and one tool message (deduped)
    expect(Array.isArray(result.input)).toBe(true);
    const toArray = (v: unknown): unknown[] => (Array.isArray(v) ? v : []);
    const getRole = (v: unknown): string | undefined => {
      if (typeof v !== "object" || v === null) return undefined;
      const obj: object = v;
      const val = Reflect.get(obj, "role");
      return typeof val === "string" ? val : undefined;
    };
    const roles = toArray(result.input)
      .map((m) => getRole(m))
      .filter((r): r is string => typeof r === "string");
    expect(roles).toEqual(["assistant", "tool"]);
    // Ensure only a single tool message for call_1
    const toolMsgs = toArray(result.input).filter((m) => getRole(m) === "tool");
    expect(toolMsgs.length).toBe(1);
    let toolCallId0: unknown;
    if (toolMsgs[0] && typeof toolMsgs[0] === "object") {
      const obj: object = toolMsgs[0];
      toolCallId0 = Reflect.get(obj, "tool_call_id");
    }
    expect(toolCallId0).toBe("call_1");
  });
});
