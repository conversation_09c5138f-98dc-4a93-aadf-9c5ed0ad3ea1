import { type spanAttributesSchema } from "@braintrust/local";
import { type z } from "zod";
import type {
  FormatNormalizer,
  NormalizedData,
} from "#/ui/trace/format-normalizer";
import { type LLMMessageType } from "#/ui/LLMView";

export function isAISDKSpan(
  _input: unknown,
  _output: unknown,
  _metadata?: string,
  spanAttributes?: z.infer<typeof spanAttributesSchema>,
): boolean {
  return spanAttributes?.name?.startsWith("ai-sdk") || false;
}

/**
 * Convert AI SDK-style message arrays (including tool-call/tool-result blocks)
 * into OpenAI Chat Completions-compatible message arrays that LLMView can parse.
 *
 * Supported inputs:
 * - string → single user message
 * - array of message-like items with { role, content } where content can be
 *   - string
 *   - array of blocks including { type: "text" }, { type: "tool-call" }, { type: "tool-result" }
 *
 * Output conforms to chatCompletionMessageParamSchema expectations.
 */
export function convertAISDKMessagesToChatCompletions(
  value: unknown,
): LLMMessageType[] | null {
  // string → user message
  if (typeof value === "string") {
    const msg: LLMMessageType = { role: "user", content: value };
    return [msg];
  }

  if (!Array.isArray(value)) return null;

  const items: unknown[] = value;

  // Helpers and type guards
  type NormalizedToolCall = {
    id: string;
    type: "function";
    function: { name: string; arguments: string };
  };
  type NormalizedToolResult = { id: string; content: string };

  const isRecord = (v: unknown): v is Record<string, unknown> =>
    typeof v === "object" && v !== null;

  const isTextPart = (p: unknown): p is { type: "text"; text: string } =>
    isRecord(p) && p.type === "text" && typeof p.text === "string";
  const isToolCallPart = (
    p: unknown,
  ): p is {
    type: "tool-call";
    toolName: string;
    input?: unknown;
    toolCallId?: string;
  } => isRecord(p) && p.type === "tool-call" && typeof p.toolName === "string";
  const isToolResultPart = (
    p: unknown,
  ): p is { type: "tool-result"; toolCallId: string; output?: unknown } =>
    isRecord(p) && p.type === "tool-result" && typeof p.toolCallId === "string";

  const isMessageLike = (
    m: unknown,
  ): m is { role: string; content?: string | unknown[] } =>
    isRecord(m) && typeof m.role === "string";

  const jsonStringifySafe = (v: unknown): string => {
    try {
      return JSON.stringify(v);
    } catch {
      return String(v);
    }
  };

  const hasJsonValue = (o: unknown): o is { type: "json"; value: unknown } =>
    isRecord(o) && o.type === "json" && "value" in o;

  const isContentPartArray = (arr: unknown[]): boolean =>
    arr.every((v) => isRecord(v) && "type" in v && !("role" in v));

  // Top-level array of content parts (e.g., [{type:'text', text}...]) → assistant messages
  if (isContentPartArray(items)) {
    const msgs: LLMMessageType[] = [];
    for (const v of items) {
      if (isTextPart(v)) {
        msgs.push({ role: "assistant", content: v.text });
      }
    }
    return msgs.length > 0 ? msgs : null;
  }

  // Handle wrapper items shaped like Chat Completions choices: [{ message: { role, content?, tool_calls? } }]
  const isWrappedMessageItem = (
    v: unknown,
  ): v is {
    message: { role: string; content?: unknown; tool_calls?: unknown };
  } => isRecord(v) && isRecord(v.message) && typeof v.message.role === "string";

  const allWrapped = items.every(isWrappedMessageItem);
  if (allWrapped) {
    const msgs: LLMMessageType[] = [];
    for (const it of items) {
      if (!isWrappedMessageItem(it)) continue;
      const m: {
        role: string;
        content?: unknown;
        tool_calls?: unknown;
        tool_call_id?: unknown;
      } = it.message;
      const role = m.role;
      const mc = m.content;

      if (role === "system") {
        msgs.push({
          role: "system",
          content: typeof mc === "string" ? mc : "",
        });
        continue;
      }
      if (role === "user") {
        msgs.push({ role: "user", content: typeof mc === "string" ? mc : "" });
        continue;
      }
      if (role === "assistant") {
        let toolCalls: NormalizedToolCall[] | undefined;
        const maybeToolCalls = m.tool_calls;
        if (Array.isArray(maybeToolCalls)) {
          const raw: unknown[] = maybeToolCalls;
          const collected: NormalizedToolCall[] = [];
          for (const tc of raw) {
            if (
              isRecord(tc) &&
              typeof tc.id === "string" &&
              isRecord(tc.function) &&
              typeof tc.function.name === "string"
            ) {
              const fnObj: object = tc.function;
              const fnArgsRaw = Reflect.get(fnObj, "arguments");
              const argsStr =
                typeof fnArgsRaw === "string"
                  ? fnArgsRaw
                  : jsonStringifySafe(fnArgsRaw);
              collected.push({
                id: tc.id,
                type: "function",
                function: { name: tc.function.name, arguments: argsStr },
              });
            }
          }
          if (collected.length > 0) toolCalls = collected;
        }

        if (toolCalls) {
          msgs.push({
            role: "assistant",
            content: typeof mc === "string" ? mc : "",
            tool_calls: toolCalls,
          });
        } else {
          msgs.push({
            role: "assistant",
            content: typeof mc === "string" ? mc : "",
          });
        }
        continue;
      }
      if (role === "tool") {
        // If the wrapper contains a tool message, surface it directly when possible
        const toolCallIdVal = m.tool_call_id;
        if (typeof toolCallIdVal === "string") {
          msgs.push({
            role: "tool",
            tool_call_id: toolCallIdVal,
            content: typeof mc === "string" ? mc : jsonStringifySafe(mc),
          });
        }
      }
    }
    return msgs.length > 0 ? msgs : null;
  }

  // Helper to extract tool result ids from a parts array
  const extractToolResultIds = (parts: unknown[]): string[] => {
    const ids: string[] = [];
    for (const p of parts) {
      if (isToolResultPart(p)) ids.push(p.toolCallId);
    }
    return ids;
  };

  // Pre-scan for tool role messages to avoid duplicating tool results emitted from assistant
  const toolRoleIds = new Set<string>();
  for (const it of items) {
    if (isMessageLike(it) && it.role === "tool" && Array.isArray(it.content)) {
      for (const id of extractToolResultIds(it.content)) toolRoleIds.add(id);
    }
  }

  const extractFromContentArray = (parts: unknown[]) => {
    let text = "";
    const toolCalls: NormalizedToolCall[] = [];
    const toolResults: NormalizedToolResult[] = [];

    for (const part of parts) {
      if (isTextPart(part)) {
        text += part.text;
        continue;
      }
      if (isToolCallPart(part)) {
        const id =
          typeof part.toolCallId === "string" && part.toolCallId
            ? part.toolCallId
            : undefined;
        const args =
          typeof part.input === "string"
            ? part.input
            : jsonStringifySafe(part.input ?? {});
        if (id) {
          toolCalls.push({
            id,
            type: "function",
            function: { name: part.toolName, arguments: args },
          });
        }
        continue;
      }
      if (isToolResultPart(part)) {
        const output = part.output;
        const content =
          typeof output === "string"
            ? output
            : output && typeof output === "object"
              ? hasJsonValue(output)
                ? jsonStringifySafe(output.value)
                : jsonStringifySafe(output)
              : "";
        toolResults.push({ id: part.toolCallId, content });
      }
    }
    return { text, toolCalls, toolResults };
  };

  const out: LLMMessageType[] = [];
  const emitToolResults = (
    results: NormalizedToolResult[],
    shouldEmit: (id: string) => boolean = () => true,
  ) => {
    for (const tr of results) {
      if (shouldEmit(tr.id)) {
        out.push({ role: "tool", tool_call_id: tr.id, content: tr.content });
      }
    }
  };

  for (const item of items) {
    if (!isMessageLike(item)) continue;
    const role = item.role;
    const content: unknown = item.content;

    let textContent = "";
    let toolCalls: NormalizedToolCall[] = [];
    let toolResults: NormalizedToolResult[] = [];
    if (typeof content === "string") textContent = content;
    else if (Array.isArray(content)) {
      ({
        text: textContent,
        toolCalls,
        toolResults,
      } = extractFromContentArray(content));
    }

    if (role === "system") {
      out.push({ role: "system", content: textContent });
      continue;
    }
    if (role === "user") {
      out.push({ role: "user", content: textContent });
      continue;
    }

    if (role === "assistant") {
      out.push({
        role: "assistant",
        content: textContent,
        tool_calls: toolCalls.length > 0 ? toolCalls : undefined,
      });

      // Only emit assistant-embedded tool results if there isn't a separate tool message for the same id
      emitToolResults(toolResults, (id) => !toolRoleIds.has(id));
      continue;
    }

    if (role === "tool") {
      // When tool results are present, we prioritize them over any text content
      // to maintain proper tool call linkage. Text in tool messages is intentionally
      // skipped as it's typically redundant with the tool result data.
      if (toolResults.length > 0) emitToolResults(toolResults);
      continue;
    }

    // Fallback: ignore other roles
  }

  return out.length > 0 ? out : null;
}

export interface AISDKConvertedSpanData {
  messages: LLMMessageType[];
  inputMessages: LLMMessageType[];
  outputMessages: LLMMessageType[];
  toolDefinitions: Map<string, string>;
}

export function convertAISDKResponseToMessages(
  input: unknown,
  output: unknown,
): AISDKConvertedSpanData {
  const inputMessages = convertAISDKMessagesToChatCompletions(input) ?? [];
  const outputMessages = convertAISDKMessagesToChatCompletions(output) ?? [];

  return {
    messages: [...inputMessages, ...outputMessages],
    inputMessages,
    outputMessages,
    toolDefinitions: new Map<string, string>(),
  };
}

export const aiSDKResponseNormalizer: FormatNormalizer = {
  name: "AISDKResponseNormalizer",
  detect: isAISDKSpan,
  normalize(
    input: unknown,
    output: unknown,
    metadata?: string,
    spanAttributes?: z.infer<typeof spanAttributesSchema>,
  ): NormalizedData {
    const converted = convertAISDKResponseToMessages(input, output);
    return {
      input: converted.inputMessages,
      output: converted.outputMessages,
      toolDefinitions: converted.toolDefinitions,
      metadata,
    };
  },
};
