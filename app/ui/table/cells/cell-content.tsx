import {
  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
} from "#/ui/color";
import { useComparisonRowQuery } from "#/ui/table/cells/comparison-row-query";
import {
  type DiffObjectType,
  getDiffRight,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { type RowComparisonFormatterProps } from "#/ui/table/formatters/row-comparison-formatter";
import { type Cell, type ColumnMeta, flexRender } from "@tanstack/react-table";
import { type MergedValue } from "#/ui/field-to-column";
import { StructRow } from "apache-arrow";
import { cn } from "#/utils/classnames";
import { DataType } from "apache-arrow";
import { isNumericType } from "#/ui/trace/diff-score-object";
import { isEmpty } from "#/utils/object";
import type { CellTooltipContentType } from "./cell-tooltip-content";
import {
  Suspense,
  lazy,
  forwardRef,
  useCallback,
  useState,
  useMemo,
} from "react";
import { Tooltip, TooltipTrigger, TooltipPortal } from "#/ui/tooltip";
import { useComparisonRowData } from "#/ui/row-comparison/useComparisonRowData";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { type StreamingContentProps, useStreamingNode } from "./streaming";
import { formatFullDateTime } from "#/utils/date";
import { getObjValueByPath } from "braintrust/util";
import { stringify } from "#/utils/string";
import { z } from "zod";
import { useQueryClient } from "@tanstack/react-query";
import { useQuery } from "@tanstack/react-query";
import { urlMatcherRegexpStrict } from "#/utils/url";
import { TableCellErrorBoundary } from "./table-cell-error-boundary";

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
const CellTooltipContentLazy = lazy(
  () => import("./cell-tooltip-content"),
) as CellTooltipContentType;

const btInternalSchema = z.object({
  __bt_internal: z.object({
    _meta: z.object({
      diffModeEnabled: z.boolean(),
    }),
  }),
});

export const MultiCellContent = <TsData, TsValue>({
  cell,
  value,
  originalValue,
  mergedValues,
  index,
  isGroupRow,
  rowComparisonProps,
  cellContentProps,
}: {
  cell: Cell<TsData, TsValue>;
  value: unknown;
  originalValue: DiffObjectType<unknown> | undefined;
  mergedValues: unknown;
  index: number;
  isGroupRow: boolean;
  rowComparisonProps?: RowComparisonFormatterProps;
  cellContentProps: Omit<CellContentProps<TsData, TsValue>, "value">;
}) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const comparisonKey = `${getDiffRight((cell.row.original as any).comparison_key) ?? ""}`;
  const { comparisonExperiment, comparisonRowId, urlComparisonRowId } =
    useComparisonRowData({
      sourceData: isGroupRow
        ? undefined
        : {
            type: "rowOriginal",
            row: cell.row.original,
          },
      comparisonKey,
      comparisonExperimentData: rowComparisonProps?.comparisonExperimentData,
      experimentIndexOverride: index - 1,
    });

  const {
    data: comparisonData,
    isQueryEnabled,
    isPending,
  } = useComparisonRowQuery({
    rowId:
      rowComparisonProps && urlComparisonRowId
        ? (comparisonRowId ?? null)
        : null,
    experiment: comparisonExperiment,
  });

  const meta = cell.column.columnDef.meta;
  if (index > 0 && isGroupRow) {
    return null;
  }

  const v =
    index > 0 && isQueryEnabled
      ? comparisonData && meta && !isPending
        ? getObjValueByPath(comparisonData, meta.path)
        : null
      : value;

  const childMeta = metaForDiffObject(meta, index);
  const colorClassName = [
    EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
    ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
  ][index];

  // TODO: rework merged values into formatter-based rather than column based
  const filteredMergedValues = Object.fromEntries(
    Object.entries(mergedValues ?? {}).flatMap((e) => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      const mergedValue = e[1] as MergedValue<TsData, TsValue>;
      if (
        !isDiffObject(mergedValue.value) ||
        !(mergedValue.value instanceof StructRow)
      ) {
        return [];
      }
      const diffObjValue = mergedValue.value.toArray()[index];
      if (diffObjValue == null) {
        return [];
      }
      return [
        [
          e[0],
          {
            ...mergedValue,
            value: diffObjValue,
          },
        ],
      ];
    }),
  );

  const isMetric = meta?.path?.[0] === "metrics";
  const isScore = meta?.path?.[0] === "scores";
  const isColoredText = isMetric || isScore;
  const newMergedValues =
    Object.keys(filteredMergedValues).length > 0
      ? filteredMergedValues
      : undefined;

  const parsedBtInternal = btInternalSchema.safeParse(cell.row.original);
  return (
    <div
      className={cn("flex items-center gap-1.5 z-0", {
        [colorClassName]: isColoredText,
        "bg-transparent": isColoredText,
        "font-medium": isColoredText && index === 0,
      })}
      key={index}
    >
      {!childMeta?.isNumeric && !isColoredText && (
        <span
          className={cn("size-1.5 rounded-full flex-none z-10", colorClassName)}
        />
      )}
      <CellContent
        {...cellContentProps}
        meta={childMeta}
        containerComponent="span"
        value={v}
        // don't show diffs for the first item
        // also don't show diffs in playground if diff mode is off
        valueDiffObject={
          index > 0 &&
          (!parsedBtInternal.success ||
            parsedBtInternal.data.__bt_internal._meta.diffModeEnabled)
            ? originalValue
            : undefined
        }
        index={index}
        colorClassName={colorClassName}
        mergedValues={newMergedValues}
      />
      {meta?.path?.[0] === "scores" && typeof value === "number" && (
        <span
          className={cn(
            "absolute left-3 z-0 h-0.5 rounded-md opacity-50 bg-primary-300",
            colorClassName,
          )}
          style={{
            top: index === 0 ? 30 : index * 26 + 30,
            width: `calc(${value * 100}% - 22px)`,
          }}
        />
      )}
    </div>
  );
};

function metaForDiffObject<TsData, TsValue>(
  meta: ColumnMeta<TsData, TsValue> | undefined,
  index: number,
) {
  if (meta?.type?.children?.[index] == null) {
    return meta;
  }

  const arrowType = meta.type.children[index].type;
  return {
    ...meta,
    type: arrowType,
    isNumeric: isNumericType(arrowType),
    isTimestamp: DataType.isTimestamp(arrowType),
  };
}

export const CellContainer = forwardRef<
  HTMLDivElement,
  {
    className?: string;
    multilineRowCount?: number;
    isGroupRow?: boolean;
    showLeftBorder?: boolean;
    isGridLayout?: boolean;
    children: React.ReactNode;
    textToCopy?: string;
    getTextToCopy?: () => Promise<string>;
  }
>((props, ref) => {
  const {
    className,
    multilineRowCount,
    isGroupRow,
    showLeftBorder,
    isGridLayout,
    children,
    textToCopy,
    getTextToCopy,
    ...rest
  } = props;
  return (
    <div
      {...rest}
      ref={ref}
      className={cn(
        "min-h-full cursor-pointer overflow-hidden z-10 py-1.5 align-middle font-inter text-sm relative group/cell",
        {
          "py-3 w-full overflow-visible": isGroupRow,
        },
        className,
      )}
    >
      {!isEmpty(textToCopy) &&
        !isDiffObject(textToCopy) &&
        !isGroupRow &&
        !isGridLayout && (
          <CopyToClipboardButton
            size="xs"
            disableTooltip
            disabled={false}
            className="duration-50 absolute right-0 top-1 z-10 translate-x-full text-primary-400 opacity-0 shadow-sm transition-[transform,opacity] delay-0 hover:bg-primary-50 group-hover/cell:flex group-hover/cell:translate-x-0 group-hover/cell:bg-primary-100 group-hover/cell:opacity-100 group-hover/cell:delay-700"
            textToCopy={textToCopy}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            getTextToCopy={getTextToCopy}
          />
        )}
      <div
        className={cn("w-full pl-3", {
          truncate: !isGridLayout,
          "border-l pl-0": isGroupRow,
          "min-h-6": showLeftBorder,
          "h-full": isGridLayout,
        })}
      >
        <span
          className={cn("leading-5 pt-0.5", {
            "leading-6": !multilineRowCount && !isGridLayout,
            "break-words whitespace-normal text-wrap line-clamp-1":
              multilineRowCount,
            "h-full flex line-clamp-none overflow-hidden": isGridLayout,
          })}
          style={
            multilineRowCount && !isGridLayout
              ? {
                  WebkitLineClamp: multilineRowCount,
                }
              : undefined
          }
        >
          {children}
        </span>
      </div>
    </div>
  );
});

CellContainer.displayName = "CellContainer";

export type CellValueProps<TsData, TsValue> = {
  value: unknown;
  // in multi-experiment mode, this is the full diff object
  valueDiffObject?: DiffObjectType<unknown>;
  mergedValues?: Record<string, MergedValue<TsData, TsValue>>;
};

export type CellContentProps<TsData, TsValue> = CellValueProps<
  TsData,
  TsValue
> & {
  className?: string;
  colorClassName?: string;
  showLeftBorder?: boolean;
  size: number;
  cell: Cell<TsData, TsValue>;
  // optional meta override
  meta?: ColumnMeta<TsData, TsValue> | undefined;

  tableType: "list" | "detailed";
  multilineRowCount?: number;
  groupBy?: string;
  comparisonExperimentData?: { id: string }[];
  streamingContentProps?: StreamingContentProps;
  index?: number;
  isLoading?: boolean;
};

export function CellContent<TsData, TsValue>(
  props: CellContentProps<TsData, TsValue> & {
    containerComponent?:
      | React.ComponentType<{
          children: React.ReactNode;
          multilineRowCount?: number;
        }>
      | "span";
  },
) {
  const {
    cell,
    value,
    valueDiffObject,
    mergedValues,
    colorClassName,
    groupBy,
    meta: _meta,
    multilineRowCount,
    showLeftBorder,
    tableType,
    containerComponent: Container = CellContainer,
    index,
    streamingContentProps,
  } = props;

  const nodeWithStreamedContent = useStreamingNode({
    colName: cell.column.columnDef.id ?? "",
    rowOriginal: cell.row.original,
    index: index ?? 0,
    streamingContentProps,
    isGridLayout: false,
    className: "whitespace-nowrap truncate",
  });

  const [isOpen, setOpen] = useState(false);

  const meta = _meta ?? cell.column.columnDef.meta;
  const cellContext = cell.getContext();
  const isGroupRow = cell.row.getCanExpand();

  const queryClient = useQueryClient();

  const queryKey = useMemo(
    () => ["cell-tooltip-content", cell.id, index, value],
    [cell.id, index, value],
  );
  const queryFn = useCallback(async () => {
    if (meta?.fetchFullContent) {
      try {
        return await meta.fetchFullContent(cell.row.id, value);
      } catch {
        return value ?? "";
      }
    }
    return value;
  }, [meta, cell.row.id, value]);

  const getTextToCopy = useCallback(async () => {
    try {
      const fullContent = await queryClient.fetchQuery({
        queryKey: ["cell-copy-content", cell.id, index, value],
        queryFn: async () => {
          const loadedContent = await queryClient.fetchQuery({
            queryKey,
            queryFn,
            staleTime: 60000,
          });
          if (
            typeof loadedContent === "string" &&
            (loadedContent.endsWith("...") || loadedContent.endsWith('..."')) &&
            meta?.fetchFullContent
          ) {
            try {
              return await meta.fetchFullContent(cell.row.id, value, -1);
            } catch {}
          }
          return loadedContent;
        },
        staleTime: 60000,
      });
      return stringify(fullContent) ?? "";
    } catch {
      return "";
    }
  }, [
    queryKey,
    queryFn,
    queryClient,
    value,
    cell.id,
    cell.row.id,
    index,
    meta,
  ]);

  const containerProps =
    Container === CellContainer
      ? {
          multilineRowCount,
          isGroupRow,
          showLeftBorder,
          isGridLayout: meta?.isGridLayout,
          textToCopy: DISABLED_COPY_BUTTON_COLUMN_NAMES.includes(
            cell.column.columnDef.id ?? "",
          )
            ? undefined
            : typeof value === "number" && meta?.isTimestamp
              ? formatFullDateTime(new Date(value))
              : stringify(value),
          getTextToCopy:
            DISABLED_COPY_BUTTON_COLUMN_NAMES.includes(
              cell.column.columnDef.id ?? "",
            ) || !meta?.fetchFullContent
              ? undefined
              : getTextToCopy,
        }
      : {};

  const content = (
    // wrap the content in this container so that the tooltip area contains the entire cell
    <Container
      className={cn("z-10", {
        "truncate w-full": !meta?.isGridLayout,
        "h-full": meta?.isGridLayout,
      })}
      {...containerProps}
    >
      <TableCellErrorBoundary columnName={meta?.name} value={value}>
        {nodeWithStreamedContent ??
          flexRender(cell.column.columnDef.cell, {
            inTable: true,
            meta,
            value,
            valueDiffObject,
            diffIndex: index,
            mergedValues,
            colorClassName,
            groupBy,
            setValue: () => undefined, // do not allow edits in table cells
            updateRow: undefined, // Do not allow edits in the table cells
            hideNulls: isGroupRow,
            ...cellContext,
          })}
      </TableCellErrorBoundary>
    </Container>
  );

  const { data: fullContent, isLoading } = useQuery({
    queryKey,
    queryFn,
    enabled: isOpen && !!meta?.fetchFullContent,
    staleTime: 60000,
  });

  const columnName = meta?.name;
  const tooltipEnabled =
    (value != null ||
      (mergedValues != null && Object.keys(mergedValues).length > 0) ||
      nodeWithStreamedContent) &&
    (tableType === "detailed" ||
      meta?.isTimestamp ||
      columnName === "last_updated" ||
      columnName === "metadata") &&
    columnName !== "source" &&
    columnName !== "__row_comparison" &&
    !(
      isGroupRow &&
      (meta?.isNumeric ||
        // tooltip handled explicitly in GroupKeyFormatter so that summary grouping buttons don't conflict with the tooltip
        (meta?.groupColumnName && meta?.path[0] === meta.groupColumnName))
    ) &&
    !meta?.isGridLayout;

  const isUrl = useMemo(() => {
    const toTest = fullContent ?? value;
    if (typeof toTest !== "string") {
      return false;
    }
    return urlMatcherRegexpStrict.test(toTest);
  }, [value, fullContent]);

  if (!tooltipEnabled) {
    return content;
  }

  return (
    <Tooltip
      onOpenChange={setOpen}
      open={isOpen}
      disableHoverableContent={
        isLoading || isUrl ? false : !meta?.allowHoverableTooltip
      }
    >
      <TooltipTrigger asChild>{content}</TooltipTrigger>
      <TooltipPortal>
        <Suspense>
          {isOpen && (
            <TableCellErrorBoundary
              columnName={meta?.name}
              value={value}
              pageName="tooltip-error"
            >
              <CellTooltipContentLazy
                {...props}
                value={fullContent ?? value}
                isLoading={isLoading}
                meta={
                  meta
                    ? {
                        ...meta,
                        isTimestamp:
                          meta?.isTimestamp || columnName === "lastUpdated",
                      }
                    : undefined
                }
              />
            </TableCellErrorBoundary>
          )}
        </Suspense>
      </TooltipPortal>
    </Tooltip>
  );
}

const DISABLED_COPY_BUTTON_COLUMN_NAMES = [
  "span_type_info",
  "creator",
  "tags",
  "source",
];
