import { BT_GROUP_KEY } from "./queries";
import {
  scoreNameForSummary,
  SUMMARY_COLLATOR,
  type SummaryBreakdownData,
  getColumnType,
  summaryFieldsForAggregationType,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type Clause } from "#/utils/search/search";

export function sortGroupRowsWithSummary<
  TsData extends {
    [BT_GROUP_KEY]?: string;
  },
>({
  groupRows,
  searchSort,
  summaryBreakdownData,
}: {
  groupRows: TsData[];
  searchSort?: Clause<"sort">[];
  summaryBreakdownData?: SummaryBreakdownData;
}) {
  const sort = searchSort?.find(
    (s) => getColumnType(s.spec?.path?.path ?? []) !== "none",
  );
  if (!sort?.spec?.col.id || !summaryBreakdownData?.groupingValue) {
    return groupRows;
  }
  const isScore = sort.spec.col.id.startsWith("scores.");
  const [field, diffField, compareField] = summaryFieldsForAggregationType(
    summaryBreakdownData.aggregationTypes[isScore ? "scores" : "metrics"] ??
      "avg",
  );
  const scoreName = scoreNameForSummary(sort.spec.col.id);
  const sortComparisonType = sort.comparison?.type;
  const sortDesc = sort.spec.col.desc;

  if (sortComparisonType == null) {
    // base sort by summary data
    const summaryData = summaryBreakdownData?.summary.experiments.find(
      (s) => s.experiment.type === "base",
    );
    return groupRows.toSorted((a, b) => {
      const groupA = a[BT_GROUP_KEY] ?? "";
      const groupB = b[BT_GROUP_KEY] ?? "";
      const sortVal = tupleSort(
        [summaryData?.groupedSummary?.[groupA]?.[scoreName]?.[field]],
        [summaryData?.groupedSummary?.[groupB]?.[scoreName]?.[field]],
        [!!sortDesc],
        (_a, _b) => _a - _b,
      );
      return sortVal !== 0 ? sortVal : SUMMARY_COLLATOR.compare(groupA, groupB);
    });
  }

  const summaryData = summaryBreakdownData.summary.experiments.filter(
    (s) => s.experiment.type === "comparison",
  );
  const comparisonExperimentIndex =
    summaryBreakdownData?.summary?.experiments.find(
      (s) => s.experiment.id === sort?.comparison?.experimentId,
    )?.experiment.index;

  if (comparisonExperimentIndex == null) {
    return groupRows;
  }

  return groupRows.toSorted((a, b) => {
    const groupA = a[BT_GROUP_KEY] ?? "null";
    const groupB = b[BT_GROUP_KEY] ?? "null";
    const diffSort =
      sortComparisonType === "regression"
        ? tupleSort(
            [
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupA
              ]?.[scoreName]?.regressions?.length ?? 0,
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupA
              ]?.[scoreName]?.[diffField],
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupA
              ]?.[scoreName]?.improvements?.length ?? 0,
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupA
              ]?.[scoreName]?.[compareField],
            ],
            [
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupB
              ]?.[scoreName]?.regressions?.length ?? 0,
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupB
              ]?.[scoreName]?.[diffField],
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupB
              ]?.[scoreName]?.improvements?.length ?? 0,
              summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                groupB
              ]?.[scoreName]?.[compareField],
            ],
            [!!sortDesc, !sortDesc, !!sortDesc, !sortDesc],
            (_a, _b) => _a - _b,
          )
        : sortComparisonType === "score"
          ? tupleSort(
              [
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupA
                ]?.[scoreName]?.[diffField],
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupA
                ]?.[scoreName]?.[compareField],
              ],
              [
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupB
                ]?.[scoreName]?.[diffField],
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupB
                ]?.[scoreName]?.[compareField],
              ],
              [!!sortDesc, !!sortDesc],
              (_a, _b) => _a - _b,
            )
          : tupleSort(
              [
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupA
                ]?.[scoreName]?.[compareField],
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupA
                ]?.[scoreName]?.[diffField],
              ],
              [
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupB
                ]?.[scoreName]?.[compareField],
                summaryData[comparisonExperimentIndex]?.groupedSummary?.[
                  groupB
                ]?.[scoreName]?.[diffField],
              ],
              [!!sortDesc, !!sortDesc],
              (_a, _b) => _a - _b,
            );

    return diffSort !== 0 ? diffSort : SUMMARY_COLLATOR.compare(groupA, groupB);
  });
}

export function isSummaryColumn(colName: string | undefined) {
  return (
    colName && (colName.startsWith("scores.") || colName.startsWith("metrics."))
  );
}

export function tupleSort<T>(
  a: (T | null | undefined)[],
  b: (T | null | undefined)[],
  desc: boolean[],
  compare: (a: T, b: T) => number,
) {
  for (let i = 0; i < a.length; i++) {
    const aVal = a[i];
    const bVal = b[i];
    if (aVal == null && bVal == null) {
      continue;
    } else if (aVal == null) {
      return 1;
    } else if (bVal == null) {
      return -1;
    }
    const comparison = compare(aVal, bVal);
    if (comparison !== 0) {
      return comparison * (desc[i] ? -1 : 1);
    }
  }
  return 0;
}
