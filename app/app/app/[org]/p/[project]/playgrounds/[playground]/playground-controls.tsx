import { <PERSON><PERSON>, buttonVariants } from "#/ui/button";
import {
  ArrowUpRight,
  Database,
  PencilLine,
  Plus,
  Settings2,
  XIcon,
} from "lucide-react";
import { type SavedScorer } from "#/utils/scorers";
import { type TransactionId } from "braintrust/util";
import { type JSONStructure } from "#/ui/prompts/hooks";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { cn } from "#/utils/classnames";
import { type PlaygroundCopilotContext } from "#/ui/copilot/playground";
import { type PromptSessionState } from "#/ui/prompts/schema";
import { type ProjectContextDataset } from "../../project-actions";
import { ScorersDropdownWithCreateDialog } from "#/app/app/[org]/prompt/[prompt]/scorers/scorers-dropdown";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { UncontrolledNumericSlider } from "#/ui/numeric-slider";
import Link from "next/link";
import { getDatasetLink } from "../../datasets/[dataset]/getDatasetLink";
import { useOrg } from "#/utils/user";
import { Switch } from "#/ui/switch";
import { ExtraMessages } from "../../prompts/extra-messages";
import { useFeatureFlags } from "#/lib/feature-flags";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { PlaygroundAddTask } from "./add-task";
import { type UIFunction } from "#/ui/prompts/schema";
import { useContext, useCallback } from "react";
import { ProjectContext } from "../../projectContext";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";

const _sortDirections = ["ascending", "descending"] as const;
type SortDirection = (typeof _sortDirections)[number];

export type PlaygroundScoreSort = {
  outputName: string;
  scoreName: string;
  dir: SortDirection;
};

interface PlaygroundControlsProps {
  projectId: string;
  promptSessionId: string;
  isReadOnly?: boolean;
  projectName: string;
  updateDataset: (datasetId?: string) => void;
  setIsNewDatasetDialogOpen: (isOpen: boolean) => void;
  openUploadDatasetDialog: VoidFunction;
  datasetId?: string;
  datasets: ProjectContextDataset[];
  selectedDataset?: ProjectContextDataset;
  savedScorers: SavedScorer[];
  updateSavedScorers: (scorers: SavedScorer[]) => Promise<TransactionId | null>;
  jsonStructure?: JSONStructure;
  getFirstEvaluationRow?: () => Record<string, unknown>;
  copilotContext?: PlaygroundCopilotContext;
  numDatasetRecords: number | null;
  datasetCountLoading: boolean;
  maxConcurrency: number;
  setMaxConcurrency: (maxConcurrency: number) => void;
  strict: boolean | undefined;
  setStrict: (strict: boolean | undefined) => void;
  extraMessages: string | undefined;
  setExtraMessages: (extraMessages: string) => void;
  isCreatingScorer: boolean;
  setIsCreatingScorer: (isCreatingScorer: boolean) => void;
  selectedScorerId: string | null;
  setSelectedScorerId: (selectedScorerId: string | null) => void;
  numScorers: number;
  numTasks: number;
  selectedOriginIds?: string[];
  lintError?: string;
  showOpenDatasetOption?: boolean;
  scorerFunctionOverride?: Record<string, UIFunction>;
  createPromptOverride?: (prompt: UIFunction) => Promise<string | null>;
  isLoopEnabled?: boolean;
  isDatasetForbidden?: boolean;
  deletePrompt: (id: string) => Promise<TransactionId | null>;
}

export const PlaygroundControls = ({
  projectId,
  promptSessionId,
  isReadOnly,
  projectName,
  updateDataset,
  setIsNewDatasetDialogOpen,
  openUploadDatasetDialog,
  datasetId,
  datasets,
  selectedDataset,
  savedScorers,
  updateSavedScorers,
  jsonStructure,
  getFirstEvaluationRow,
  copilotContext,
  numDatasetRecords,
  datasetCountLoading,
  maxConcurrency,
  setMaxConcurrency,
  strict,
  setStrict,
  extraMessages,
  setExtraMessages,
  isCreatingScorer,
  setIsCreatingScorer,
  selectedScorerId,
  setSelectedScorerId,
  numScorers,
  numTasks,
  selectedOriginIds,
  lintError,
  showOpenDatasetOption = true,
  scorerFunctionOverride,
  createPromptOverride,
  isLoopEnabled = false,
  isDatasetForbidden = false,
  deletePrompt,
}: PlaygroundControlsProps) => {
  const appAnalytics = useAppAnalytics();
  const org = useOrg();
  const { isOrgDatasetsLoading } = useContext(ProjectContext);

  const {
    flags: { playxExtraMessages },
  } = useFeatureFlags();

  // Wrapper for setExtraMessages with analytics tracking
  const handleExtraMessagesChange = useCallback(
    (newExpression: string) => {
      const previousValue = extraMessages;

      if (appAnalytics && projectId && previousValue !== newExpression) {
        appAnalytics.track("playgroundEdit", {
          playgroundId: promptSessionId,
          projectId,
          triggerEntity: "human",
          entryPoint: "globalControls",
          editType: "configChanged",
          configType: "global",
          previousValue: previousValue || "",
          newValue: newExpression,
          details: {
            settingName: "extraMessages",
            previousExtraMessages: previousValue,
            newExtraMessages: newExpression,
          },
          source: "web",
        });
      }

      setExtraMessages(newExpression);
    },
    [appAnalytics, projectId, promptSessionId, extraMessages, setExtraMessages],
  );

  return (
    <div className="flex flex-1 justify-end gap-2">
      <PlaygroundAddTask
        isReadOnly={isReadOnly}
        numTasks={numTasks}
        projectName={projectName}
        disabledOriginIds={selectedOriginIds}
        deletePrompt={deletePrompt}
      />
      <ScorersDropdownWithCreateDialog
        projectId={projectId}
        projectName={projectName}
        savedScorers={savedScorers}
        updateScorers={updateSavedScorers}
        jsonStructure={jsonStructure}
        getFirstEvaluationRow={getFirstEvaluationRow}
        copilotContext={copilotContext}
        isCreatingScorer={isCreatingScorer}
        setIsCreatingScorer={setIsCreatingScorer}
        selectedScorerId={selectedScorerId}
        setSelectedScorerId={setSelectedScorerId}
        isLoopEnabled={isLoopEnabled}
        dropdownMenuContentProps={{
          align: "end",
        }}
        scorerFunctionOverride={scorerFunctionOverride}
        createPromptOverride={createPromptOverride}
      >
        <Button
          size="xs"
          className={cn("text-primary-600", {
            hidden: isReadOnly,
          })}
          Icon={Plus}
        >
          Scorer
          {numScorers > 0 && (
            <span className="text-[10px] font-normal text-primary-500">
              {numScorers}
            </span>
          )}
        </Button>
      </ScorersDropdownWithCreateDialog>

      {!isReadOnly && (
        <>
          {/* Tooltip primitives are used directly rather than using BasicTooltip
          because the nesting with DatasetDropdown (which also uses asChild on its trigger)
          causes weird interaction issues. */}
          <Tooltip>
            <DatasetDropdown
              datasets={datasets}
              selectedDatasetId={datasetId}
              onSelectDataset={(dataset) => updateDataset(dataset.id)}
              onCreateNewDataset={() => setIsNewDatasetDialogOpen(true)}
              onUploadDataset={openUploadDatasetDialog}
              align="end"
              onClear={() => updateDataset(undefined)}
              showOpenDatasetOption={showOpenDatasetOption}
              isLoopEnabled={isLoopEnabled}
            >
              <TooltipTrigger asChild>
                <Button
                  size="xs"
                  Icon={Database}
                  isDropdown
                  className={
                    !!lintError || isDatasetForbidden
                      ? "bg-bad-100 hover:bg-bad-200"
                      : ""
                  }
                  isLoading={isOrgDatasetsLoading}
                >
                  <span className="flex truncate">
                    <span
                      className={cn("block truncate max-w-40", {
                        "text-primary-500 font-normal": !selectedDataset,
                      })}
                    >
                      {selectedDataset?.name ?? "Select a dataset"}
                    </span>
                    {selectedDataset && numDatasetRecords !== null && (
                      <span className="pl-1 text-[10px] font-normal text-primary-500">
                        {datasetCountLoading ? null : numDatasetRecords}
                      </span>
                    )}
                  </span>
                </Button>
              </TooltipTrigger>
            </DatasetDropdown>
            <TooltipPortal>
              {lintError && (
                <TooltipContent className="max-w-sm text-xs">
                  {lintError}
                </TooltipContent>
              )}
            </TooltipPortal>
          </Tooltip>
          {selectedDataset && (
            <>
              <BasicTooltip
                tooltipContent={`Go to dataset${
                  selectedDataset.project_name === projectName
                    ? ""
                    : ` in ${selectedDataset.project_name}`
                }`}
              >
                <Link
                  href={getDatasetLink({
                    orgName: org.name,
                    projectName: selectedDataset.project_name,
                    datasetName: selectedDataset.name,
                  })}
                  className={cn(
                    buttonVariants({
                      size: "xs",
                      variant: "ghost",
                    }),
                    "hidden text-primary-400 group-hover:flex",
                  )}
                >
                  <ArrowUpRight className="size-3" />
                </Link>
              </BasicTooltip>
              <BasicTooltip tooltipContent="Clear dataset">
                <Button
                  size="xs"
                  variant="ghost"
                  className="hidden text-primary-400 group-hover:flex"
                  onClick={() => updateDataset(undefined)}
                  Icon={XIcon}
                />
              </BasicTooltip>
            </>
          )}
        </>
      )}

      <div className={cn({ hidden: isReadOnly })}>
        <Popover>
          <PopoverTrigger asChild>
            <Button size="xs" Icon={Settings2} />
          </PopoverTrigger>
          <PopoverContent align="end">
            {playxExtraMessages && (
              <div className="flex flex-col gap-0.5 text-xs">
                Appended dataset messages path
                <ExtraMessages
                  currentExpression={extraMessages}
                  setExtraMessages={handleExtraMessagesChange}
                  datasetId={datasetId}
                >
                  <Button size="xs" className="my-1">
                    <span
                      className={cn(
                        "flex-1 truncate text-left text-primary-600",
                        {
                          "font-mono text-accent-700": !!extraMessages,
                        },
                      )}
                    >
                      {extraMessages || "Disabled"}
                    </span>
                    <PencilLine className="size-3 flex-none text-primary-500 transition-opacity" />
                  </Button>
                </ExtraMessages>
                <p className="text-primary-500">
                  Path to messages from the dataset to append to prompts.
                </p>
              </div>
            )}

            <div className="flex flex-col gap-2 pt-8">
              <UncontrolledNumericSlider
                value={maxConcurrency}
                title="Max concurrency"
                min={1}
                max={100}
                step={1}
                setValue={async (v) => {
                  const newValue = Math.max(1, Math.min(100, v ?? 10));
                  const previousValue = maxConcurrency;

                  if (appAnalytics && projectId && previousValue !== newValue) {
                    appAnalytics.track("playgroundEdit", {
                      playgroundId: promptSessionId,
                      projectId,
                      triggerEntity: "human",
                      entryPoint: "globalControls",
                      editType: "configChanged",
                      configType: "global",
                      previousValue: previousValue.toString(),
                      newValue: newValue.toString(),
                      details: {
                        settingName: "maxConcurrency",
                        previousMaxConcurrency: previousValue,
                        newMaxConcurrency: newValue,
                      },
                      source: "web",
                    });
                  }

                  setMaxConcurrency(newValue);
                  return null;
                }}
                className="w-9"
                sliderClassName="!min-w-0"
              />
              <p className="pt-1 text-xs text-primary-500">
                The maximum number of tasks/scorers that will be run
                concurrently. This is useful for avoiding rate limits from AI
                providers.
              </p>
            </div>

            <div className="flex flex-col gap-2 pt-8">
              <div className="flex items-center gap-2 text-xs">
                <Switch
                  checked={strict}
                  onCheckedChange={(checked) => {
                    const newValue = checked ?? false;
                    const previousValue = strict;

                    if (
                      appAnalytics &&
                      projectId &&
                      previousValue !== newValue
                    ) {
                      appAnalytics.track("playgroundEdit", {
                        playgroundId: promptSessionId,
                        projectId,
                        triggerEntity: "human",
                        entryPoint: "globalControls",
                        editType: "configChanged",
                        configType: "global",
                        previousValue: previousValue?.toString() || "undefined",
                        newValue: newValue.toString(),
                        details: {
                          settingName: "strict",
                          previousStrict: previousValue,
                          newStrict: newValue,
                        },
                        source: "web",
                      });
                    }

                    setStrict(newValue);
                  }}
                />
                Strict variables
              </div>
              <p className="text-xs text-primary-500">
                Fail evaluation if the input does not include all referenced
                variables
              </p>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
PlaygroundControls.displayName = "PlaygroundControls";

export const sortPrompts = (
  promptSessionState: PromptSessionState | null | undefined,
) => {
  if (!promptSessionState || typeof promptSessionState !== "object") return [];

  try {
    return Object.entries(promptSessionState)
      .sort((a, b) =>
        (a[1]?.prompt_data?.options?.position ?? "").localeCompare(
          b[1]?.prompt_data?.options?.position ?? "",
        ),
      )
      .map(([id, prompt]) => ({ id, ...prompt }));
  } catch (e) {
    console.error("Error sorting prompts:", e);
    return [];
  }
};
