import { useQuery } from "@tanstack/react-query";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { type GetToken } from "@clerk/types";
import { useOrg } from "#/utils/user";
import { useMemo } from "react";
import {
  type getDataset,
  type getProjectContextDatasets,
} from "#/app/app/[org]/p/[project]/project-actions";
import { DEFAULT_PAGE_LIMIT } from "./pagination";

export function useDatasetApiQuery({
  datasetId,
}: {
  datasetId?: string | null;
}) {
  const { getToken } = useAuth();

  const result = useQuery({
    queryKey: ["getDataset", datasetId],
    queryFn: async () =>
      datasetId != null
        ? await invokeServerAction<typeof getDataset>({
            fName: "getDataset",
            args: { dataset_id: datasetId },
            getToken,
          })
        : null,
  });

  return result.data ?? undefined;
}

export function useOrgDatasetsApiQuery() {
  const { name: orgName } = useOrg();
  const { getToken } = useAuth();

  const { queryKey, queryFn } = useMemo(
    () =>
      orgDatasetsApiQuery({
        orgName,
        getToken,
      }),
    [orgName, getToken],
  );

  const result = useQuery({
    queryKey,
    queryFn,
  });

  return { result, queryKey };
}

const MAX_ITERATIONS = 10;
const PAGE_SIZE = 2000;

export function orgDatasetsApiQuery(params: {
  orgName: string;
  getToken: GetToken;
}) {
  const queryKey = ["getProjectContextDatasets", params.orgName];
  const queryFn = async ({ signal }: { signal: AbortSignal }) => {
    const results = [];

    let cursor: string | undefined;
    for (let i = 0; i < MAX_ITERATIONS; i++) {
      const queryResults = await invokeServerAction<
        typeof getProjectContextDatasets
      >({
        fName: "getProjectContextDatasets",
        args: {
          org_name: params.orgName,
          paginationParams: {
            starting_after: cursor,
            limit: PAGE_SIZE,
          },
        },
        getToken: params.getToken,
        signal,
      });

      results.push(...queryResults);
      const nextCursor = queryResults.at(-1)?.id;
      if (nextCursor === cursor) {
        break;
      }
      cursor = nextCursor;
      if (queryResults.length < DEFAULT_PAGE_LIMIT) {
        break;
      }
    }
    return results;
  };

  return { queryKey, queryFn };
}
