import argparse
import multiprocessing
import os
import re
import shutil
import subprocess
import sys
import tempfile
from concurrent.futures import Thr<PERSON>PoolExecutor
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional

from braintrust.framework import bcolors

OPERATIONS = ["format", "compile", "test"]
VERBOSE = False

SKIP_PATTERNS = [
    r"^\.\.\/",  # skip markdown files outside of CONTENT_DIR
    r"docs\/cookbook/.*",
    r"docs\/reference/api/.*",
    r"docs\/reference/api/.*",
    r"^careers.*",
    r"^legal.*",
]

SKIP_FILES = [
    "docs/reference/libs/python.md",
    "docs/reference/autoevals/python.mdx",
    "docs/reference/libs/nodejs/index.md",
]

REPO_ROOT = os.path.realpath(os.path.join(os.path.dirname(__file__), "../../../../"))
CONTENT_DIR = os.path.join(REPO_ROOT, "app/content")
TMP_DIR = os.path.join(REPO_ROOT, ".tmp/doc_snippets")

# We need two pools to prevent deadlocks
OUTER_POOL = None
INNER_POOL = None


class Language(Enum):
    PYTHON = "py"
    TYPESCRIPT = "ts"
    BTQL = "btql"


@dataclass
class CodeSnippet:
    lang_str: Optional[str]
    lang: Optional[Language]
    tags: List[str]
    code: str


@dataclass
class File:
    code_snippets: List[CodeSnippet]
    text_snippets: List[str]


def extract_code_snippets_with_language(content):
    # text_snippets[i] appears before code_snippets[i]
    code_snippets = []
    text_snippets = []
    last_end = 0

    for match in re.finditer(r"```(.*?)\n(.*?)```", content, re.DOTALL):
        first_line = match.group(1)
        code = match.group(2)

        lang_str = first_line.split()[0] if first_line else ""
        metadata = first_line[len(lang_str) :].strip() if first_line else ""

        tags = re.findall(r"#([\w-]+)", metadata)

        lang = None
        if lang_str in ["python", "py"]:
            lang = Language.PYTHON
        elif lang_str in ["typescript", "javascript", "js", "ts"]:
            lang = Language.TYPESCRIPT
        elif lang_str == "sql" and "btql" in tags:
            lang = Language.BTQL
        elif lang_str:
            assert lang_str in [
                "bash",
                "twig",
                "json",
                "yaml",
                "html",
                "sql",
                "go",
                "cpp",
                "ansi",
            ], f"Unknown language {lang_str}"

        code_snippets.append(
            CodeSnippet(
                lang=lang,
                lang_str=first_line,
                tags=tags,
                code=code.strip(),
            )
        )

        text_snippets.append(content[last_end : match.start()])
        last_end = match.end()

    text_snippets.append(content[last_end:])
    return File(text_snippets=text_snippets, code_snippets=code_snippets)


def should_skip(path):
    for pattern in SKIP_PATTERNS:
        if re.match(pattern, path):
            return True

    for skip_path in SKIP_FILES:
        if path == skip_path:
            return True
    return False


# Formats the file in-place, and returns True if any changes were made
def format_code_path(code_path: str, lang: Language):
    if lang == Language.PYTHON:
        fmt_result = subprocess.run(
            ["ruff", "format", code_path],
            capture_output=True,
            text=True,
        )
        check_result = subprocess.run(
            ["ruff", "check", "--fix", code_path],
            capture_output=True,
            text=True,
        )
        return ("unchanged" not in fmt_result.stdout) or ("Found" in check_result.stdout)
    elif lang == Language.TYPESCRIPT:
        result = subprocess.run(
            ["npx", "prettier", "--ignore-path", "/dev/null", "--end-of-line", "auto", "--write", code_path],
            capture_output=True,
            text=True,
        )
        return result.stdout.strip() != "" and "unchanged" not in result.stdout
    elif lang == Language.BTQL:
        return False
    else:
        raise ValueError(f"Unknown language {lang}")


def compile_code_path(code_path: str, lang: Language):
    if lang == Language.TYPESCRIPT:
        result = subprocess.run(
            [
                "npx",
                "tsup",
                code_path,
            ],
            capture_output=not VERBOSE,
            text=True,
        )

        errors = []
        success = result.returncode == 0
        if not success and not VERBOSE:
            for line in result.stderr.split("\n"):
                if os.path.basename(code_path) in line:
                    errors.append(line.strip())

        return success, errors
    elif lang == Language.PYTHON:
        result = subprocess.run(
            ["ruff", "check", code_path],
            capture_output=True,
            text=True,
        )
        errors = []
        success = result.returncode == 0
        if not success:
            errors = [result.stdout.strip()]
        return success, errors
    elif lang == Language.BTQL:
        code = open(code_path).read()
        result = subprocess.run(
            ["node", "./btql/cli/dist/index.js", "parse", code],
            capture_output=True,
            text=True,
        )
        success = result.returncode == 0
        if not success:
            print("FAILED BTQL", code, file=sys.stderr)

        errors = [result.stderr.strip()]
        return success, errors
    else:
        return True, []


@dataclass
class ProcessResult:
    unchanged: bool
    compiled: bool
    compile_errors: List[str]


def process_snippet(args):
    fpath, snippet, i, ops = args

    if snippet.lang is None:
        return ProcessResult(unchanged=True, compiled=True, compile_errors=[])

    rel_path = os.path.relpath(fpath, start=CONTENT_DIR)
    tmp_fname = rel_path.replace("/", "_") + f"_{i}.{snippet.lang.value}"

    tmp_subdir = tempfile.mkdtemp(dir=TMP_DIR)
    tmp_path = os.path.join(tmp_subdir, tmp_fname)

    try:
        with open(tmp_path, "w") as f:
            f.write(snippet.code)
            f.write("\n")  # Code formatters are obsessed with this

        changed = False
        if "format" in ops and "skip-format" not in snippet.tags:
            changed = format_code_path(tmp_path, snippet.lang)
            if changed:
                snippet.code = open(tmp_path).read().rstrip("\n")

        compiled = True
        compile_errors = []
        if "compile" in ops and "skip-compile" not in snippet.tags:
            compiled, compile_errors = compile_code_path(tmp_path, snippet.lang)

        return ProcessResult(unchanged=not changed, compiled=compiled, compile_errors=compile_errors)
    finally:
        shutil.rmtree(tmp_subdir)


def process_file(args):
    fpath, file, ops, write, fail_on_write = args
    print(f"Processing file {fpath}", file=sys.stderr)

    results = list(
        INNER_POOL.map(process_snippet, [(fpath, snippet, i, ops) for i, snippet in enumerate(file.code_snippets)])
    )

    if any([not r.unchanged for r in results]):
        if write:
            print(f"Writing formatted {fpath}", file=sys.stderr)
            file_pieces = []
            for i, snippet in enumerate(file.text_snippets):
                file_pieces.append(snippet)
                if i < len(file.code_snippets):
                    file_pieces.append(f"```{file.code_snippets[i].lang_str}\n")
                    file_pieces.append(file.code_snippets[i].code)
                    file_pieces.append("\n```")
        else:
            print(f"Error: would write formatted {fpath}", file=sys.stderr)
            return False

        new_file = "".join(file_pieces)
        with open(fpath, "w") as f:
            f.write(new_file)

        if fail_on_write:
            return False

    failed_to_compile = len([r for r in results if not r.compiled])
    if any([not r.compiled for r in results]):
        print(f"{failed_to_compile} snippet{'s' if failed_to_compile > 1 else ''} in {fpath} failed to compile")
        for i, (snippet, r) in enumerate(zip(file.code_snippets, results)):
            if not r.compiled:
                print(bcolors.WARNING + f"\n!!!! Snippet {i} failed to compile !!!!" + bcolors.ENDC)
                print("```")
                print(snippet.code)
                print("```")
                print(bcolors.FAIL + "\n".join(r.compile_errors) + bcolors.ENDC)
        return False

    return True


def setup(concurrency, verbose):
    global OUTER_POOL, INNER_POOL, VERBOSE
    if OUTER_POOL is None:
        OUTER_POOL = ThreadPoolExecutor(concurrency)

    if INNER_POOL is None:
        INNER_POOL = ThreadPoolExecutor(concurrency)

    VERBOSE = verbose
    os.makedirs(TMP_DIR, exist_ok=True)

    src_tsconfig = os.path.join(REPO_ROOT, "examples/tsconfig.json")
    dst_tsconfig = os.path.join(TMP_DIR, "tsconfig.json")
    if not os.path.exists(dst_tsconfig) or open(src_tsconfig).read() != open(dst_tsconfig).read():
        shutil.copyfile(src_tsconfig, os.path.join(TMP_DIR, "tsconfig.json"))


def load_docs(file_or_dirs=None):
    if not file_or_dirs:
        file_or_dirs = [os.path.join(CONTENT_DIR)]

    candidate_files = set()
    for file_or_dir in file_or_dirs:
        if os.path.isdir(file_or_dir):
            for root, dirs, files in os.walk(file_or_dir):
                for file in files:
                    candidate_files.add(os.path.join(root, file))
        elif os.path.exists(file_or_dir):
            candidate_files.add(file_or_dir)
        else:
            print(f"File {file_or_dir} does not exist. Skipping...", file=sys.stderr)

    file_info = {}
    for fpath in candidate_files:
        if not (fpath.endswith(".md") or fpath.endswith(".mdx")):
            continue

        rel_path = os.path.relpath(fpath, start=CONTENT_DIR)
        if should_skip(rel_path):
            continue

        with open(fpath, "r") as f:
            content = f.read()

        file_info[fpath] = extract_code_snippets_with_language(content)

    return file_info


def main(args=None):
    if args is None:
        args = sys.argv[1:]

    parser = argparse.ArgumentParser()
    parser.add_argument("--ops", default="format,compile", type=str)
    parser.add_argument("--write", action="store_true")
    parser.add_argument(
        "--fail-on-write", action="store_true", help="If provided, fail if any file would be changed by --write"
    )
    parser.add_argument("--concurrency", default=multiprocessing.cpu_count(), type=int)
    parser.add_argument("--verbose", action="store_true")
    parser.add_argument("files", nargs="*")
    args = parser.parse_args(args=args)

    ops = set([x.strip() for x in args.ops.split(",") if x.strip()])
    for op in ops:
        if op not in OPERATIONS:
            raise ValueError(f"Invalid operation: {op}. Must be one of {OPERATIONS}")

    setup(args.concurrency, args.verbose)
    file_info = load_docs(args.files)

    tasks = []
    for fpath, file in file_info.items():
        tasks.append(OUTER_POOL.submit(process_file, (fpath, file, ops, args.write, args.fail_on_write)))

    any_failures = False
    for (fpath, file), task in zip(file_info.items(), tasks):
        success = task.result()
        if not success:
            any_failures = True

    OUTER_POOL.shutdown()

    if any_failures:
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
