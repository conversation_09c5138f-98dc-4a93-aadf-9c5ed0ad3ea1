[{"context": {}, "expected": null, "id": "279089ebe631674e301cf4c04806d006995a5188b2b019a58c5324d39a7a649b", "input": [{"content": "say 10", "role": "system"}], "is_root": true, "metadata": {"model": "gpt-4o", "provider": "openai", "response_format": {"json_schema": {"name": "CalendarEvent", "schema": {"properties": {"date": {"title": "Date", "type": "string"}, "name": {"title": "Name", "type": "string"}, "participants": {"items": {"type": "string"}, "title": "Participants", "type": "array"}}, "required": ["name", "date", "participants"], "title": "CalendarEvent", "type": "object"}}, "type": "json_schema"}, "temperature": 0, "tools": [{"function": {"name": "QueryCalendarEvent", "parameters": {"additionalProperties": false, "properties": {"date": {"title": "Date", "type": "string"}, "name": {"title": "Name", "type": "string"}}, "required": ["name", "date"], "title": "QueryCalendarEvent", "type": "object"}, "strict": true}, "type": "function"}]}, "metrics": {"completion_accepted_prediction_tokens": 0, "completion_audio_tokens": 0, "completion_reasoning_tokens": 0, "completion_rejected_prediction_tokens": 0, "completion_tokens": 30, "prompt_audio_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 113, "tokens": 143}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": [], "audio": null, "content": "{\"name\":\"Team Meeting\",\"date\":\"2023-11-10\",\"participants\":[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]}", "function_call": null, "parsed": {"date": "2023-11-10", "name": "Team Meeting", "participants": ["<PERSON>", "<PERSON>", "<PERSON>"]}, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "279089ebe631674e301cf4c04806d006995a5188b2b019a58c5324d39a7a649b", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "279089ebe631674e301cf4c04806d006995a5188b2b019a58c5324d39a7a649b", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "67529ffdefc73cc055d3a20ab0d1dc90e9404c0cf3ced30d825d87086db2cf43", "input": [{"content": "say 10", "role": "system"}], "is_root": true, "metadata": {"background": null, "created_at": **********, "error": null, "id": "resp_680ac0bf57a88192ae5b15e1d7433bd90e9f80e36095dcc8", "incomplete_details": null, "instructions": null, "max_output_tokens": null, "max_tool_calls": null, "metadata": {}, "model": "gpt-4o-2024-08-06", "object": "response", "parallel_tool_calls": true, "previous_response_id": null, "prompt": null, "provider": "openai", "reasoning": {"effort": null, "generate_summary": null, "summary": null}, "service_tier": "default", "status": "completed", "store": true, "temperature": 1, "text": {"format": {"description": null, "name": "calendar_event", "schema": {"additionalProperties": false, "properties": {"date": {"type": "string"}, "name": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}}, "required": ["name", "date", "participants"], "type": "object"}, "schema_": {"additionalProperties": false, "properties": {"date": {"type": "string"}, "name": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}}, "required": ["name", "date", "participants"], "type": "object"}, "strict": true, "type": "json_schema"}}, "tool_choice": "auto", "tools": [], "top_logprobs": null, "top_p": 1, "truncation": "disabled", "user": null}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 35, "prompt_cached_tokens": 0, "prompt_tokens": 53, "tokens": 88}, "origin": null, "output": [{"content": [{"annotations": [], "logprobs": null, "text": "{\"name\":\"Annual Technology Conference\",\"date\":\"2023-12-05\",\"participants\":[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]}", "type": "output_text"}], "id": "msg_680ac0c125f88192b2fc63fa0a9259d60e9f80e36095dcc8", "role": "assistant", "status": "completed", "type": "message"}], "root_span_id": "67529ffdefc73cc055d3a20ab0d1dc90e9404c0cf3ced30d825d87086db2cf43", "scores": null, "span_attributes": {"name": "openai.responses.create", "type": "llm"}, "span_id": "67529ffdefc73cc055d3a20ab0d1dc90e9404c0cf3ced30d825d87086db2cf43", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "67529ffdefc73cc055d3a20ab0d1dc90e9404c0cf3ced30d825d87086db2cf43", "input": [{"content": "say 10", "role": "system"}], "is_root": true, "metadata": {"background": null, "created_at": **********, "error": null, "id": "resp_680ac0bf57a88192ae5b15e1d7433bd90e9f80e36095dcc8", "incomplete_details": null, "instructions": null, "max_output_tokens": null, "max_tool_calls": null, "metadata": {}, "model": "gpt-4o-2024-08-06", "object": "response", "parallel_tool_calls": true, "previous_response_id": null, "prompt": null, "provider": "openai", "reasoning": {"effort": null, "generate_summary": null, "summary": null}, "service_tier": "default", "status": "completed", "store": true, "temperature": 1, "text": {"format": {"description": null, "name": "calendar_event", "schema": {"additionalProperties": false, "properties": {"date": {"type": "string"}, "name": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}}, "required": ["name", "date", "participants"], "type": "object"}, "schema_": {"additionalProperties": false, "properties": {"date": {"type": "string"}, "name": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}}, "required": ["name", "date", "participants"], "type": "object"}, "strict": true, "type": "json_schema"}}, "tool_choice": "auto", "tools": [], "top_logprobs": null, "top_p": 1, "truncation": "disabled", "user": null}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 35, "prompt_cached_tokens": 0, "prompt_tokens": 53, "tokens": 88}, "origin": null, "output": [{"content": [{"annotations": [], "logprobs": null, "text": "{\"name\":\"Annual Technology Conference\",\"date\":\"2023-12-05\",\"participants\":[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]}", "type": "output_text"}], "id": "msg_680ac0c125f88192b2fc63fa0a9259d60e9f80e36095dcc8", "role": "assistant", "status": "completed", "type": "message"}], "root_span_id": "67529ffdefc73cc055d3a20ab0d1dc90e9404c0cf3ced30d825d87086db2cf43", "scores": null, "span_attributes": {"name": "openai.responses.create", "type": "llm"}, "span_id": "67529ffdefc73cc055d3a20ab0d1dc90e9404c0cf3ced30d825d87086db2cf43", "span_parents": null, "tags": null}]