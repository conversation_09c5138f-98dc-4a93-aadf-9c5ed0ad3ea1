import { BraintrustCallbackHandler } from "@braintrust/langchain-js";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";
import { AsyncLocalStorage } from "async_hooks";
import { ExactMatch } from "autoevals";
import { _internalGetGlobalState, currentSpan, Eval } from "braintrust";

async function main() {
  const projectName = "Bob Langchain Eval";
  const expName = "braintrust-numeric-metrics";
  const prompt = ChatPromptTemplate.fromTemplate("Q: {input}\nA:");

  console.log("OPENAI_BASE_URL", process.env.OPENAI_BASE_URL);

  const llm = new ChatOpenAI(
    {
      model: "gpt-4o-mini",
      temperature: 0.2,
    },
    {
      apiKey: process.env.BRAINTRUST_API_KEY,
      baseURL: `${process.env.OPENAI_BASE_URL}`,
    },
  );

  await <PERSON><PERSON>(projectName, {
    experimentName: expName,
    data: () => [
      { input: "Capital of France?", expected: "Paris" },
      { input: "Two plus two?", expected: "four" },
      { input: "Opposite of hot?", expected: "cold" },
    ],

    task: async (input: string) => {
      const chain = prompt.pipe(llm);

      const handler = new BraintrustCallbackHandler({
        // XXX: in my testing the `currentSpan` in BraintrustCallbackHandler.startSpan losses async context after the first call.
        // this new `parent` is necessary to ensure the default parent span is getting the right tracing context.
        parent: AsyncLocalStorage.bind(() => currentSpan()),
      });

      const response = await chain.invoke({ input }, { callbacks: [handler] });
      return response.content;
    },
    scores: [ExactMatch],
  });

  console.log("Eval finished.");
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
