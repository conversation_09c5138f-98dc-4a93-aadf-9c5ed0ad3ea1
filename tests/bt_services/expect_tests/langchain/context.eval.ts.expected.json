[{"context": {}, "expected": "Paris", "id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "input": "Capital of France?", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "The capital of France is Paris.", "root_span_id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "e3346dac7ccd1ea4da2fe0dc44751b02a34b7ebff63b63505f5d4355a43998e5", "input": "Capital of France?", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "The capital of France is Paris.", "root_span_id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "e3346dac7ccd1ea4da2fe0dc44751b02a34b7ebff63b63505f5d4355a43998e5", "span_parents": ["ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a"], "tags": null}, {"context": {}, "expected": "cold", "id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "input": "Opposite of hot?", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "The opposite of hot is cold.", "root_span_id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b0e8a99f067b30148ec567ea003c593be868e12201b4f89a96f7a179d63005bb", "input": "Opposite of hot?", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "The opposite of hot is cold.", "root_span_id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b0e8a99f067b30148ec567ea003c593be868e12201b4f89a96f7a179d63005bb", "span_parents": ["8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82"], "tags": null}, {"context": {}, "expected": "four", "id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "input": "Two plus two?", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "Two plus two equals four.", "root_span_id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "c62b0d60c1e2647466e439cab0a6d96c970f3b22bcf895f6f0d50e3cad9d7c45", "input": "Two plus two?", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "Two plus two equals four.", "root_span_id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c62b0d60c1e2647466e439cab0a6d96c970f3b22bcf895f6f0d50e3cad9d7c45", "span_parents": ["8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79"], "tags": null}, {"context": {}, "expected": null, "id": "31e5375f7af2325601a886df490cd0fc2928d51a097f5a88d89d822fe31e982e", "input": [{"content": "Q: Capital of France?\nA:", "role": "user"}], "is_root": false, "metadata": {"frequency_penalty": 0, "model": "gpt-4o-mini", "n": 1, "presence_penalty": 0, "tags": ["seq:step:2"], "temperature": 0.2, "top_p": 1}, "metrics": {"completion_tokens": 7, "prompt_tokens": 15, "tokens": 22}, "origin": null, "output": [{"content": "The capital of France is Paris.", "role": "assistant"}], "root_span_id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "scores": null, "span_attributes": {"name": "ChatOpenAI", "type": "llm"}, "span_id": "31e5375f7af2325601a886df490cd0fc2928d51a097f5a88d89d822fe31e982e", "span_parents": ["67ee18ae6746e2476f180f0ff3f326aa154f4d5dd1e097966a552495e356dc69"], "tags": null}, {"context": {}, "expected": null, "id": "cb518f2a786581e9c8ce0d07c2a74af7fada3e0d30d2339aab3e7032a4cd944c", "input": [{"content": "Q: Opposite of hot?\nA:", "role": "user"}], "is_root": false, "metadata": {"frequency_penalty": 0, "model": "gpt-4o-mini", "n": 1, "presence_penalty": 0, "tags": ["seq:step:2"], "temperature": 0.2, "top_p": 1}, "metrics": {"completion_tokens": 7, "prompt_tokens": 16, "tokens": 23}, "origin": null, "output": [{"content": "The opposite of hot is cold.", "role": "assistant"}], "root_span_id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "scores": null, "span_attributes": {"name": "ChatOpenAI", "type": "llm"}, "span_id": "cb518f2a786581e9c8ce0d07c2a74af7fada3e0d30d2339aab3e7032a4cd944c", "span_parents": ["957ec1d394478b7e2e3e6a81f67aef45a163931beffae2ffd0250740704270d2"], "tags": null}, {"context": {}, "expected": null, "id": "7b020e147081fbb935b5f30e8c8968ff9597c608c2d5a7f8dd61dd5b7351fbff", "input": [{"content": "Q: Two plus two?\nA:", "role": "user"}], "is_root": false, "metadata": {"frequency_penalty": 0, "model": "gpt-4o-mini", "n": 1, "presence_penalty": 0, "tags": ["seq:step:2"], "temperature": 0.2, "top_p": 1}, "metrics": {"completion_tokens": 6, "prompt_tokens": 15, "tokens": 21}, "origin": null, "output": [{"content": "Two plus two equals four.", "role": "assistant"}], "root_span_id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "scores": null, "span_attributes": {"name": "ChatOpenAI", "type": "llm"}, "span_id": "7b020e147081fbb935b5f30e8c8968ff9597c608c2d5a7f8dd61dd5b7351fbff", "span_parents": ["3deab2a9c3142ca9ebc60aba403407849eb228a2e762b0a7d7849630cd55a1e5"], "tags": null}, {"context": {}, "expected": null, "id": "dbadf484d887a74207c4412bb3daa81b3d198aaea7e12300546a31a51af5f04a", "input": {"expected": "Paris", "input": "Capital of France?", "metadata": {}, "output": "The capital of France is Paris."}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "scores": {"ExactMatch": 0}, "span_attributes": {"name": "ExactMatch", "type": "score"}, "span_id": "dbadf484d887a74207c4412bb3daa81b3d198aaea7e12300546a31a51af5f04a", "span_parents": ["ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a"], "tags": null}, {"context": {}, "expected": null, "id": "5cbc4a4a4e7830c65209fe2e19319214b0aa63566ff755fc423a64ae051b9b47", "input": {"expected": "cold", "input": "Opposite of hot?", "metadata": {}, "output": "The opposite of hot is cold."}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "scores": {"ExactMatch": 0}, "span_attributes": {"name": "ExactMatch", "type": "score"}, "span_id": "5cbc4a4a4e7830c65209fe2e19319214b0aa63566ff755fc423a64ae051b9b47", "span_parents": ["8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82"], "tags": null}, {"context": {}, "expected": null, "id": "c48946db98ea9a734b8cd1d5bff15705f9a0958d78f99dfecb6e27de634a147f", "input": {"expected": "four", "input": "Two plus two?", "metadata": {}, "output": "Two plus two equals four."}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "scores": {"ExactMatch": 0}, "span_attributes": {"name": "ExactMatch", "type": "score"}, "span_id": "c48946db98ea9a734b8cd1d5bff15705f9a0958d78f99dfecb6e27de634a147f", "span_parents": ["8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79"], "tags": null}, {"context": {}, "expected": null, "id": "67ee18ae6746e2476f180f0ff3f326aa154f4d5dd1e097966a552495e356dc69", "input": {"input": "Capital of France?"}, "is_root": false, "metadata": {"tags": []}, "metrics": {}, "origin": null, "output": "The capital of France is Paris.", "root_span_id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "scores": null, "span_attributes": {"name": "RunnableSequence"}, "span_id": "67ee18ae6746e2476f180f0ff3f326aa154f4d5dd1e097966a552495e356dc69", "span_parents": ["e3346dac7ccd1ea4da2fe0dc44751b02a34b7ebff63b63505f5d4355a43998e5"], "tags": null}, {"context": {}, "expected": null, "id": "b73833b26eae255449e0a3bdcd84dfc58adb1939626e72617ef8a3c5439d707a", "input": {"input": "Capital of France?"}, "is_root": false, "metadata": {"tags": ["seq:step:1"]}, "metrics": {}, "origin": null, "output": "Q: Capital of France?\nA:", "root_span_id": "ff9392758b61f41817d619556704db33a61c0ac710b8451439819ecf53defc7a", "scores": null, "span_attributes": {"name": "ChatPromptTemplate"}, "span_id": "b73833b26eae255449e0a3bdcd84dfc58adb1939626e72617ef8a3c5439d707a", "span_parents": ["67ee18ae6746e2476f180f0ff3f326aa154f4d5dd1e097966a552495e356dc69"], "tags": null}, {"context": {}, "expected": null, "id": "957ec1d394478b7e2e3e6a81f67aef45a163931beffae2ffd0250740704270d2", "input": {"input": "Opposite of hot?"}, "is_root": false, "metadata": {"tags": []}, "metrics": {}, "origin": null, "output": "The opposite of hot is cold.", "root_span_id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "scores": null, "span_attributes": {"name": "RunnableSequence"}, "span_id": "957ec1d394478b7e2e3e6a81f67aef45a163931beffae2ffd0250740704270d2", "span_parents": ["b0e8a99f067b30148ec567ea003c593be868e12201b4f89a96f7a179d63005bb"], "tags": null}, {"context": {}, "expected": null, "id": "dbe635d42ce1adf0761b3c335b1c4f1a9276848f3c77ac8468f6556f35aa9a2a", "input": {"input": "Opposite of hot?"}, "is_root": false, "metadata": {"tags": ["seq:step:1"]}, "metrics": {}, "origin": null, "output": "Q: Opposite of hot?\nA:", "root_span_id": "8b6922be3fe6bcea0d232eeca7edc64afe1373a22f700c7d8877ff155b929a82", "scores": null, "span_attributes": {"name": "ChatPromptTemplate"}, "span_id": "dbe635d42ce1adf0761b3c335b1c4f1a9276848f3c77ac8468f6556f35aa9a2a", "span_parents": ["957ec1d394478b7e2e3e6a81f67aef45a163931beffae2ffd0250740704270d2"], "tags": null}, {"context": {}, "expected": null, "id": "3deab2a9c3142ca9ebc60aba403407849eb228a2e762b0a7d7849630cd55a1e5", "input": {"input": "Two plus two?"}, "is_root": false, "metadata": {"tags": []}, "metrics": {}, "origin": null, "output": "Two plus two equals four.", "root_span_id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "scores": null, "span_attributes": {"name": "RunnableSequence"}, "span_id": "3deab2a9c3142ca9ebc60aba403407849eb228a2e762b0a7d7849630cd55a1e5", "span_parents": ["c62b0d60c1e2647466e439cab0a6d96c970f3b22bcf895f6f0d50e3cad9d7c45"], "tags": null}, {"context": {}, "expected": null, "id": "e6b46d2e2cfcc9c9474990a60ec21798a4956653d4be75380a38eb3e8a7a6eb8", "input": {"input": "Two plus two?"}, "is_root": false, "metadata": {"tags": ["seq:step:1"]}, "metrics": {}, "origin": null, "output": "Q: Two plus two?\nA:", "root_span_id": "8bbe0aded9e1f59017520853793ddde919a3b2b333b9bce333738cbc2d016e79", "scores": null, "span_attributes": {"name": "ChatPromptTemplate"}, "span_id": "e6b46d2e2cfcc9c9474990a60ec21798a4956653d4be75380a38eb3e8a7a6eb8", "span_parents": ["3deab2a9c3142ca9ebc60aba403407849eb228a2e762b0a7d7849630cd55a1e5"], "tags": null}]