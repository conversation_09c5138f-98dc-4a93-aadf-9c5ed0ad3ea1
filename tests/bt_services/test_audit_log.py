import braintrust
from braintrust.db_fields import IS_MERGE_FIELD, OBJECT_DELETE_FIELD
from braintrust_local.api_db_util import get_object_json, log_raw

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class AuditLogTest(BraintrustAppTestBase):
    def test_comments(self):
        logger = braintrust.init_logger("p")
        logger.log(id="row0", input="foo", output="bar", scores=dict())
        logger.log_feedback(id="row0", comment="This was okay")
        logger.log_feedback(id="row0", comment="This was great")
        braintrust.flush()

        def get_comment_rows(use_btql):
            if use_btql:
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/btql",
                    json=dict(
                        query=f"select: * from: project_logs('{logger.id}')",
                        _testing_only_use_extended_schema=True,
                    ),
                ).json()["data"]
                return {comment["comment"]["text"]: comment for row in resp for comment in row.get("comments", [])}
            else:
                audit_log_rows = get_object_json("project_logs", logger.id, audit_log=True)
                return {x["comment"]["text"]: x for x in audit_log_rows if (x.get("comment") or {}).get("text")}

        for use_btql in [True, False]:
            comment_rows = get_comment_rows(use_btql)
            self.assertEqual(set(comment_rows.keys()), {"This was okay", "This was great"})

        # With btql we should be able to filter for comments.
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * from: project_logs('{logger.id}') filter: comments[0].comment.text = 'This was okay'",
                _testing_only_use_extended_schema=True,
            ),
        ).json()["data"]
        self.assertEqual(len(resp), 1)
        self.assertEqual(resp[0]["id"], "row0")

        # Manually delete the comment.
        row_to_delete = comment_rows["This was okay"]
        log_raw(
            dict(
                id=row_to_delete["id"],
                origin=row_to_delete["origin"],
                log_id="g",
                project_id=logger.id,
                comment={},
                created=row_to_delete["created"],
                **{
                    OBJECT_DELETE_FIELD: True,
                    IS_MERGE_FIELD: True,
                },
            ),
        )

        for use_btql in [True, False]:
            comment_rows = get_comment_rows(use_btql)
            self.assertEqual(set(comment_rows.keys()), {"This was great"})

    def test_no_trace_expansion(self):
        exp = braintrust.init("p")
        with exp.start_span(id="span0") as span:
            span.log(input="foo", output="bar")
            with span.start_span(id="span1") as span1:
                span1.log(input="baz", output="buz")
        braintrust.flush()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * from: experiment('{exp.id}') filter: id='span1'",
                fmt="json",
                audit_log=True,
            ),
        ).json()["data"]
        self.assertEqual(set(["span1"]), set(r["origin"]["id"] for r in resp))

    def test_audit_log_filters(self):
        logger = braintrust.init_logger("p")
        logger.log(id="row0", input="foo", output="bar")
        braintrust.flush()

        # If we don't specify any filter, we should get an empty result.
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * from: project_logs('{logger.id}')",
                fmt="json",
                audit_log=True,
            ),
        ).json()["data"]
        self.assertEqual(resp, [])

        # But for experiments, we should get the row.
        exp = braintrust.init("p")
        exp.log(id="row0", input="foo", output="bar", scores=dict())
        braintrust.flush()
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * from: experiment('{exp.id}')",
                fmt="json",
                audit_log=True,
            ),
        ).json()["data"]
        self.assertEqual(set(["row0"]), set(r["origin"]["id"] for r in resp))

    def test_audit_log_btql(self):
        logger = braintrust.init_logger("p")
        logger.log(id="row0", input="foo", output="bar")
        braintrust.flush()
        logger.update_span(id="row0", expected="foo")
        braintrust.flush()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * from: project_logs('{logger.id}')",
                _testing_only_use_extended_schema=True,
            ),
        ).json()["data"]
        audit_data = resp[0]["audit_data"]
        self.assertEqual(len(audit_data), 2)
        self.assertEqual(audit_data[0]["audit_data"], {"action": "upsert"})
        self.assertEqual(
            audit_data[1]["audit_data"], {"action": "merge", "from": None, "path": ["expected"], "to": "foo"}
        )
