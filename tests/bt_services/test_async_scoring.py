import dataclasses
import json
from typing import Dict, List

import braintrust
import redis
import requests
from braintrust.db_fields import ASYNC_SCORING_CONTROL_FIELD, MERGE_PATHS_FIELD, SKIP_ASYNC_SCORING_FIELD
from braintrust_local.api_db_util import get_object_json
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT
from braintrust_local.test_proxy_util import get_test_proxy_server_config

from tests.braintrust_app_test_base import LOCAL_API_URL, TEST_ARGS, BraintrustAppTestBase

# see api-ts/src/env.ts for details - when testing we set this lower to avoid hitting the btql response overflow
# NOTE: keep this in sync with api-ts/.env.development and local/py/src/braintrust_local/generate_docker_compose.py
ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD = 64 * 1024

# Similar to the makeObjectIdKey function defined in
# api-ts/src/async_scoring.ts.
def make_object_id_key(project_id: str):
    return json.dumps(["async_scoring_cache_keys", "project_logs", project_id], separators=(",", ":"))


def collect_scores(rows):
    return {row["id"]: {k: round(v, 2) for k, v in (row.get("scores") or {}).items()} for row in rows}


def collect_root_span_scores(rows):
    return collect_scores([row for row in rows if row.get("span_id") == row.get("root_span_id")])


class AsyncScoringTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.conn = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT, db=0)

    def tearDown(self):
        if TEST_ARGS.get("update"):
            proxy_url = braintrust.logger._state.proxy_url
            requests.get(f"{proxy_url}/proxy/dump-cache")
        super().tearDown()

    def _set_config(self, project_id, config):
        # First wipe out any existing configs.
        existing_configs = self.run_request(
            "get", f"{LOCAL_API_URL}/v1/project_score", params={"project_id": project_id}
        ).json()
        for c in existing_configs["objects"]:
            self.run_request("delete", f"{LOCAL_API_URL}/v1/project_score/{c['id']}")

        # Now re-add the new configs.
        for i, c in enumerate(config):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/v1/project_score",
                json=dict(
                    project_id=project_id,
                    name=f"config_{project_id}_{i}",
                    score_type="online",
                    config=dict(online=c),
                ),
            )

    def _flush(self):
        braintrust.flush()
        BraintrustAppTestBase.flush_proxy_promises()

    def test_basic(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="Levenshtein"), dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="bar")
            span0_slug = span0.export()
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="goo", output="bar", expected="baz")
            span1_slug = span1.export()
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(
            rows,
            {
                "row0": {"Levenshtein": 1, "ExactMatch": 1},
                "row1": {"Levenshtein": 0.67, "ExactMatch": 0},
            },
        )

        # Updating the rows should re-trigger scoring.
        braintrust.update_span(span0_slug, expected="baz")
        braintrust.update_span(span1_slug, expected="bar")
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(
            rows,
            {
                "row0": {"Levenshtein": 0.67, "ExactMatch": 0},
                "row1": {"Levenshtein": 1, "ExactMatch": 1},
            },
        )

        # Doing a BTQL query with the expanded schema should give us the
        # "enabled" async scoring for these rows.
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(query=f"select: * from: project_logs('{logger.id}')", _testing_only_use_extended_schema=True),
        ).json()["data"]
        row_to_async_scoring_state = {r["id"]: r.get("_async_scoring_state") for r in resp}
        self.assertEqual(row_to_async_scoring_state["row0"]["status"], "enabled")
        self.assertEqual(row_to_async_scoring_state["row1"]["status"], "enabled")

    def test_multi_project_insert(self):
        logger0 = braintrust.init_logger("test0")
        logger1 = braintrust.init_logger("test1")
        logger2 = braintrust.init_logger("test2")
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
            )
        ]
        self._set_config(logger0.project.id, config)
        self._set_config(logger1.project.id, config)

        # Use sync flush mode so that we can write to all three projects in a
        # single request.
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            for l in [logger0, logger1, logger2]:
                l.log(id="row", input="foo", output="bar", expected="bar")
            custom_bg_logger.flush()
        self._flush()

        rows0, rows1, rows2 = [
            collect_root_span_scores(get_object_json("project_logs", l.project.id))
            for l in [logger0, logger1, logger2]
        ]
        self.assertEqual(rows0, {"row": {"ExactMatch": 1}})
        self.assertEqual(rows1, {"row": {"ExactMatch": 1}})
        self.assertEqual(rows2, {"row": {}})

    def test_sampling_rate(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=0.5,
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        for i in range(100):
            logger.log(id=f"row{i}", input="foo", output="bar", expected="bar")
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        frac_rows_with_scores = len([row for row in rows.values() if row]) / len(rows)
        self.assertLess(abs(frac_rows_with_scores - 0.5), 0.2)

    def test_btql_filter(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="ExactMatch")],
                    sampling_rate=1,
                    btql_filter="input = 'foo' OR input = 'bar'",
                )
            ],
        )
        logger.log(id="row0", input="foo", output="bar", expected="bar")
        logger.log(id="row1", input="goo", output="bar", expected="bar")
        logger.log(id="row2", input="bar", output="bar", expected="bar")
        self._flush()

        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="Levenshtein")],
                    sampling_rate=1,
                    btql_filter="input = 'foo' OR input = 'bar'",
                    apply_to_span_names=["sub0", "sub1"],
                )
            ],
        )
        with braintrust.start_span(id="row3") as root_span:
            root_span.log(input="foo", output="bar", expected="bar", metadata={"something": 1})
            with root_span.start_span(id="sub0", name="sub0") as subspan:
                subspan.log(input="goo", output="bar", expected="bar", metadata={"something": 2})
                with subspan.start_span(id="sub1", name="sub1") as subsubspan:
                    subsubspan.log(input="bar", output="xxx", expected="bar", metadata={"something": 3})
        self._flush()

        rows = collect_scores(
            [
                row
                for row in get_object_json("project_logs", project_id)
                if row["id"].startswith("row") or row["id"].startswith("sub")
            ]
        )
        self.assertEqual(
            rows,
            {
                "row0": {"ExactMatch": 1},
                "row1": {},
                "row2": {"ExactMatch": 1},
                "row3": {},
                "sub0": {},
                "sub1": {"Levenshtein": 0},
            },
        )

    def test_modify_config(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="ExactMatch")],
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )
        with logger.start_span(id="row0") as span:
            span.log(input="foo", output="bar", expected="bar")
            span0_slug = span.export()
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(
            rows,
            {
                "row0": {"ExactMatch": 1},
            },
        )
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="Levenshtein")],
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )
        logger.log(id=f"row1", input="foo", output="bar", expected="baz", allow_concurrent_with_spans=True)
        # Also update the original row. It should still use the original scoring
        # function.
        braintrust.update_span(span0_slug, expected="baz")
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(
            rows,
            {
                "row0": {"ExactMatch": 0},
                "row1": {"Levenshtein": 0.67},
            },
        )

    def test_metrics_end_epoch(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="ExactMatch")],
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )

        with logger.start_span(id="row0", start_time=950) as span:
            span.log(input="foo", output="bar", expected="bar")
            self._flush()
            # Before we've logged metrics.end, there should be no score.
            rows = collect_root_span_scores(get_object_json("project_logs", project_id))
            self.assertEqual(rows, {"row0": {}})
            # But after we log it, there should be.
            span.log(metrics=dict(end=1000))
            self._flush()
            rows = collect_root_span_scores(get_object_json("project_logs", project_id))
            self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

            # If we clear the async scoring state back to "auto-determine", it
            # will stop scoring the row, because we are past the "metrics.end"
            # epoch.
            span.log(**{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_override", state=None)})
            braintrust.flush()
            span.log(output="boo")
            rows = collect_root_span_scores(get_object_json("project_logs", project_id))
            self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

            # But if we manually un-set metrics.end and then re-set it, we will
            # re-trigger the scoring criteria and re-run the score.
            span.log(metrics=dict(start=950), **{MERGE_PATHS_FIELD: [["metrics"]]})
            braintrust.flush()
            span.log(metrics=dict(end=1000))
            self._flush()
            rows = collect_root_span_scores(get_object_json("project_logs", project_id))
            self.assertEqual(rows, {"row0": {"ExactMatch": 0}})

    def test_async_scoring_control_state_override(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="ExactMatch")],
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )
        with logger.start_span(id="row0") as span:
            span_slug = span.export()
            span.log(input="foo", output="bar", expected="bar")
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # Explicitly disable async scoring. We should not trigger a re-scoring
        # off the update.
        braintrust.update_span(
            span_slug,
            expected="baz",
            **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_override", state=None)},
        )
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # Explicitly re-enable async scoring. It should re-trigger scoring.
        braintrust.update_span(
            span_slug,
            **{
                ASYNC_SCORING_CONTROL_FIELD: dict(
                    kind="state_override",
                    state=dict(status="enabled", token="foobar", function_ids=[dict(global_function="ExactMatch")]),
                )
            },
        )
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 0}})

    def test_async_scoring_control_state_override_disabled(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="ExactMatch")],
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )
        # Initially overriding the state to null, or passing skip on an
        # intermediate row will not actually turn off scoring, since it will get
        # selected once we publish `metrics.end`.
        with logger.start_span(
            id="row0", **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_override", state=None)}
        ) as span:
            braintrust.flush()
            span_slug = span.export()
            span.log(input="foo", output="bar", expected="bar", **{SKIP_ASYNC_SCORING_FIELD: True})
            braintrust.flush()
        # But if we explicitly disable the state, it will not get selected later
        # on.
        with logger.start_span(
            id="row1", **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_override", state=dict(status="disabled"))}
        ) as span:
            braintrust.flush()
            span.log(input="foo", output="baz", expected="baz")
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}, "row1": {}})

    def test_async_scoring_control_score_update(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config_function_ids = [dict(type="global", name="ExactMatch")]
        self._set_config(
            project_id,
            [
                dict(
                    scorers=config_function_ids,
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )
        with logger.start_span(id="row0") as span:
            span_slug = span.export()
            span.log(input="foo", output="bar", expected="bar")
        self._flush()

        # If we try a score update control with the wrong token, it should fail.
        braintrust.update_span(
            span_slug,
            scores={"ExactMatch": 0.5},
            **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="score_update", token="foobar")},
        )
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # But if we explicitly override the token and then run a score update,
        # it should work.
        braintrust.update_span(
            span_slug,
            **{
                ASYNC_SCORING_CONTROL_FIELD: dict(
                    kind="state_override",
                    state=dict(status="enabled", token="foobar", function_ids=[dict(global_function="ExactMatch")]),
                )
            },
        )
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})
        braintrust.update_span(
            span_slug,
            scores={"ExactMatch": 0.5},
            **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="score_update", token="foobar"), SKIP_ASYNC_SCORING_FIELD: True},
        )
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 0.5}})

    def test_async_scoring_control_state_force_reselect(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="ExactMatch")],
                    sampling_rate=0,
                    apply_to_root_span=True,
                )
            ],
        )
        with logger.start_span(id="row0") as span:
            span_slug = span.export()
            span.log(input="foo", output="bar", expected="bar")
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {}})

        # If we change the config to select everything, it should still not
        # score the row, because it has passed its epoch. But it'll score
        # another row.
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="ExactMatch")],
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )
        braintrust.update_span(span_slug, output="baz", expected="baz")
        logger.log(id=f"row1", input="foo", output="baz", expected="baz", allow_concurrent_with_spans=True)
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {}, "row1": {"ExactMatch": 1}})

        # Even force-overriding the state back to null should not do anything.
        braintrust.update_span(span_slug, **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_override", state=None)})
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {}, "row1": {"ExactMatch": 1}})

        # But if we force re-selection, it should select the row and score it.
        braintrust.update_span(span_slug, **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_force_reselect")})
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}, "row1": {"ExactMatch": 1}})

        # If we change the scoring function and force re-selection, we should
        # retain the old score and include the new score.
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[dict(type="global", name="Levenshtein")],
                    sampling_rate=1,
                    apply_to_root_span=True,
                )
            ],
        )
        braintrust.update_span(
            span_slug, expected="bar", **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_force_reselect")}
        )
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(rows, {"row0": {"ExactMatch": 1, "Levenshtein": 0.67}, "row1": {"ExactMatch": 1}})

    def test_nonexistent_function(self):
        NONEXISTENT_FUNCTION_NAME = "__BT_UNITTEST_NONEXISTENT_FUNCTION__"
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        self._set_config(
            project_id,
            [
                dict(
                    scorers=[
                        dict(type="global", name=NONEXISTENT_FUNCTION_NAME),
                        dict(type="global", name="ExactMatch"),
                    ],
                    sampling_rate=1.0,
                    apply_to_root_span=True,
                )
            ],
        )

        logger.log(id="row0", input="foo", output="bar", expected="bar")
        self._flush()
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})
        error_rows = [r for r in all_rows if r.get("error")]
        self.assertEqual(len(error_rows), 1)
        self.assertIn(f"Function {NONEXISTENT_FUNCTION_NAME} not found", error_rows[0]["error"])

    def test_multiple_configs(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="Levenshtein")],
                sampling_rate=0.0,
                apply_to_root_span=True,
            ),
            dict(
                scorers=[dict(type="global", name="Levenshtein")],
                sampling_rate=1.0,
                apply_to_span_names=["sub0", "sub1"],
            ),
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_span_names=["sub1", "sub2"],
            ),
        ]
        self._set_config(project_id, config)

        ROW_CONTENTS = dict(input="foo", output="bar", expected="baz")
        with braintrust.start_span(id="row0") as root_span:
            root_span.log(**ROW_CONTENTS)
            with root_span.start_span(id="sub0", name="sub0") as subspan:
                subspan.log(**ROW_CONTENTS)
                with subspan.start_span(id="sub1", name="sub1") as subsubspan:
                    subsubspan.log(**ROW_CONTENTS)
            with root_span.start_span(id="sub2", name="sub2") as subspan:
                subspan.log(**ROW_CONTENTS)
                with subspan.start_span(id="sub3", name="sub3") as subsubspan:
                    subsubspan.log(**ROW_CONTENTS)
        self._flush()
        rows = {
            k: v
            for k, v in collect_scores(get_object_json("project_logs", project_id)).items()
            if k in ["row0", "sub0", "sub1", "sub2", "sub3"]
        }
        self.assertEqual(
            rows,
            {
                "row0": {},
                "sub0": {"Levenshtein": 0.67},
                "sub1": {"Levenshtein": 0.67, "ExactMatch": 0},
                "sub2": {"ExactMatch": 0},
                "sub3": {},
            },
        )

    def test_custom_prompt_function(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id

        function_record = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/function",
            json=dict(
                project_id=project_id,
                prompt_data={
                    "prompt": {
                        "type": "chat",
                        "messages": [
                            {
                                "role": "user",
                                "content": "If {{input}} is less than or equal to 5, return choice A, otherwise, return choice B.",
                            }
                        ],
                    },
                    "options": {
                        "model": "gpt-4o",
                    },
                    "parser": {
                        "type": "llm_classifier",
                        "use_cot": True,
                        "choice_scores": {
                            "A": 0.1,
                            "B": 0.9,
                        },
                    },
                },
                function_data={
                    "type": "prompt",
                },
                name="my prompt",
                slug="my-slug",
            ),
        ).json()

        config = [
            dict(
                scorers=[dict(type="function", id=function_record["id"])],
                sampling_rate=1.0,
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        logger.log(id="row0", input="5")
        logger.log(id="row1", input="6")
        self._flush()
        rows = collect_root_span_scores(get_object_json("project_logs", project_id))
        self.assertEqual(
            rows,
            {
                "row0": {"my prompt": 0.1},
                "row1": {"my prompt": 0.9},
            },
        )

    def test_no_infinite_loop(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[
                    dict(type="global", name="ExactMatch"),
                ],
                sampling_rate=1.0,
                apply_to_root_span=True,
            ),
            dict(
                scorers=[
                    dict(type="global", name="ExactMatch"),
                ],
                # Set this slightly below 1, so that in case there really is an
                # infinite loop, we don't go crazy. At worst the test will emit
                # false negatives, but no false positives.
                sampling_rate=0.95,
                apply_to_span_names=["ExactMatch"],
            ),
        ]
        self._set_config(project_id, config)

        logger.log(id="row0", input="foo", output="bar", expected="bar")
        # Flush several times in a row, to make sure any infinite loops are
        # played out.
        for i in range(10):
            self._flush()
        rows = get_object_json("project_logs", project_id)
        self.assertEqual(len(rows), 2)
        name_to_row = {row["span_attributes"]["name"]: row for row in rows}
        self.assertEqual(set(name_to_row.keys()), {"root", "ExactMatch"})
        self.assertEqual(name_to_row["root"]["scores"], {"ExactMatch": 1})

    def test_skip_logging(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
                skip_logging=True,
            )
        ]
        self._set_config(project_id, config)

        # Log a span and verify it gets scored but doesn't create a scoring span
        logger.log(id="row0", input="foo", output="bar", expected="bar")
        self._flush()
        rows = get_object_json("project_logs", project_id)
        self.assertEqual(len(rows), 1)  # Should only have the original span, no scoring span
        self.assertEqual(rows[0]["scores"], {"ExactMatch": 1})  # Should still have scores

        # Test that even with multiple scorers, no scoring spans are created
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch"), dict(type="global", name="Levenshtein")],
                sampling_rate=1.0,
                apply_to_root_span=True,
                skip_logging=True,
            )
        ]
        self._set_config(project_id, config)

        logger.log(id="row1", input="foo", output="bar", expected="baz")
        self._flush()
        rows = get_object_json("project_logs", project_id)
        self.assertEqual(len(rows), 2)  # Should only have the original spans
        scores = collect_root_span_scores(rows)
        self.assertEqual(scores, {"row0": {"ExactMatch": 1}, "row1": {"ExactMatch": 0, "Levenshtein": 0.67}})

    def test_async_scoring_big_payload(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="Levenshtein"), dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        fs = "f" * (ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD // 4)
        gs = "g" * (ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD // 4)
        # Flush the rows incrementally to ensure we use the final row size when
        # figuring out async scoring.
        with braintrust.start_span(id="row0") as span0:
            span0.log(input="big_payload", output=fs * 2, expected=fs + gs)
            self._flush()
            span0_slug = span0.export()
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="big_payload", output=gs * 2, expected=gs * 2)
            self._flush()
            span1_slug = span1.export()
        self._flush()

        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(
            rows,
            {
                "row0": {"Levenshtein": 0.5, "ExactMatch": 0},
                "row1": {"Levenshtein": 1, "ExactMatch": 1},
            },
        )
        self.assertEqual(sum(r["span_attributes"]["name"] == "Resolve Row Ref Input" for r in all_rows), 4)

        # Updating the rows should re-trigger scoring.
        braintrust.update_span(span0_slug, expected=fs * 2)
        braintrust.update_span(span1_slug, expected=fs + gs)
        self._flush()
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(
            rows,
            {
                "row0": {"Levenshtein": 1, "ExactMatch": 1},
                "row1": {"Levenshtein": 0.5, "ExactMatch": 0},
            },
        )
        # We should have 8 resolveRowRefInput spans, 4 from before the update
        # and 4 from after.
        self.assertEqual(sum(r["span_attributes"]["name"] == "Resolve Row Ref Input" for r in all_rows), 8)

    def test_tags_change_no_async_scoring(self):
        """Test that changing tags does not trigger re-scoring when other fields remain the same."""
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        # Create initial span with scoring
        with braintrust.start_span(id="row0") as span:
            span.log(input="foo", output="bar", expected="bar", tags=["tag1", "tag2"])
            span_slug = span.export()
        self._flush()

        # Verify initial scoring happened
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # Count number of scoring spans initially
        initial_scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")

        # Update only the tags
        braintrust.update_span(span_slug, tags=["tag3", "tag4"])
        self._flush()

        # Check that scores remain the same and no new scoring happened
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # Verify no new scoring spans were created (scoring was not re-triggered)
        new_scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")
        self.assertEqual(new_scoring_spans, initial_scoring_spans, "Tags change should not trigger new scoring")

        # Now update a field that SHOULD trigger scoring (expected)
        braintrust.update_span(span_slug, expected="baz")
        self._flush()

        # Verify re-scoring happened
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {"ExactMatch": 0}})

        # Verify a new scoring span was created
        final_scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")
        self.assertGreater(final_scoring_spans, new_scoring_spans, "Expected field change should trigger new scoring")

    def test_tags_change_with_force_rescore_control(self):
        """Test that changing tags with state_enabled_force_rescore control does trigger re-scoring."""
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        # Create initial span with scoring
        with braintrust.start_span(id="row0") as span:
            span.log(input="foo", output="bar", expected="bar", tags=["tag1", "tag2"])
            span_slug = span.export()
        self._flush()

        # Verify initial scoring happened
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # Count number of scoring spans initially
        initial_scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")

        # Update only tags with force rescore control
        braintrust.update_span(
            span_slug, tags=["tag3", "tag4"], **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_enabled_force_rescore")}
        )
        self._flush()

        # Check that scores remain the same (input/output/expected haven't changed)
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # Verify a new scoring span WAS created due to force rescore
        new_scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")
        self.assertGreater(new_scoring_spans, initial_scoring_spans, "Force rescore should trigger new scoring")

    def test_force_rescore_control_only_works_when_enabled(self):
        """Test that state_enabled_force_rescore control only works when state is already enabled."""
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=0.0,  # Disable sampling so it won't be enabled initially
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        # Create initial span without scoring (due to sampling rate)
        with braintrust.start_span(id="row0") as span:
            span.log(input="foo", output="bar", expected="bar")
            span_slug = span.export()
        self._flush()

        # Verify no scoring happened
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {}})

        # Try to use force rescore control - it should NOT trigger scoring
        braintrust.update_span(
            span_slug, tags=["tag1"], **{ASYNC_SCORING_CONTROL_FIELD: dict(kind="state_enabled_force_rescore")}
        )
        self._flush()

        # Verify still no scoring
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {}})

        # No scoring spans should exist
        scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")
        self.assertEqual(scoring_spans, 0, "Force rescore should not work when state is not enabled")

    def test_tags_change_with_explicit_scoring_control(self):
        """Test that changing tags with explicit scoring control does trigger re-scoring."""
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
            )
        ]
        self._set_config(project_id, config)

        # Create initial span with scoring
        with braintrust.start_span(id="row0") as span:
            span.log(input="foo", output="bar", expected="bar", tags=["tag1", "tag2"])
            span_slug = span.export()
        self._flush()

        # Verify initial scoring happened
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(rows, {"row0": {"ExactMatch": 1}})

        # Count number of scoring spans initially
        initial_scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")

        # Update tags AND add explicit scoring control to force re-scoring
        braintrust.update_span(
            span_slug,
            tags=["tag3", "tag4"],
            **{
                ASYNC_SCORING_CONTROL_FIELD: dict(
                    kind="state_override",
                    state=dict(status="enabled", token="new_token", function_ids=[dict(global_function="ExactMatch")]),
                )
            },
        )
        self._flush()

        # Check that re-scoring happened even though only tags changed (due to explicit control)
        all_rows = get_object_json("project_logs", project_id)
        rows = collect_root_span_scores(all_rows)
        self.assertEqual(
            rows, {"row0": {"ExactMatch": 1}}
        )  # Score should remain the same since input/output/expected haven't changed

        # Verify a new scoring span was created (scoring was re-triggered)
        new_scoring_spans = sum(1 for r in all_rows if r.get("span_attributes", {}).get("name") == "ExactMatch")
        self.assertGreater(
            new_scoring_spans,
            initial_scoring_spans,
            "Explicit scoring control should trigger new scoring even with only tags change",
        )

    def test_empty_apply_to_span_names(self):
        """Test that apply_to_span_names=[] with apply_to_root_span=true works correctly."""
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=True,
                apply_to_span_names=[],
            )
        ]
        self._set_config(project_id, config)

        # Create spans - both root and nested
        with braintrust.start_span(id="row0") as root_span:
            root_span.log(input="foo", output="bar", expected="bar")
            with root_span.start_span(id="sub0", name="sub0") as subspan:
                subspan.log(input="goo", output="bar", expected="bar")
        self._flush()

        # Only root span should be scored since apply_to_span_names=[] and apply_to_root_span=true
        all_rows = get_object_json("project_logs", project_id)
        # Filter to just the spans we created (exclude scoring spans)
        rows = {row["id"]: row.get("scores") or {} for row in all_rows if row["id"] in ["row0", "sub0"]}
        self.assertEqual(
            rows,
            {
                "row0": {"ExactMatch": 1},
                "sub0": {},
            },
        )

    def test_empty_apply_to_span_names_with_btql_filter(self):
        """Test that apply_to_span_names=[] with apply_to_root_span=false but btql_filter set works correctly."""
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = [
            dict(
                scorers=[dict(type="global", name="ExactMatch")],
                sampling_rate=1.0,
                apply_to_root_span=False,
                apply_to_span_names=[],
                btql_filter="input = 'foo'",
            )
        ]
        self._set_config(project_id, config)

        # Create spans
        logger.log(id="row0", input="foo", output="bar", expected="bar")
        logger.log(id="row1", input="goo", output="bar", expected="bar")
        self._flush()

        # Without the fix, this would fail because empty apply_to_span_names causes all spans to be skipped
        all_rows = get_object_json("project_logs", project_id)
        rows = {row["id"]: row.get("scores") or {} for row in all_rows if row["id"] in ["row0", "row1"]}
        self.assertEqual(
            rows,
            {
                "row0": {"ExactMatch": 1},  # Should be scored due to btql_filter
                "row1": {},  # Should not be scored
            },
        )

    def test_test_online_scoring(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        config = dict(
            scorers=[dict(type="global", name="ExactMatch"), dict(type="global", name="Nonexistent")],
            btql_filter="input = 'foo' OR input = 'bar'",
            apply_to_span_names=["sub0", "sub1"],
            sampling_rate=1.0,
        )

        with braintrust.start_span(id="row0") as root_span:
            root_span.log(input="foo", output="bar", expected="bar", metadata={"something": 1})
            with root_span.start_span(id="sub0", name="sub0") as subspan:
                subspan.log(input="goo", output="bar", expected="bar", metadata={"something": 2})
                with subspan.start_span(id="sub1", name="sub1") as subsubspan:
                    subsubspan.log(input="bar", output="xxx", expected="bar", metadata={"something": 3})
        self._flush()

        # only sub1 matches the btql filter and span filters
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, name="test", score_type="online", config=dict(online=config)),
        )
        assert resp.status_code == 200
        data = resp.json()
        assert data["kind"] == "success"
        assert data["payload"] == [
            {
                "row": {"input": "bar", "output": "xxx", "expected": "bar", "metadata": {"something": 3}},
                "results": [
                    {"kind": "success", "global_function": "ExactMatch", "result": {"name": "ExactMatch", "score": 0}},
                    {
                        "kind": "error",
                        "global_function": "Nonexistent",
                        "error": "Error: Function Nonexistent not found",
                    },
                ],
            },
        ]

        config = dict(
            scorers=[],
            btql_filter="input = 'foo' OR input = 'bar'",
            sampling_rate=1.0,
        )
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, name="test", score_type="online", config=dict(online=config)),
        )
        assert resp.status_code == 200
        data = resp.json()
        assert data["kind"] == "error"
        assert data["message"] == "No scorers found in online scoring config"

        config = dict(
            scorers=[dict(type="global", name="ExactMatch")],
            btql_filter="input = 47 AND output = 'nonexistent'",
            sampling_rate=1.0,
        )
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, name="test", score_type="online", config=dict(online=config)),
        )
        assert resp.status_code == 200
        data = resp.json()
        assert data["kind"] == "error"
        assert data["message"] == "No matching rows found for the provided filters. Adjust the filter to test again."
