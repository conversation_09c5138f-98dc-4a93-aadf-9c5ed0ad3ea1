# BraintrustClient Usage Guide

This guide demonstrates how to use the `BraintrustClient` class to create project automations in Braintrust.

## Prerequisites

1. **Install required dependencies:**
   ```bash
   pip install requests
   ```

2. **Set up environment variables:**
   ```bash
   export BRAINTRUST_API_KEY="your-actual-api-key"
   export BRAINTRUST_ORG_NAME="your-org-name"
   export BRAINTRUST_PROJECT_ID="your-project-id"
   export BRAINTRUST_API_URL="https://api.braintrust.dev"  # Optional, defaults to this
   ```

3. **For S3 exports (optional):**
   ```bash
   export AWS_ACCESS_KEY_ID="your-aws-access-key"
   export AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
   ```

## Quick Start

```python
from braintrust_client_example import BraintrustClient, create_webhook_automation_config

# Initialize the client
client = BraintrustClient(
    api_url="https://api.braintrust.dev",
    org_name="your-org-name",
    api_key="your-api-key"
)

# Create a simple webhook automation
config = create_webhook_automation_config(
    btql_filter="error_count > 0",
    webhook_url="https://your-webhook.com/alerts",
    interval_seconds=3600  # 1 hour
)

automation = client.create_project_automation(
    project_id="your-project-id",
    name="Error Alerts",
    config=config
)

print(f"Created automation: {automation['id']}")
```

## Automation Types

### 1. Webhook Automations

Trigger webhooks when certain conditions are met in your logs:

```python
config = create_webhook_automation_config(
    btql_filter="error_count > 5 AND timestamp > now() - interval '1 hour'",
    webhook_url="https://hooks.slack.com/your-webhook-url",
    interval_seconds=1800  # 30 minutes
)
```

**Common BTQL filters:**
- `error_count > 0` - Any errors
- `latency > 1000` - High latency requests
- `status_code >= 400` - HTTP errors
- `model = 'gpt-4' AND cost > 0.1` - Expensive model calls

### 2. BTQL Export Automations

Export your data to external storage systems:

```python
config = create_btql_export_automation_config(
    export_path="s3://my-bucket/braintrust-data/",
    format="parquet",  # or "jsonl"
    interval_seconds=86400,  # Daily
    batch_size=10000,
    export_type="log_traces"  # or "log_spans", "btql_query"
)
```

**Export types:**
- `log_traces` - Export complete trace data
- `log_spans` - Export individual span data
- `btql_query` - Export results of a custom BTQL query

### 3. Retention Policy Automations

Automatically clean up old data:

```python
config = create_retention_automation_config(
    object_type="project_logs",  # or "experiment", "dataset"
    retention_days=90
)
```

## Running the Example

1. **Set your environment variables:**
   ```bash
   export BRAINTRUST_API_KEY="bt-..."
   export BRAINTRUST_ORG_NAME="my-org"
   export BRAINTRUST_PROJECT_ID="proj-..."
   ```

2. **Run the example script:**
   ```bash
   python braintrust_client_example.py
   ```

3. **Expected output:**
   ```
   🚀 Initialized BraintrustClient for org: my-org
      API URL: https://api.braintrust.dev
      Project ID: proj-...

   📡 Creating webhook automation...
   ✅ Created webhook automation: Error Alert Webhook
      ID: auto-...

   📊 Creating BTQL export automation...
   ✅ Created export automation: Daily Log Export
      ID: auto-...

   🗂️  Creating retention policy automation...
   ✅ Created retention automation: 90-Day Log Retention
      ID: auto-...

   🎉 Example completed!
   ```

## Error Handling

The client includes basic error handling. Common issues:

- **401 Unauthorized**: Check your API key
- **403 Forbidden**: Verify org name and project permissions
- **404 Not Found**: Ensure project ID exists
- **422 Validation Error**: Check your configuration format

## Advanced Usage

### Custom BTQL Query Export

```python
config = create_btql_export_automation_config(
    export_path="s3://analytics-bucket/custom-reports/",
    format="parquet",
    export_type="btql_query",
    btql_query="""
        SELECT 
            timestamp,
            model,
            cost,
            latency,
            error_count
        FROM logs 
        WHERE timestamp > now() - interval '1 day'
        AND cost > 0.01
    """,
    batch_size=5000
)
```

### Multiple Webhook Actions

You can create multiple automations for different alert conditions:

```python
# High error rate alert
error_config = create_webhook_automation_config(
    btql_filter="error_count > 10",
    webhook_url="https://alerts.company.com/critical",
    interval_seconds=300  # 5 minutes
)

# Cost monitoring alert
cost_config = create_webhook_automation_config(
    btql_filter="cost > 1.0",
    webhook_url="https://alerts.company.com/cost",
    interval_seconds=3600  # 1 hour
)
```

## Best Practices

1. **Start with longer intervals** (e.g., 1 hour) and adjust based on your needs
2. **Test your BTQL filters** in the Braintrust dashboard first
3. **Use specific webhook URLs** for different types of alerts
4. **Monitor your automation costs** - frequent exports can be expensive
5. **Set appropriate retention policies** to manage storage costs

## Troubleshooting

If you encounter issues:

1. **Verify your credentials** are correct and have the right permissions
2. **Check the Braintrust dashboard** to see if automations were created
3. **Test your webhook URLs** independently to ensure they're working
4. **Validate your BTQL syntax** using the Braintrust query interface
5. **Check AWS credentials** for S3 export automations

For more information, visit the [Braintrust documentation](https://www.braintrust.dev/docs).
