import { Request, Response } from "express";
import {
  AliasExpr,
  ParsedQuery,
  parsedQuerySchema,
  parseQuery,
  Parser,
} from "@braintrust/btql/parser";
import {
  OBJECT_CACHE,
  ObjectCacheEntry,
  objectCacheEntryToAuditObject,
} from "./object_cache";
import {
  AccessDeniedError,
  BadRequestError,
  BT_INTERNAL_TRACE_ID_HEADER,
  BT_QUERY_PLAN_HEADER,
  InternalServerError,
  isEmpty,
  isVersionQuerySupported,
  objectTypeToAclObjectType,
  QueryTooCostlyError,
  wrapBindError,
  wrapZodError,
} from "./util";
import { genSingleObjectIdValue, scanObjectsQuery } from "./object_scan";
import {
  addCoercions,
  makeExprContext,
  planExpr,
  planQuery,
  snippet,
  sql,
  ToSQL,
} from "@braintrust/btql/planner";
import {
  bindQuery,
  traverseExpr,
  traverseQuery,
  getResultSchema,
  ResponseSchema,
  weakestScalarType,
  BoundQuery,
  BoundExpr,
} from "@braintrust/btql/binder";
import {
  PAGINATION_CURSOR_XACT_ID_FIELD,
  PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD,
} from "./pagination_cursor";
import { ObjectType, objectTypeSchema, PHYSICAL_SCHEMA } from "./schema";
import { PhysicalSchema } from "@braintrust/btql/schema";
import {
  BRAINSTORE_DISABLE_REALTIME_QUERIES,
  BRAINSTORE_PROJECT_LOGS_REALTIME_READ_TIMEOUT_MS,
  PG_LOGS2_TABLE,
  PG_LOGS_TABLE,
  RESPONSE_BUCKET_NAME,
  RESPONSE_BUCKET_OVERFLOW_THRESHOLD,
  RESPONSE_BUCKET_PREFIX,
  STRICT_VALIDATION_MODE,
  TESTING_ONLY_SKIP_LIMIT_CHECKS,
  TESTING_ONLY_ALLOW_QUERY_FULL_AUDIT_LOG,
  MAX_LIMIT_FOR_QUERIES,
} from "./env";
import * as tmp from "tmp";
import * as fs from "fs/promises";
import { z } from "zod";
import {
  applyComputedFields,
  BRAINTRUST_AUDIT_LOG_LOGICAL_SCHEMA,
  BRAINTRUST_LOGICAL_SCHEMA,
  BRAINTRUST_LOGICAL_SCHEMA_EXTENDED_FOR_TESTING,
  DUCKDB_PHYSICAL_SCHEMA,
  redactBtqlQuery,
} from "@braintrust/local/api-schema";
import { createGzip } from "zlib";
import { PassThrough, Readable } from "stream";
import { createReadStream, createWriteStream } from "fs";
import {
  _urljoin,
  BT_CURSOR_HEADER,
  deterministicReplacer,
} from "braintrust/util";
import { duckq, getDuckDBConn } from "./db/duckdb";
import { getPG } from "./db/pg";
import {
  brainstoreDefault,
  brainstoreEnabled,
  canContainRowRefs,
  canPgInsertLogs2,
  runBrainstoreQuery,
  shouldUseBrainstore,
} from "./brainstore/brainstore";
import { setAuditHeaders } from "./audit";
import { getTrackedObjectStatus } from "./brainstore/backfill";
import {
  createSummaryData,
  fetchComments,
  fetchCustomColumns,
  fetchProject,
  fetchProjectScoreConfig,
  ModelCostsMap,
  modifyResultSchemaMakeDuckdbSchema,
} from "./summary";
import { AclObjectType } from "@braintrust/typespecs";
import { fetchModelCosts } from "./summary";
import {
  DiscriminatedProjectScore,
  isAggregateScore,
  makeWeightedScoreExprs,
} from "@braintrust/local/query";
import { isBlockBackfillObject } from "./block_backfill_objects";
import { getLogger } from "./instrumentation/logger";
import { parseObjectStoreURL } from "./object-storage/url";
import { RealtimeState, FreshnessState } from "@braintrust/local/app-schema";
import { makeObjectStore, ObjectStore } from "./object-storage/object-storage";
import { customColumnSchema } from "./project-columns";

const BTQL_API_VERSION = 1;
const DEFAULT_LIMIT = 1000;
const DEFAULT_PROJECT_LOGS_LIMIT = 10;
const SEARCH_PAGE_SIZE = 1000;

const requestSchema = z.object({
  query: z.union([z.string(), parsedQuerySchema]),
  fmt: z.enum(["json", "jsonl", "parquet"]).optional().default("json"),
  api_version: z.number().optional().default(BTQL_API_VERSION),
  // Note that tz_offset follows the convention of Date.prototype.getTimezoneOffset()
  // Positive numbers represent being behind UTC (Hawaii, UTC-10:00 is 600),
  // and negative numbers represent being ahead of UTC (Sydney, UTC+10:00 is -600).
  tz_offset: z.number().optional(),
  disable_limit: z.boolean().optional(),
  // Only use `force_push_limit` if you are running a query which ends up
  // doing the object scan inside a subquery and aggregating results outside
  // the subquery. We can deprecate this option once we add support for
  // subqueries inside BTQL.
  force_push_limit: z.boolean().optional(),
  version: z.string().optional(),
  audit_log: z.boolean().optional(),
  use_match_search_index: z.boolean().optional(),
  expected_cost: z
    .number()
    .optional()
    .describe("A rough estimate of the cost on a scale of 1-10"),
  overflow_results: z
    .boolean()
    .optional()
    .describe(
      "Overflow results to object storage rather than returning them directly.",
    ),

  inference_depth: z
    .number()
    .optional()
    .describe("If set, perform schema inference up to this object depth"),
  // Some consumers do not require the strict liveness and de-duplication
  // guarantees that the search query provides by default. Set this flag to
  // true to disable these guarantees, which can significantly improve search
  // performance. There are several caveats to be aware of:
  //
  // - Search filters may match stale versions of rows, but we'll only return
  // the latest version of each trace. Which means it's possible to return
  // traces which don't match the filters. The consumer is expected to
  // re-apply the filters.
  //
  // - Paginated queries may return the same trace over multiple pages, in
  // case matching rows crop up in different pages. The consumer is expected
  // to de-duplicate rows which have already been seen.
  relaxed_search_mode: z.boolean().optional(),

  // Uses brainstore
  use_brainstore: z.boolean().optional(),
  brainstore_default_traces: z
    .boolean()
    .optional()
    .describe(
      "If true, then use the (legacy) behavior of defaulting to trace expansion. Primarily used in tests.",
    ),
  brainstore_realtime: z
    .boolean()
    .optional()
    .describe(
      "If true, use real-time data (which may slow down read performance)",
    ),
  brainstore_skip_backfill_check: z
    .boolean()
    .optional()
    .describe(
      "If true, skip checking if the data has been backfilled before using brainstore",
    ),

  query_timeout_seconds: z
    .number()
    .optional()
    .describe(
      "If set, the query will be cancelled after this many seconds. This is only supported for Brainstore queries.",
    ),

  include_plan: z
    .boolean()
    .optional()
    .describe("Include the query plan in the response"),

  _debug_use_duckdb: z.boolean().optional(),
  _debug_explain: z.boolean().optional().describe("Explain the query plan"),
  _testing_only_allow_query_full_audit_log: z
    .boolean()
    .optional()
    .describe(
      "If true, allow audit log queries to be run without filtering by id. Only used for testing.",
    ),
  _testing_only_skip_limit_checks: z
    .boolean()
    .optional()
    .describe(
      "If true, allow queries to be run without a limit. Only used for testing.",
    ),
  _testing_only_use_extended_schema: z
    .boolean()
    .optional()
    .describe(
      "If true, use an extended schema intended for testing. Only used for testing.",
    ),
  custom_column_scope: customColumnSchema
    .pick({
      object_type: true,
      object_id: true,
      subtype: true,
      variant: true,
    })
    .optional()
    .describe("The scope of the custom columns that should be applied"),
});

export type BtqlRequest = z.infer<typeof requestSchema>;

// This is an express handler
export async function runBtqlRequest({
  req,
  res,
  ctxData,
  appOrigin,
  ctxToken,
  acceptsEncodings,
  isV1FetchRequest,
  objectCacheWasCachedToken,
  auditHeaders,
}: {
  req: Request;
  res: Response;
  ctxData: unknown;
  appOrigin: string;
  ctxToken?: string;
  acceptsEncodings: (encoding: string[]) => string | false;
  isV1FetchRequest?: boolean;
  objectCacheWasCachedToken?: string;
  auditHeaders?: boolean;
}) {
  const parsed = requestSchema.safeParse(ctxData);
  if (!parsed.success) {
    res
      .status(400)
      .json({ error: "Invalid request", errors: parsed.error.errors });
    return;
  }
  const body = parsed.data;

  if (body.api_version > BTQL_API_VERSION) {
    // 501 is HTTP Version Not Supported which is the closest thing to an API version error
    res.status(501).json({
      error: `Unsupported API version ${body.api_version} (max ${BTQL_API_VERSION}))`,
    });
    return;
  }

  // If the user didn't specify use_brainstore, but the stack is defaulting to Brainstore,
  // then use Brainstore by default.
  const defaultStatus = brainstoreDefault();
  if (
    defaultStatus === "force" ||
    (defaultStatus === "default" && isEmpty(body.use_brainstore))
  ) {
    // Remove this as early as April 28, 2025
    if (
      isEmpty(body.brainstore_default_traces) &&
      body.use_brainstore !== true
    ) {
      body.brainstore_default_traces = true;
    }

    body.use_brainstore = true;
    body.brainstore_realtime = body.brainstore_realtime ?? true;
  }

  const result = await runBtql({
    body,
    objectCacheWasCachedToken,
    appOrigin,
    ctxToken,
    setTraceIdHeader: (traceId) => {
      res.setHeader(BT_INTERNAL_TRACE_ID_HEADER, traceId);
    },
    dedupeScoresAndMetrics: body.fmt === "parquet",
  });

  if ("explain" in result) {
    if (
      body._debug_use_duckdb ||
      body.overflow_results ||
      body.fmt !== "json"
    ) {
      throw new BadRequestError(
        "Only postgres, JSON, direct output is supported for explain",
      );
    }

    res.json(result);
    return;
  }

  const {
    rows,
    resultSchema,
    cursor,
    objectsByType,
    duckdbSchema,
    realtime_state,
    ...rest
  } = result;

  if (cursor) {
    res.setHeader(BT_CURSOR_HEADER, cursor);
  }
  if (body.include_plan && result.plan) {
    res.setHeader(
      BT_QUERY_PLAN_HEADER,
      Buffer.from(result.plan).toString("base64"),
    );
  }
  if (auditHeaders) {
    if (!objectsByType) {
      throw new Error("Missing objectsByType");
    }
    await setAuditHeaders({
      req,
      res,
      objects: Array.from(objectsByType.values())
        .flatMap((obj) => Object.values(obj))
        .map(objectCacheEntryToAuditObject),
    });
  }

  const acceptsGzip = acceptsEncodings(["gzip"]);

  let bytes: Readable;
  let byteLen;
  let cleanup = () => {};
  switch (body.fmt) {
    case "json": {
      res.setHeader("Content-Type", "application/json");
      const response = JSON.stringify(
        isV1FetchRequest
          ? { events: rows, cursor }
          : {
              data: rows,
              schema: resultSchema,
              cursor,
              realtime_state,
              ...rest,
            },
      );
      bytes = Readable.from(Buffer.from(response));
      byteLen = response.length;

      if (acceptsGzip) {
        bytes = bytes.pipe(createGzip());
        res.setHeader("Content-Encoding", "gzip");
      }
      break;
    }
    case "jsonl": {
      res.setHeader("Content-Type", "application/json");

      let writeStream = new PassThrough();
      if (acceptsGzip) {
        res.setHeader("Content-Encoding", "gzip");
        writeStream = writeStream.pipe(createGzip());
      }
      writeRowsToStream(rows, writeStream).catch((e) => {
        getLogger().error(
          { error: e },
          `Failed to write jsonl rows to stream: ${e instanceof Error ? e.message : `${e}`}`,
        );
      });

      bytes = writeStream;
      byteLen = JSON.stringify(rows[0] ?? null).length * rows.length; // Rough estimate
      break;
    }
    case "parquet": {
      res.setHeader("Content-Type", "application/octet-stream");
      const { path, cleanup: cleanupFile } = await buildParquet(
        rows,
        resultSchema,
        duckdbSchema,
      );
      bytes = createReadStream(path);
      byteLen = (await fs.stat(path)).size;
      cleanup = cleanupFile;
      break;
    }
    default:
      const _: never = body.fmt;
      throw new InternalServerError(`Unsupported format ${_}`);
  }

  if (
    body.overflow_results !== false &&
    RESPONSE_BUCKET_NAME &&
    (byteLen >= RESPONSE_BUCKET_OVERFLOW_THRESHOLD ||
      body.overflow_results === true)
  ) {
    res.setHeader(BT_CURSOR_HEADER, cursor || "");
    res.setHeader("x-bt-count", `${rows.length}`);
    res.removeHeader("Content-Encoding");

    const overflowed = await saveFileToObjectStore({
      client: await makeObjectStore(),
      stream: bytes,
      isGzip: !!acceptsGzip,
      format: body.fmt,
      metadata: cursor ? { "bt-cursor": cursor } : undefined,
      sign: true,
    });

    res.redirect(
      // This 303 informs the client that they should switch to using GET. It's important
      // because you can POST /btql, so with just a plain 302, they'll try to POST the
      // overflow URL.
      303,
      overflowed,
    );
    cleanup();
  } else {
    bytes.pipe(res);
    res.on("finish", () => {
      cleanup();
    });
  }
}

export function writeRowsToStream(
  rows: DatabaseResponse,
  stream: NodeJS.WritableStream,
): Promise<string | undefined> {
  return new Promise((resolve, reject) => {
    let i = 0;

    function writeData() {
      try {
        let ok = true; // Flag to monitor if the buffer is handling more data well

        // Write each row until backpressure is signaled
        while (i < rows.length && ok) {
          const jsonString = JSON.stringify(rows[i]) + "\n";
          // Check if it's the last row
          if (i === rows.length - 1) {
            // End the stream with the last data piece
            stream.end(jsonString, "utf8");
          } else {
            // Otherwise, continue writing
            ok = stream.write(jsonString, "utf8");
          }

          i++; // Increment index to move to the next row
        }

        if (i === rows.length) {
          stream.end(); // End the stream if we're done writing all rows
        }

        if (i < rows.length) {
          // If we stopped because the buffer is full, wait for 'drain'
          stream.once("drain", writeData); // Resume writing when the stream is drained
        } else {
          // If we're done writing all rows, resolve the promise
          resolve(undefined);
        }
      } catch (e) {
        getLogger().error({ error: e }, "Failed to write rows to JSONL stream");
        reject(e);
      }
    }
    writeData();
  });
}

export type DatabaseResponse = Record<string, unknown>[];

export async function runBtql({
  body,
  objectCacheWasCachedToken,
  skipAclCheck,
  skipCustomColumns,
  dedupeScoresAndMetrics,
  appOrigin,
  ctxToken,
  setTraceIdHeader,
}: {
  body: Omit<BtqlRequest, "fmt" | "api_version">;
  objectCacheWasCachedToken?: string;
  skipAclCheck?: boolean;
  skipCustomColumns?: boolean;
  dedupeScoresAndMetrics?: boolean;
  appOrigin: string;
  ctxToken?: string;
  setTraceIdHeader?: (traceId: string) => void;
}): Promise<
  | {
      explain: unknown;
    }
  | {
      rows: Record<string, unknown>[];
      resultSchema: ResponseSchema;
      duckdbSchema?: Record<string, unknown>;
      cursor?: string;
      objectsByType?: Map<string, Record<string, ObjectCacheEntry>>; // set iff !skipAclCheck
      plan?: string;
      realtime_state?: RealtimeState;
      freshness_state?: FreshnessState;
    }
> {
  const pino = getLogger().child({
    task: "btql",
  });

  let query: ParsedQuery;
  if (typeof body.query === "string") {
    try {
      query = parseQuery(body.query);
    } catch (e) {
      throw new BadRequestError(`Invalid query: ${e}`);
    }
  } else {
    query = body.query;
  }

  const debug_useDuckDB = body._debug_use_duckdb;

  // We handle summary queries outside of brainstore, etc. and just convert this to
  // a trace query.
  let isSummary = false;
  if (query.from?.op === "function" && query.from.shape === "summary") {
    isSummary = true;

    // Also validate that this is a super-simple query
    if (
      !(
        query.select &&
        query.select.length === 1 &&
        "op" in query.select[0] &&
        query.select[0].op === "star"
      )
    ) {
      throw new BadRequestError(
        "Summary queries must be a single star projection",
      );
    }

    // No dimensions, pivot, measures, or unpivot
    if (query.dimensions || query.pivot || query.measures || query.unpivot) {
      throw new BadRequestError(
        "Summary queries cannot have dimensions, pivot, measures, or unpivot",
      );
    }
  }

  let ranBackfillCheck = false;
  const useBrainstore = await (async () => {
    const queryObjectIds = extractQueryObjectIds(query);
    const isBlocked =
      queryObjectIds &&
      queryObjectIds.objectIds.some((objectId) =>
        isBlockBackfillObject(queryObjectIds.objectType, objectId),
      );
    if (
      body.use_brainstore &&
      queryObjectIds &&
      brainstoreEnabled() &&
      shouldUseBrainstore(queryObjectIds.objectType) &&
      !body.audit_log &&
      !body.version &&
      !isBlocked
    ) {
      if (
        body.brainstore_skip_backfill_check ||
        brainstoreDefault() === "force" ||
        isSummary ||
        query.infer
      ) {
        return true;
      }
      ranBackfillCheck = true;
      return await checkBackfilledEnough({
        objectType: queryObjectIds.objectType,
        objectIds: queryObjectIds.objectIds,
        appOrigin,
        ctxToken,
        objectCacheWasCachedToken,
      });
    } else if (isBlocked) {
      throw new QueryTooCostlyError();
    } else {
      if (
        brainstoreDefault() === "force" &&
        queryObjectIds &&
        shouldUseBrainstore(queryObjectIds?.objectType) &&
        !body.audit_log
      ) {
        pino.warn(
          {
            queryObjectIds,
            brainstoreDefault: brainstoreDefault(),
            shouldUseBrainstore: shouldUseBrainstore(
              queryObjectIds?.objectType,
            ),
            bodyAuditLog: body.audit_log,
            bodyVersion: body.version,
            blocked: isBlocked,
          },
          "RUNPG",
        );
      }
      return false;
    }
  })();

  let remainingQuery: ParsedQuery;
  let pushedFilter: ParsedQuery;
  let tzOffset;
  let queryCursor: string | undefined = undefined;
  if (useBrainstore) {
    remainingQuery = { ...query };

    pushedFilter = {
      from: query.from,
      select: [{ op: "star" }],
    };
    tzOffset = body.tz_offset;
  } else if (debug_useDuckDB) {
    remainingQuery = {
      from: query.from,
      select: [{ op: "star" }],
    };
    pushedFilter = {
      from: query.from,
      select: [{ op: "star" }],
    };
    tzOffset = undefined; // Do this in the final query
  } else {
    const { filter, cursor, ...queryWithoutFilter } = query;

    remainingQuery = queryWithoutFilter;
    pushedFilter = {
      from: query.from,
      select: [{ op: "star" }],
      filter,
    };
    tzOffset = body.tz_offset;
    queryCursor = cursor ?? undefined;
  }

  const bindSchema = body.audit_log
    ? BRAINTRUST_AUDIT_LOG_LOGICAL_SCHEMA
    : body._testing_only_use_extended_schema
      ? BRAINTRUST_LOGICAL_SCHEMA_EXTENDED_FOR_TESTING
      : BRAINTRUST_LOGICAL_SCHEMA;

  const boundFrom = wrapBindError(() =>
    bindQuery({
      query: {
        from: remainingQuery.from,
        select: [{ op: "star" }],
      },
      schema: bindSchema,
      queryText: typeof body.query === "string" ? body.query : undefined,
      applyComputedFields,
      skipFieldCasts: useBrainstore,
    }),
  );

  const objectTypeName = boundFrom.from?.name;
  if (!objectTypeName) {
    throw new BadRequestError("No object type provided");
  }

  const objectTypeP = objectTypeSchema.safeParse(objectTypeName);
  if (!objectTypeP.success) {
    throw new BadRequestError(`Invalid object type ${objectTypeName}`);
  }
  const objectType = objectTypeP.data;

  if (body.version && !isVersionQuerySupported(objectType)) {
    throw new BadRequestError(
      `Version queries are not supported for object type ${objectType}`,
    );
  }

  const objectIds = boundFrom.from?.objects;
  if (!objectIds) {
    throw new BadRequestError("No object ids provided");
  }

  let objectsByType: Map<string, Record<string, ObjectCacheEntry>> | undefined =
    undefined;
  const { aclObjectType, overrideRestrictObjectType } =
    objectTypeToAclObjectType(objectType);
  if (!skipAclCheck) {
    const objects = await OBJECT_CACHE.checkAndGetMulti({
      appOrigin: appOrigin,
      authToken:
        wrapZodError(() => z.string().nullish().parse(ctxToken)) ?? undefined,
      aclObjectType,
      overrideRestrictObjectType,
      objectIds,
      // If we already checked whether the object is backfilled, we don't want to populate the cache
      // token because this request will be cached.
      wasCachedToken: ranBackfillCheck ? undefined : objectCacheWasCachedToken,
    });
    for (const id of objectIds) {
      const o = objects[id];
      if (!o || !o.permissions.includes("read")) {
        throw new AccessDeniedError({
          permission: "read",
          aclObjectType,
          overrideRestrictObjectType,
          objectId: id,
        });
      }
    }

    objectsByType = new Map<string, Record<string, ObjectCacheEntry>>();
    objectsByType.set(
      JSON.stringify(
        { aclObjectType, overrideRestrictObjectType },
        deterministicReplacer,
      ),
      objects,
    );
  }

  let orgName: string | undefined = undefined;
  let projectName: string | undefined = undefined;
  let projectId: string | undefined = undefined;
  let orgId: string | undefined = undefined;
  let summaryObjectInfo:
    | {
        objectType: AclObjectType;
        objectId: string;
      }
    | undefined = undefined;
  let queryObjectType: ObjectType | undefined = undefined;
  if (isSummary) {
    if (skipAclCheck) {
      throw new InternalServerError("Cannot summarize data without ACL checks");
    }

    // Update rows and resultSchema to be the summarized version of the data
    if (!objectsByType || Array.from(objectsByType.values()).length !== 1) {
      throw new BadRequestError(
        "Summary data is only supported for a single object type",
      );
    }
    const objectIdInfo = Array.from(objectsByType.values())[0];
    if (Object.keys(objectIdInfo).length !== 1) {
      throw new BadRequestError(
        "Summary data is only supported for a single object",
      );
    }

    const objectInfo = Object.values(objectIdInfo)[0];
    const organization = objectInfo.parent_cols.get("organization");
    const project = objectInfo.parent_cols.get("project");
    if (organization) {
      orgName = organization.name;
      orgId = organization.id;
    }
    if (project) {
      projectName = project.name;
      projectId = project.id;
    }
    summaryObjectInfo = {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      objectType: objectInfo.acl_object_type as AclObjectType,
      objectId: objectInfo.object_id,
    };

    const queryObjectIds = extractQueryObjectIds(query);
    queryObjectType = queryObjectIds?.objectType;
  } else if (objectsByType) {
    const orgIds = new Set<string>();
    const projectIds = new Set<string>();
    for (const objectInfoRecord of Array.from(objectsByType.values())) {
      for (const objectInfo of Object.values(objectInfoRecord)) {
        const organization = objectInfo.parent_cols.get("organization");
        if (organization?.id) {
          orgIds.add(organization.id);
        }

        const project = objectInfo.parent_cols.get("project");
        if (project?.id) {
          projectIds.add(project.id);
        } else if (objectInfo.acl_object_type === "project") {
          projectIds.add(objectInfo.object_id);
        }
      }
    }

    if (orgIds.size === 1) {
      orgId = Array.from(orgIds)[0];
    }

    if (projectIds.size === 1) {
      projectId = Array.from(projectIds)[0];
    }
  }

  let modelCosts: ModelCostsMap | undefined = undefined;
  let scoreConfig: DiscriminatedProjectScore[] | undefined = undefined;
  let customColumns: AliasExpr[] = remainingQuery.custom_columns ?? [];
  if (useBrainstore && isSummary) {
    if (
      !orgName ||
      !projectId ||
      !projectName ||
      !summaryObjectInfo ||
      !queryObjectType
    ) {
      throw new InternalServerError(
        "Cannot summarize data without org and project",
      );
    }

    const [projectConfig, _projectScores, costs, _customColumns] =
      await Promise.all([
        fetchProject({ projectId, appOrigin, authToken: ctxToken }),
        fetchProjectScoreConfig({
          appOrigin,
          authToken: ctxToken,
          orgName,
          projectName,
          projectId,
        }),
        fetchModelCosts({
          appOrigin,
          authToken: ctxToken,
          orgName,
          projectId,
        }),
        fetchCustomColumns({
          appOrigin,
          authToken: ctxToken,
          projectId,
          ...(body.custom_column_scope
            ? {
                objectId: body.custom_column_scope.object_id,
                objectType: body.custom_column_scope.object_type,
                subtype: body.custom_column_scope.subtype || "",
                variant: !body.custom_column_scope.variant
                  ? body.custom_column_scope.subtype ||
                    body.custom_column_scope.object_type
                  : body.custom_column_scope.variant,
              }
            : {
                objectId: projectId,
                objectType: "project",
                subtype: summaryObjectInfo.objectType,
                variant: summaryObjectInfo.objectType,
              }),
        }),
      ]);

    const projectScores = _projectScores ?? [];

    if (!skipCustomColumns) {
      customColumns = customColumns.concat(
        _customColumns.map<AliasExpr>(({ name, expr }) => ({
          alias: name,
          expr: { btql: expr },
        })),
      );
    }

    const rootSchema = bindSchema.properties?.[queryObjectType];
    if (
      !rootSchema ||
      typeof rootSchema !== "object" ||
      !("items" in rootSchema)
    ) {
      throw new BadRequestError(`Object type ${queryObjectType} not an array`);
    }
    const objectSchema = rootSchema.items;
    if (!objectSchema) {
      throw new BadRequestError(`Object type ${queryObjectType} not found`);
    }

    modelCosts = costs;
    scoreConfig = projectScores;

    const aggregateScoreExprs = makeWeightedScoreExprs({
      scores: projectScores.filter((s) => isAggregateScore(s.score_type)),
    });
    remainingQuery.custom_columns = customColumns;
    remainingQuery.comparison_key = new Parser(
      projectConfig?.settings?.comparison_key ?? "input",
    ).parseExpr();
    remainingQuery.preview_length = query.preview_length;
    remainingQuery.weighted_scores = aggregateScoreExprs.map(
      ({ name, expr }) => ({ alias: name, expr }),
    );
  } else if (body.custom_column_scope) {
    if (!objectsByType || Array.from(objectsByType.values()).length !== 1) {
      throw new BadRequestError(
        "Summary data is only supported for a single object type",
      );
    }
    const objectIdInfo = Array.from(objectsByType.values())[0];
    if (Object.keys(objectIdInfo).length !== 1) {
      throw new BadRequestError(
        "Summary data is only supported for a single object",
      );
    }

    const objectInfo = Object.values(objectIdInfo)[0];
    const project = objectInfo.parent_cols.get("project");
    if (!project) {
      throw new BadRequestError(
        "Object must have a project for custom columns",
      );
    }
    projectId = project.id;
    customColumns = (
      await fetchCustomColumns({
        appOrigin,
        authToken: ctxToken,
        projectId,
        objectId: body.custom_column_scope?.object_id,
        objectType: body.custom_column_scope?.object_type,
        subtype:
          body.custom_column_scope?.subtype === null
            ? undefined
            : body.custom_column_scope?.subtype,
        variant: body.custom_column_scope?.variant,
      })
    ).map<AliasExpr>(({ name, expr }) => ({
      alias: name,
      expr: { btql: expr },
    }));
    remainingQuery.custom_columns = customColumns;
  }

  let boundQuery: BoundQuery;
  try {
    boundQuery = wrapBindError(() =>
      bindQuery({
        query: remainingQuery,
        schema: bindSchema,
        queryText: typeof body.query === "string" ? body.query : undefined,
        applyComputedFields,
        skipFieldCasts: useBrainstore,
      }),
    );
  } catch (e) {
    pino.error({ error: e }, "Invalid query");
    throw new BadRequestError(`Invalid query: ${e}`);
  }

  let boundFilter;
  try {
    boundFilter = wrapBindError(() =>
      bindQuery({
        query: pushedFilter,
        schema: BRAINTRUST_LOGICAL_SCHEMA, // Filters always run against the base table
        queryText: typeof body.query === "string" ? body.query : undefined,
        applyComputedFields,
      }),
    );
  } catch (e) {
    pino.error({ error: e }, "Invalid filter");
    throw new BadRequestError(`Invalid filter: ${e}`);
  }

  const tableType = body.audit_log ? "audit_log" : "main";

  if (debug_useDuckDB && tableType !== "main") {
    throw new BadRequestError("Audit log queries are not supported in duckdb");
  }

  const canPushLimit =
    body.force_push_limit ||
    // Group by
    (!(
      "dimensions" in boundQuery &&
      boundQuery.dimensions &&
      boundQuery.dimensions.length > 0
    ) &&
      !(
        "measures" in boundQuery &&
        boundQuery.measures &&
        boundQuery.measures.length > 0
      ) &&
      // We sort outside of the object scan, so we can't push the limit down
      (isEmpty(boundQuery.sort) || boundQuery.sort.length === 0));
  const skipLimitChecks =
    body._testing_only_skip_limit_checks && TESTING_ONLY_SKIP_LIMIT_CHECKS;

  if (isEmpty(boundQuery.limit) && !skipLimitChecks) {
    const sampleLimit =
      "sample" in boundQuery &&
      boundQuery.sample &&
      boundQuery.sample.method.type === "count"
        ? boundQuery.sample.method.value
        : undefined;

    // For project_logs queries where we are pushing the limit into the main
    // object scan, we ignore `disable_limit`.
    if (objectType === "project_logs" && canPushLimit) {
      boundQuery.limit = sampleLimit ?? DEFAULT_PROJECT_LOGS_LIMIT;
    } else if (!body.disable_limit) {
      boundQuery.limit = sampleLimit ?? DEFAULT_LIMIT;
    }
  }

  if (
    !isEmpty(boundQuery.limit) &&
    !skipLimitChecks &&
    // We intentionally ignore a limit of 0.
    MAX_LIMIT_FOR_QUERIES &&
    boundQuery.limit > MAX_LIMIT_FOR_QUERIES
  ) {
    throw new BadRequestError(`Limit must be <= ${MAX_LIMIT_FOR_QUERIES}`);
  }

  if (useBrainstore) {
    if (tableType !== "main") {
      // Should not happen because we explicitly check for audit log when
      // determining useBrainstore.
      throw new InternalServerError(
        "Brainstore queries are not supported in audit_log",
      );
    }
    const result: Awaited<ReturnType<typeof runBrainstoreQuery>> & {
      duckdbSchema?: Record<string, unknown>;
    } = await runBrainstoreQuery({
      query: boundQuery,
      realtime:
        !BRAINSTORE_DISABLE_REALTIME_QUERIES && !!body.brainstore_realtime,
      realtimeReadTimeoutMs: ["project_logs"].includes(objectType)
        ? BRAINSTORE_PROJECT_LOGS_REALTIME_READ_TIMEOUT_MS
        : undefined,
      explain: body._debug_explain
        ? "explain"
        : body.include_plan
          ? "include_plan"
          : null,
      tableType,
      appOrigin,
      ctxToken,
      inferenceDepth: body.inference_depth,
      setTraceIdHeader:
        setTraceIdHeader &&
        ((traceId) => {
          setTraceIdHeader(traceId);
        }),
      tzOffset,
      modelCosts,
      queryTimeoutSeconds: body.query_timeout_seconds,
      tracingMetadata: {
        "btql.org_id": orgId,
        "btql.project_id": projectId,
        "btql.object_type": objectType,
        "btql.object_id": objectIds[0],
        "btql.object_ids": objectIds.join(","),
        "btql.query": JSON.stringify(redactBtqlQuery(body.query)),
      },
      metricMetadata: {
        org_id: orgId,
        project_id: projectId,
        object_type: objectType,
        multi_object: objectIds.length > 1,
      },
    });

    if (isSummary && "rows" in result) {
      const { duckdbSchema } = modifyResultSchemaMakeDuckdbSchema({
        resultSchema: result.resultSchema,
        data: result.rows,
        scoreConfig: scoreConfig!,
        customColumns,
        dedupeScoresAndMetrics,
      });
      result.duckdbSchema = duckdbSchema;

      // Get the full set of row ids
      const retrievedRowIds = new Set<string>();
      result.rows.forEach((row) => {
        const rowId = rowWithIdSchema.safeParse(row);
        if (rowId.success) {
          retrievedRowIds.add(rowId.data.id);
        }
      });

      const comments = await fetchComments({
        objectType: queryObjectType!,
        objectId: summaryObjectInfo!.objectId,
        ids: retrievedRowIds,
      });

      if (comments) {
        result.rows.forEach((row) => {
          const rowId = z.string().safeParse(row.id);
          if (rowId.success) {
            const rowComments = comments[rowId.data];
            row.comments = rowComments;
          }
        });
      }
    }
    return {
      ...result,
      objectsByType,
    };
  } else if (
    !body.audit_log &&
    (objectIds.some((objectId) => canContainRowRefs(objectType, objectId)) ||
      objectIds.some((objectId) => canPgInsertLogs2(objectType, objectId)))
  ) {
    // If we are not using brainstore, then we can only run queries over objects
    // which cannot contain realtime refs or logs2. `audit_log` queries are
    // exempted because they don't require any ref data and can be merged across
    // multiple logs tables.
    throw new BadRequestError(
      `Cannot query ${objectType} objects ${objectIds.join(", ")} over a single data backend. Consider splitting your query into multiple requests.`,
    );
  }

  const isBlockBackfill = objectIds.some((objectId) =>
    isBlockBackfillObject(objectType, objectId),
  );

  if (body.version || body.audit_log) {
    if (
      !isEmpty(body.expected_cost) &&
      body.expected_cost > 1 &&
      !useBrainstore
    ) {
      throw new QueryTooCostlyError();
    }
  }

  const filterSchema = PHYSICAL_SCHEMA.postgres.main;

  const cursorParams = queryCursor ? parseCursor(queryCursor) : null;

  const useMatch = body.use_match_search_index;

  let sequenceIdFilter: ToSQL | undefined = undefined;
  const matchString =
    useMatch && boundFilter?.filter ? findMatchExpr(boundFilter?.filter) : null;
  if (matchString) {
    const objectIdValue = genSingleObjectIdValue({
      objectType,
      objectIds,
    });

    const query = sql`
          SELECT DISTINCT sequence_id
            FROM logs_search_index_composite
           WHERE object_id = ${objectIdValue}
             AND snippet @@ websearch_to_tsquery('english', ${matchString})
           ORDER BY sequence_id DESC LIMIT ${snippet(`${SEARCH_PAGE_SIZE}`)}
      `;

    const start = Date.now();
    const results = await executePG(query);
    const sequenceIds = results.map((r) => r.sequence_id);
    pino.info(
      {
        sequenceIdsLength: sequenceIds.length,
        matchString,
        time: Date.now() - start,
      },
      "Found sequence ids (match)",
    );

    sequenceIdFilter =
      sequenceIds.length > 0
        ? snippet(`sequence_id IN (${sequenceIds.join(",")})`)
        : snippet("false");
  }

  boundFilter = addCoercions(boundFilter);

  if (
    !useBrainstore &&
    boundFilter?.filter &&
    hasMatchExpr(boundFilter.filter)
  ) {
    throw new BadRequestError(
      "Match search is not supported in non-Brainstore queries",
    );
  }

  if (!useBrainstore && body.audit_log) {
    if (boundFilter?.filter) {
      const SUPPORTED_FIELD_EXPRS = ["id"];
      const field = hasUnsupportedFieldExpr(
        boundFilter.filter,
        SUPPORTED_FIELD_EXPRS,
      );
      if (field) {
        throw new BadRequestError(
          `Unsupported filter field in audit log query: ${field}`,
        );
      }
    }
    if (cursorParams) {
      throw new BadRequestError("Audit log queries do not support cursors");
    }

    if (
      !(
        // For testing, we allow queries over the full audit log table if the
        // user specifies and the data plane is configured to allow this.
        (
          (body._testing_only_allow_query_full_audit_log &&
            TESTING_ONLY_ALLOW_QUERY_FULL_AUDIT_LOG) ||
          // We also allow queries over the full audit log table unless the object
          // type is one of the following.
          !["project_logs", "dataset", "playground_logs"].includes(
            objectType,
          ) ||
          // Otherwise, we require the query to filter by id.
          (boundFilter.filter && hasIdEqualityExpr(boundFilter.filter))
        )
      )
    ) {
      // Audit log queries must filter by id, otherwise we return an empty
      // result. But we bypass this check if the user specifies
      // `_testing_only_allow_query_full_audit_log` and the data plane is
      // configured to allow this.
      boundFilter.filter = { op: "literal", value: false, type: "boolean" };
    }

    // Audit log queries do not support pagination.
    boundQuery.limit = undefined;
  }

  const finalSchema = PHYSICAL_SCHEMA.postgres[tableType];

  const usedFields = collectPhysicalFields(boundQuery, finalSchema);

  function makeBaseQuery({
    pgLogsTableName,
    objectIds,
    tzOffset,
    boundFilterFilter,
  }: {
    pgLogsTableName: string;
    objectIds: string[];
    tzOffset: number | undefined;
    boundFilterFilter: BoundExpr | null | undefined;
  }) {
    const filterCtx = makeExprContext(filterSchema, boundQuery, {
      tzOffset,
    });

    if (filterCtx.table?.alias) {
      // While pushing the filter down, the table alias is "logs", not the logical table name.
      filterCtx.table.alias = pgLogsTableName;
    }

    // If it's a blocked object, do not run any filters against Postgres
    const plannedFilter =
      boundFilterFilter && !isBlockBackfill
        ? planExpr(filterCtx, boundFilterFilter)
        : undefined;

    return scanObjectsQuery({
      pgLogsTableName,
      objectType,
      objectIds,
      version: body.version,
      keepJsonb: true,
      auditLog: body.audit_log,
      search: {
        filter: plannedFilter,
        sequenceIdFilter,
        limit: canPushLimit ? (boundQuery.limit ?? undefined) : undefined,
        ...cursorParams,
      },
      usedFields,
      relaxedSearchMode: body.relaxed_search_mode,
    });
  }

  const baseQuery = makeBaseQuery({
    pgLogsTableName: PG_LOGS_TABLE,
    objectIds,
    tzOffset,
    boundFilterFilter: boundFilter?.filter,
  });

  // Capture the result schema before we modify the query (eg to add pagination columns),
  // so that the schema we return and use to generate parquet files is correct.
  if (canPushLimit) {
    // Do not apply the limit again in the final query, because we may truncate spans/traces
    boundQuery.limit = undefined;
    // But also add the pagination columns to the select list.
    if ("select" in boundQuery) {
      boundQuery.select.push(
        {
          alias: PAGINATION_CURSOR_XACT_ID_FIELD,
          expr: {
            op: "field",
            name: [PAGINATION_CURSOR_XACT_ID_FIELD],
            type: {
              type: "string",
            },
          },
        },
        {
          alias: PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD,
          expr: {
            op: "field",
            name: [PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD],
            type: {
              type: "string",
            },
          },
        },
      );
    }
  }

  function runPlanQuery(baseQuery: ToSQL, objectTypeName: string) {
    return planQuery(finalSchema, boundQuery, {
      tableExprs: {
        [objectTypeName]: sql`(${baseQuery})`,
      },
      tzOffset: body.tz_offset,
      parseJSONFields: false,
      strictValidate: STRICT_VALIDATION_MODE,
      inferenceDepth: body.inference_depth,
    });
  }

  const {
    sql: finalQuery,
    schema: initialResultSchema,
    postProcess: initialPostProcessor,
  } = runPlanQuery(baseQuery, objectTypeName);
  let resultSchema = initialResultSchema;
  let postProcess = initialPostProcessor; // XXX cleanup

  if (body._debug_explain) {
    const explainResult = await executePG(
      sql`EXPLAIN (FORMAT JSON) ${finalQuery}`,
    );
    return { explain: explainResult };
  }

  // NOTE: As we add pivot support, we'll probably want to have a more sophisticated post-processor
  // or even move the interpreter part into the handler, but for now, we'll just process the schema.
  let rows: DatabaseResponse;
  // const start = Date.now();
  try {
    // If we are running an audit log query, we run the same query across both
    // tables and concatenate the results.
    if (body.audit_log) {
      const baseQuery2 = makeBaseQuery({
        pgLogsTableName: PG_LOGS2_TABLE,
        objectIds,
        tzOffset,
        boundFilterFilter: boundFilter?.filter,
      });
      const { sql: finalQuery2 } = runPlanQuery(baseQuery2, objectTypeName);
      const [rows1, rows2] = await Promise.all([
        executePG(finalQuery),
        executePG(finalQuery2),
      ]);
      rows = [...rows1, ...rows2];
    } else {
      rows = await executePG(finalQuery);
    }
  } catch (e) {
    pino.error({ error: e }, "Failed to execute query");
    throw e;
  }

  if (debug_useDuckDB) {
    const ret = await runDuckDBQuery({
      tz_offset: body.tz_offset,
      scanObjectsQuery: boundFilter,
      boundQuery: wrapBindError(() =>
        bindQuery({
          query,
          schema: body.audit_log
            ? BRAINTRUST_AUDIT_LOG_LOGICAL_SCHEMA
            : BRAINTRUST_LOGICAL_SCHEMA,
          queryText: typeof body.query === "string" ? body.query : undefined,
          applyComputedFields,
        }),
      ),
      rows,
      inferenceDepth: body.inference_depth,
    });
    rows = ret.results;
    postProcess = ret.postProcess;
    resultSchema = ret.schema;
  }

  const processedRows = [];
  for await (const row of postProcess(rowIterator(rows))) {
    processedRows.push(row);
  }
  rows = processedRows;

  const cursorValue = findCursor(rows);
  const cursor = cursorValue ? formatCursor(cursorValue) : undefined;
  rows = rows.map(cleanRow);
  delete resultSchema.items.properties[PAGINATION_CURSOR_XACT_ID_FIELD];
  delete resultSchema.items.properties[PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD];

  let duckdbSchema: Record<string, unknown> | undefined = undefined;
  if (isSummary) {
    if (!summaryObjectInfo) {
      throw new InternalServerError("Summary object info is required");
    }
    const updatedResult = await createSummaryData({
      rows: rows,
      resultSchema: resultSchema,
      orgName: orgName!,
      projectName: projectName!,
      projectId: projectId!,
      appOrigin,
      authToken: ctxToken,
      ...summaryObjectInfo,
    });
    rows = updatedResult.rows;
    resultSchema = updatedResult.resultSchema;
    duckdbSchema = updatedResult.duckdbSchema;
  }

  return {
    rows,
    resultSchema,
    cursor,
    objectsByType,
    duckdbSchema,
  };
}

export async function executePG(query: ToSQL): Promise<DatabaseResponse> {
  const pg = getPG();
  const { query: queryText, params } = query.toNumericParamQuery();

  const { rows } = await pg.query(queryText, params);
  return rows;
}

export async function buildParquet(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  rows: any[],
  schema: ResponseSchema,
  duckdbSchema?: Record<string, unknown>,
): Promise<{
  path: string;
  cleanup: () => void;
}> {
  const { path: jsonPath, cleanupCallback: cleanupJson } = await createTempFile(
    {
      postfix: ".json",
    },
  );

  const { path: parquetPath, cleanupCallback: cleanupParquet } =
    await createTempFile({
      postfix: ".parquet",
    });

  try {
    const writeStream = createWriteStream(jsonPath);
    try {
      await writeRowsToStream(rows, writeStream);
    } finally {
      writeStream.close();
    }

    // By waiting for the stream to close, we're ensuring that the file
    // is fully written and flushed to disk.
    await new Promise((resolve, reject) => {
      writeStream.on("finish", resolve);
      writeStream.on("error", reject);
    });

    // This is a sad attempt at producing this schema format: https://duckdb.org/docs/data/json/overview.html#loading-json
    duckdbSchema = duckdbSchema ?? createDuckdbJsonSchema(schema);
    const singleQuotedSchema =
      "{" +
      Object.entries(duckdbSchema)
        .map(([k, v]) => sql`${k}: ${v}`.toPlainStringQuery())
        .join(", ") +
      "}";

    const query = sql`
            COPY
            (
                SELECT *
                FROM (
                  SELECT * FROM read_ndjson(
                    ${jsonPath},
                    columns=${snippet(singleQuotedSchema)}
                  )
                )
            )
            TO ${parquetPath} (FORMAT PARQUET, COMPRESSION 'ZSTD')
      `;

    await duckq(getDuckDBConn(), query.toPlainStringQuery());
    return {
      path: parquetPath,
      cleanup: cleanupParquet,
    };
  } catch (e) {
    getLogger().error({ error: e }, "Failed to build parquet");
    throw e;
  } finally {
    cleanupJson();
  }
}

// Promise wrapper for creating a temporary file
function createTempFile({ postfix }: { postfix: string }): Promise<{
  path: string;
  fd: number;
  cleanupCallback: () => void;
}> {
  return new Promise((resolve, reject) => {
    tmp.file({ keep: true, postfix }, (err, path, fd, cleanupCallback) => {
      if (err) {
        reject(err);
      } else {
        resolve({ path, fd, cleanupCallback });
      }
    });
  });
}

export function createDuckdbJsonSchema(schema: ResponseSchema): {
  [key: string]: unknown;
} {
  const caseInsensitiveKeys = new Set<string>();
  return Object.fromEntries(
    Object.entries(schema.items.properties).reduce<[string, string][]>(
      (acc, [k, v]) => {
        const lowerCaseKey = k.toLowerCase();
        if (!caseInsensitiveKeys.has(lowerCaseKey)) {
          // DuckDB, but not JSON, is case-insensitive, so check for duplicates while keeping the original casing
          caseInsensitiveKeys.add(lowerCaseKey);
          const scalarType = weakestScalarType(v);
          switch (scalarType) {
            case "string":
            case "null":
              acc.push([k, "STRING"]);
              break;
            case "datetime":
              acc.push([k, "TIMESTAMP WITH TIME ZONE"]);
              break;
            case "date":
              acc.push([k, "DATE"]);
              break;
            case "number":
              acc.push([k, "DOUBLE"]);
              break;
            case "integer":
              acc.push([k, "BIGINT"]);
              break;
            case "boolean":
              acc.push([k, "BOOLEAN"]);
              break;
            case "object":
            case "array":
            case "unknown":
              acc.push([k, "JSON"]);
              break;
            case "interval":
              acc.push([k, "INTERVAL"]);
              break;
            default:
              throw new Error(`Unknown scalar type: ${scalarType}`);
          }
        }
        return acc;
      },
      [],
    ),
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
(BigInt.prototype as any).toJSON = function () {
  const strValue = this.toString();
  const numValue = parseInt(strValue);
  if (BigInt(numValue) === this) {
    return numValue;
  } else {
    return strValue;
  }
};

// This is for debug purposes only. We use it to test the DuckDB filters (and make sure
// they compile/result in the same thing).
async function runDuckDBQuery({
  tz_offset,
  scanObjectsQuery,
  boundQuery,
  rows,
  inferenceDepth,
}: {
  tz_offset: number | undefined;
  scanObjectsQuery: BoundQuery;
  boundQuery: BoundQuery;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  rows: any[];
  inferenceDepth: number | undefined;
}) {
  const scanObjectsSchema = getResultSchema(scanObjectsQuery);

  const { path: parquetPath, cleanup: cleanupParquet } = await buildParquet(
    rows,
    scanObjectsSchema,
  );

  // In the UI, scores/metrics are a map and struct, respectively, but not in the simplified
  // format we use to query in the tests.
  const duckdbSchemaWithScoresJSON: PhysicalSchema = {
    type: "duckdb",
    tables: {
      ...Object.fromEntries(
        Object.entries(DUCKDB_PHYSICAL_SCHEMA.tables).map(([k, v]) => [
          k,
          {
            columns: {
              ...v.columns,
              ...(v.columns.scores
                ? { scores: { path: ["scores"], type: { type: "json" } } }
                : {}),
              ...(v.columns.metrics
                ? { metrics: { path: ["metrics"], type: { type: "json" } } }
                : {}),
            },
          },
        ]),
      ),
    },
  };

  try {
    const {
      sql: finalQuery,
      postProcess,
      schema,
    } = planQuery(duckdbSchemaWithScoresJSON, boundQuery, {
      tableExprs: {
        [boundQuery.from!.name!]: sql`(
        SELECT
          * ${snippet(
            duckdbSchemaWithScoresJSON.tables[boundQuery.from!.name!].columns
              .scores
              ? `REPLACE (
            -- Given that this is used primarily for tests, converting to JSON here
            -- enables the "struct_map" type to still work.
            json(metrics) AS metrics,
            json(scores) AS scores
          )`
              : "",
          )}
        FROM read_parquet([${parquetPath}])
      )`,
      },
      tzOffset: tz_offset,
      parseJSONFields: true,
      inferenceDepth,
    });

    const conn = getDuckDBConn();
    const results = await duckq(conn, finalQuery.toPlainStringQuery());

    return { results, postProcess, schema };
  } finally {
    cleanupParquet();
  }
}

export async function saveFileToObjectStore({
  client,
  stream,
  isGzip,
  format,
  metadata,
  exportPath,
  sign,
}: {
  client: ObjectStore;
  stream: Readable;
  isGzip: boolean;
  format: z.infer<typeof requestSchema>["fmt"];
  metadata: Record<string, string> | undefined;
  exportPath?: string;
  sign?: boolean;
}) {
  let bucket = RESPONSE_BUCKET_NAME;
  let prefix = RESPONSE_BUCKET_PREFIX;
  const protocol = "s3";
  if (exportPath) {
    const url = parseObjectStoreURL(exportPath);
    if (url.protocol !== "s3") {
      throw new BadRequestError(
        `Invalid export path: ${exportPath}. Must be a valid S3 URL.`,
      );
    }
    bucket = url.bucket;
    prefix = url.key;
  }

  if (!bucket) {
    throw new Error(
      "Must either set a RESPONSE_BUCKET_NAME or provide an exportPath",
    );
  }

  let extension: string;
  let contentType: string;
  let contentEncoding: string | undefined;
  switch (format) {
    case "json":
      extension = "json" + (isGzip ? ".gz" : "");
      contentEncoding = isGzip ? "gzip" : undefined;
      contentType = "application/json";
      break;
    case "jsonl":
      extension = "jsonl" + (isGzip ? ".gz" : "");
      contentEncoding = isGzip ? "gzip" : undefined;
      contentType = "application/json";
      break;
    case "parquet":
      extension = "parquet";
      contentType = "application/octet-stream";
      break;
  }
  const key = _urljoin(
    prefix ?? "",
    new Date().toISOString().split("T")[0],
    crypto.randomUUID() + "." + extension,
  );

  await client.put({
    bucket,
    key,
    body: stream,
    metadata,
    contentType,
    contentEncoding,
  });

  getLogger().debug({ key }, "Saved response to object storage.");

  if (sign) {
    return await client.signedGetUrl({
      bucket,
      key,
      expiresIn: 3600,
    });
  } else {
    return `${protocol}://${bucket}/${key}`;
  }
}

type Cursor = {
  max_xact_id: string;
  max_root_span_id: string;
};

function findCursor(rows: DatabaseResponse): Cursor | null {
  // We have to pick the maximum cursor out of all rows, because the rows may be
  // concatenated over queries from multiple tables.
  let ret: Cursor | null = null;
  for (const row of rows) {
    if (
      row[PAGINATION_CURSOR_XACT_ID_FIELD] &&
      row[PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD]
    ) {
      const cursor = {
        max_xact_id: z.string().parse(row[PAGINATION_CURSOR_XACT_ID_FIELD]),
        max_root_span_id: z
          .string()
          .parse(row[PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD]),
      };
      if (
        !ret ||
        cursor.max_xact_id > ret.max_xact_id ||
        (cursor.max_xact_id === ret.max_xact_id &&
          cursor.max_root_span_id > ret.max_root_span_id)
      ) {
        ret = cursor;
      }
    }
  }
  return ret;
}

export function formatCursor(cursor: Cursor) {
  const xactId = BigInt(cursor.max_xact_id);
  const xactIdBuffer = bigIntToBuffer(xactId);
  if (xactIdBuffer.length !== 8) {
    throw new Error(`Invalid transaction ID ${xactId}`);
  }

  const rootSpanBuffer = Buffer.from(cursor.max_root_span_id, "utf-8");

  // The first 8 bytes are the transaction ID The remaining bytes are the root
  // span ID.
  const finalValue = Buffer.concat([xactIdBuffer, rootSpanBuffer]).toString(
    "base64",
  );
  return finalValue;
}

export function parseCursor(cursor: string): {
  max_xact_id: string;
  max_root_span_id: string;
} {
  const rawBytes = Buffer.from(cursor, "base64");
  return {
    max_xact_id: bufferToBigInt(rawBytes.subarray(0, 8)).toString(),
    max_root_span_id: rawBytes.subarray(8).toString("utf-8"),
  };
}

function bigIntToBuffer(bigInt: bigint) {
  // Determine the byte size
  const byteSize = Math.ceil(bigInt.toString(16).length / 2);
  const buffer = Buffer.alloc(byteSize);

  // Write BigInt to buffer (as Big Endian)
  try {
    buffer.writeBigInt64BE(bigInt, 0);
  } catch (e) {
    throw new Error(
      `Failed to write BigInt ${bigInt} to buffer: ${JSON.stringify(e)}`,
    );
  }

  return buffer;
}

function bufferToBigInt(buffer: Buffer) {
  return buffer.readBigInt64BE(0);
}

// Should be called AFTER obtaining the pagination cursor.
function cleanRow(row: Record<string, unknown>) {
  const {
    [PAGINATION_CURSOR_XACT_ID_FIELD]: _0,
    [PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD]: _1,
    ...rest
  } = row;
  return { ...rest };
}

function collectPhysicalFields(query: BoundQuery, schema: PhysicalSchema) {
  const usedFields = new Set<string>();
  if (!query.from || !query.from.name) {
    return usedFields;
  }
  const table = schema.tables[query.from.name];
  const handleFn = (expr: BoundExpr) => {
    if (expr.op === "field") {
      const first = expr.name[0];
      if (first in table.columns) {
        usedFields.add(table.columns[first].path[0]);
      }
      return false;
    } else {
      return true;
    }
  };

  traverseQuery(query, handleFn);
  return usedFields;
}

function findMatchExpr(e: BoundExpr): string | null {
  let match: string | null = null;
  let inferredMatch: string | null = null;

  traverseExpr(e, (expr: BoundExpr) => {
    switch (expr.op) {
      case "and":
        return true;
      case "match":
        if (
          expr.left.op !== "field" ||
          expr.right.op !== "literal" ||
          !expr.right.value ||
          typeof expr.right.value !== "string"
        ) {
          throw new BadRequestError(
            "match RHS must be a non-empty literal string",
          );
        }
        if (match !== null) {
          throw new BadRequestError("only one match expression allowed");
        }
        match = expr.right.value;
        break;
      case "like":
      case "ilike":
        if (
          inferredMatch !== null ||
          expr.left.op !== "field" ||
          expr.right.op !== "literal" ||
          !expr.right.value ||
          typeof expr.right.value !== "string"
        ) {
          break;
        }
        let s = expr.right.value;
        if (s.startsWith("%")) {
          s = s.slice(1);
        }
        if (s.endsWith("%")) {
          s = s.slice(0, -1);
        }
        if (s.length > 0 && !s.includes("%")) {
          inferredMatch = s;
        }
    }
    return false;
  });

  return match ?? inferredMatch;
}

function hasMatchExpr(e: BoundExpr): boolean {
  let found = false;
  traverseExpr(e, (expr) => {
    if (expr.op === "match") {
      found = true;
      return false;
    }
    return true;
  });
  return found;
}

function hasIdEqualityExpr(e: BoundExpr): boolean {
  let found = false;
  traverseExpr(e, (expr) => {
    if (
      expr.op === "eq" &&
      expr.left.op === "field" &&
      expr.right.op === "literal" &&
      expr.left.name.length === 1 &&
      expr.left.name[0] === "id"
    ) {
      found = true;
      return false;
    }
    return true;
  });
  return found;
}

function hasUnsupportedFieldExpr(
  e: BoundExpr,
  supportedFieldExprs: (string | number)[],
): string | number | null {
  let found: string | number | null = null;
  traverseExpr(e, (expr) => {
    if (
      expr.op === "field" &&
      expr.name.length &&
      !supportedFieldExprs.includes(expr.name[0])
    ) {
      found = expr.name[0];
      return false;
    }
    return true;
  });
  return found;
}

export async function* rowIterator(rows: DatabaseResponse) {
  for (const row of rows) {
    yield row;
  }
}

function extractQueryObjectIds(
  query: ParsedQuery,
): { objectType: ObjectType; objectIds: string[] } | null {
  if (query.from?.op !== "function") {
    return null;
  }
  const name = query.from.name.name[0];
  const objectTypeParsed = objectTypeSchema.safeParse(name);
  if (!objectTypeParsed.success) {
    return null;
  }
  const objectType = objectTypeParsed.data;

  const objectIds = query.from.args
    .map((arg) =>
      arg.op === "literal"
        ? wrapZodError(() => z.string().parse(arg.value), {
            errmsg: "Invalid object ID in `from:` clause",
          })
        : null,
    )
    .filter((v) => v !== null);
  return { objectType, objectIds };
}

async function checkBackfilledEnough({
  objectType,
  objectIds,
  appOrigin,
  ctxToken,
  objectCacheWasCachedToken,
}: {
  objectType: ObjectType;
  objectIds: string[];
  appOrigin: string;
  ctxToken?: string;
  objectCacheWasCachedToken?: string;
}) {
  // This is technically duplicated with the ACL check we do a bit later in the
  // btql endpoint, but the checks are cached, so it should be fine.
  const { aclObjectType, overrideRestrictObjectType } =
    objectTypeToAclObjectType(objectType);
  const objects = await OBJECT_CACHE.checkAndGetMulti({
    appOrigin,
    authToken:
      wrapZodError(() => z.string().nullish().parse(ctxToken)) ?? undefined,
    aclObjectType,
    overrideRestrictObjectType,
    objectIds,
    wasCachedToken: objectCacheWasCachedToken,
  });

  const projectIds = new Set<string>();
  for (const object of Object.values(objects)) {
    if (object.acl_object_type === "project") {
      projectIds.add(object.object_id);
    } else {
      const project = object.parent_cols.get("project");
      if (!project) {
        throw new InternalServerError(
          `Object ${object.object_id} of type ${object.acl_object_type} has no project`,
        );
      }
      projectIds.add(project.id);
    }
  }

  const statuses = await getTrackedObjectStatus({
    projectIds: Array.from(projectIds),
    objectType,
  });

  const statusProjectIds = new Set<string>();
  for (const s of statuses) {
    statusProjectIds.add(s.project_id);
  }

  if (statusProjectIds.size !== projectIds.size) {
    return false;
  }

  return statuses.every(
    (s) => s.enabled && s.completed_initial_backfill_ts !== null,
  );
}

const rowWithIdSchema = z.object({
  id: z.string(),
});
