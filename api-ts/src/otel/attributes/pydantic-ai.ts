/**
 * This file implements parsing for pydantic-ai OTEL telemetry.
 * Pydantic-ai stores conversation data in the `all_messages_events` attribute
 * as a JSON string containing the full conversation flow.
 */

import { z } from "zod";
import { get } from "lodash";
import { isEmpty } from "braintrust/util";
import { Message } from "@braintrust/typespecs";
import { MessageField, SpanSpec, notHandled, handled } from "./attributes";
import { translateGenAIMessages } from "./gen-ai";

export const pydanticAISpanSpec: SpanSpec = {
  input: (attributes) => {
    const result = parsePydanticAIAllMessages(attributes, "input");
    if (result !== undefined) {
      return handled(result);
    }

    // Handle tool execution spans - use tool_arguments as input
    const toolName = get(attributes, "gen_ai.tool.name");
    const toolArguments = get(attributes, "tool_arguments");
    const logfireMsg = get(attributes, "logfire.msg");

    if (
      toolName &&
      toolArguments &&
      typeof logfireMsg === "string" &&
      String(logfireMsg).startsWith("running tool:")
    ) {
      // Try to parse tool_arguments as JSON, fallback to string
      let parsedArguments: unknown = toolArguments;
      if (typeof toolArguments === "string") {
        try {
          parsedArguments = JSON.parse(toolArguments);
        } catch {
          // Keep as string if not valid JSON
          parsedArguments = toolArguments;
        }
      }

      return handled(
        {
          isLLM: false,
          data: parsedArguments,
        },
        ["tool_arguments"],
      );
    }

    // Handle conversation spans with all_messages_events
    const conversationData = parsePydanticAIConversation(attributes);
    if (conversationData) {
      const { inputMessages } = conversationData;
      if (inputMessages.length > 0) {
        return handled(
          {
            isLLM: true,
            data: inputMessages,
          },
          ["all_messages_events"],
        );
      }
    }

    // Handle chat spans with events in attributes (gen_ai.operation.name: "chat")
    const chatData = parsePydanticAIChatEvents(attributes);
    if (chatData) {
      const { inputMessages } = chatData;
      if (inputMessages.length > 0) {
        return handled(
          {
            isLLM: true,
            data: inputMessages,
          },
          ["events"],
        );
      }
    }

    return notHandled;
  },
  output: (attributes) => {
    const result = parsePydanticAIAllMessages(attributes, "output");
    if (result !== undefined) {
      return handled(result);
    }

    // Handle tool execution spans - use tool_response as output
    const toolName = get(attributes, "gen_ai.tool.name");
    const toolResponse = get(attributes, "tool_response");
    const logfireMsg = get(attributes, "logfire.msg");

    if (
      toolName &&
      toolResponse &&
      typeof logfireMsg === "string" &&
      String(logfireMsg).startsWith("running tool:")
    ) {
      // Try to parse tool_response as JSON, fallback to string
      let parsedResponse: unknown = toolResponse;
      if (typeof toolResponse === "string") {
        try {
          parsedResponse = JSON.parse(toolResponse);
        } catch {
          // Keep as string if not valid JSON
          parsedResponse = toolResponse;
        }
      }

      return handled(
        {
          isLLM: false,
          data: parsedResponse,
        },
        ["tool_response"],
      );
    }

    // Handle conversation spans with all_messages_events
    const conversationData = parsePydanticAIConversation(attributes);
    if (conversationData) {
      const { outputMessages } = conversationData;
      if (outputMessages.length > 0) {
        return handled(
          {
            isLLM: true,
            data: outputMessages,
          },
          ["all_messages_events"],
        );
      }
    }

    // Handle chat spans with events in attributes (gen_ai.operation.name: "chat")
    const chatData = parsePydanticAIChatEvents(attributes);
    if (chatData) {
      const { outputMessages } = chatData;
      if (outputMessages.length > 0) {
        return handled(
          {
            isLLM: true,
            data: outputMessages,
          },
          ["events"],
        );
      }
    }

    return notHandled;
  },
  span_attributes: (attributes) => {
    const allMessagesEvents = get(attributes, "all_messages_events");
    const logfireMsg = get(attributes, "logfire.msg");

    // Check if this is a pydantic-ai agent run span
    if (allMessagesEvents) {
      return handled({ type: "llm" });
    }

    // Check for tool execution spans with pydantic-ai logfire pattern
    const toolName = get(attributes, "gen_ai.tool.name");
    if (
      toolName &&
      typeof logfireMsg === "string" &&
      String(logfireMsg).startsWith("running tool:")
    ) {
      return handled({
        type: "tool",
        name: String(logfireMsg),
      });
    }

    return notHandled;
  },
  metadata: (attributes) => {
    const result: Record<string, unknown> = {};

    // Add model name if available
    const modelName = get(attributes, "model_name");
    if (modelName && typeof modelName === "string") {
      result.model = modelName;
    }

    // Parse model_request_parameters for tool definitions
    const modelRequestParams = get(attributes, "model_request_parameters");
    if (typeof modelRequestParams === "string") {
      try {
        const parsed = JSON.parse(modelRequestParams);
        if (parsed.function_tools && Array.isArray(parsed.function_tools)) {
          const tools = parsed.function_tools
            .filter(isToolObject)
            .map((toolObj: ToolObject) => ({
              type: "function",
              function: {
                name: toolObj.name,
                description: toolObj.description || `Tool: ${toolObj.name}`,
                parameters: toolObj.parameters_json_schema || {
                  type: "object",
                  properties: {},
                  required: [],
                },
              },
            }));
          if (tools.length > 0) {
            result.tools = tools;
          }
        }
      } catch {
        // Ignore parsing errors
      }
    }

    if (Object.keys(result).length === 0) {
      return notHandled;
    }
    return handled(result);
  },
};

// Schema for parsing pydantic-ai message events
const pydanticAIMessageEventSchema = z.object({
  role: z.enum(["system", "user", "assistant", "tool"]).optional(),
  content: z.union([z.string(), z.array(z.string())]).optional(),
  tool_calls: z
    .array(
      z.object({
        id: z.string(),
        type: z.string(),
        function: z.object({
          name: z.string(),
          arguments: z.string(),
        }),
      }),
    )
    .optional(),
  id: z.string().optional(),
  name: z.string().optional(),
  "gen_ai.message.index": z.number().optional(),
  "event.name": z.string().optional(),
  "gen_ai.system": z.string().optional(),
  index: z.number().optional(),
  message: z.unknown().optional(), // For gen_ai.choice events
});

interface ConversationData {
  inputMessages: Message[];
  outputMessages: Message[];
}

interface ToolObject {
  name: string;
  description?: string;
  parameters_json_schema?: unknown;
}

interface ToolCall {
  id: string;
  function: {
    name: string;
    arguments: string;
  };
}

interface MessageObject {
  tool_calls?: unknown[];
  content?: unknown;
}

function isRecord(obj: unknown): obj is Record<string, unknown> {
  return typeof obj === "object" && obj !== null;
}

function isToolObject(obj: unknown): obj is ToolObject {
  if (!isRecord(obj)) {
    return false;
  }
  return "name" in obj && typeof obj.name === "string";
}

function isToolCall(obj: unknown): obj is ToolCall {
  if (!isRecord(obj)) {
    return false;
  }
  if (!("id" in obj) || typeof obj.id !== "string") {
    return false;
  }
  if (!("function" in obj) || !isRecord(obj.function)) {
    return false;
  }
  return (
    "name" in obj.function &&
    typeof obj.function.name === "string" &&
    "arguments" in obj.function &&
    typeof obj.function.arguments === "string"
  );
}

function isMessageObject(obj: unknown): obj is MessageObject {
  return typeof obj === "object" && obj !== null;
}

function parsePydanticAIConversation(
  attributes: unknown,
): ConversationData | null {
  const allMessagesEventsRaw = get(attributes, "all_messages_events");

  if (!allMessagesEventsRaw) {
    return null;
  }

  let messagesArray: unknown;

  // Handle both string (JSON) and already-parsed array formats
  if (typeof allMessagesEventsRaw === "string") {
    try {
      messagesArray = JSON.parse(allMessagesEventsRaw);
    } catch {
      return null;
    }
  } else if (Array.isArray(allMessagesEventsRaw)) {
    messagesArray = allMessagesEventsRaw;
  } else {
    return null;
  }

  if (!Array.isArray(messagesArray)) {
    return null;
  }

  try {
    const messages = messagesArray.map((msg) =>
      pydanticAIMessageEventSchema.parse(msg),
    );

    // Sort by gen_ai.message.index to preserve chronological order
    const sortedMessages = messages.sort((a, b) => {
      const indexA = a["gen_ai.message.index"] ?? 0;
      const indexB = b["gen_ai.message.index"] ?? 0;
      return indexA - indexB;
    });

    const inputMessages: Message[] = [];
    const outputMessages: Message[] = [];

    for (const msg of sortedMessages) {
      if (msg.role === "system" && msg.content) {
        inputMessages.push({
          role: "system",
          content: Array.isArray(msg.content)
            ? msg.content.join(", ")
            : msg.content,
        });
      } else if (msg.role === "user" && msg.content) {
        inputMessages.push({
          role: "user",
          content: Array.isArray(msg.content)
            ? msg.content.join(", ")
            : msg.content,
        });
      } else if (msg.role === "assistant") {
        if (msg.tool_calls) {
          // Assistant message with tool calls goes in input (shows what the agent tried to do)
          inputMessages.push({
            role: "assistant",
            tool_calls: msg.tool_calls.map((tc) => ({
              id: tc.id,
              type: "function" as const,
              function: {
                name: tc.function.name,
                arguments: tc.function.arguments,
              },
            })),
          });
        } else if (msg.content) {
          // Regular assistant message - this is the final output
          outputMessages.push({
            role: "assistant",
            content: Array.isArray(msg.content)
              ? msg.content.join(", ")
              : msg.content,
          });
        }
      } else if (msg.role === "tool" && msg.content && msg.id) {
        // Tool responses go in input (shows what the tools returned)
        // Format content according to Braintrust typespecs
        let formattedContent;
        if (Array.isArray(msg.content)) {
          // If content is an array, join it into a single text block
          formattedContent = [
            {
              type: "text" as const,
              text: msg.content.join(", "),
            },
          ];
        } else {
          // If content is a string, wrap it in the proper format
          formattedContent = [
            {
              type: "text" as const,
              text: String(msg.content),
            },
          ];
        }

        inputMessages.push({
          role: "tool",
          content: formattedContent,
          tool_call_id: msg.id,
        });
      }
    }

    return {
      inputMessages,
      outputMessages,
    };
  } catch {
    return null;
  }
}

function parsePydanticAIChatEvents(
  attributes: unknown,
): ConversationData | null {
  // Check if this is a pydantic-ai chat span
  const operationName = get(attributes, "gen_ai.operation.name");
  if (operationName !== "chat") {
    return null;
  }

  const eventsRaw = get(attributes, "events");
  if (!eventsRaw || typeof eventsRaw !== "string") {
    return null;
  }

  try {
    const eventsArray = JSON.parse(eventsRaw);
    if (!Array.isArray(eventsArray)) {
      return null;
    }

    const messages = eventsArray.map((event) =>
      pydanticAIMessageEventSchema.parse(event),
    );

    const inputMessages: Message[] = [];
    const outputMessages: Message[] = [];

    for (const msg of messages) {
      if (msg["event.name"] === "gen_ai.system.message" && msg.content) {
        inputMessages.push({
          role: "system",
          content: Array.isArray(msg.content)
            ? msg.content.join(", ")
            : msg.content,
        });
      } else if (msg["event.name"] === "gen_ai.user.message" && msg.content) {
        inputMessages.push({
          role: "user",
          content: Array.isArray(msg.content)
            ? msg.content.join(", ")
            : msg.content,
        });
      } else if (msg["event.name"] === "gen_ai.choice") {
        // Handle gen_ai.choice events - these contain assistant responses
        if (msg.message && isMessageObject(msg.message)) {
          const message = msg.message;
          if (message.tool_calls && Array.isArray(message.tool_calls)) {
            // Assistant message with tool calls
            const validToolCalls = message.tool_calls.filter(isToolCall);
            if (validToolCalls.length > 0) {
              outputMessages.push({
                role: "assistant",
                content: "",
                tool_calls: validToolCalls.map((toolCall) => ({
                  id: toolCall.id,
                  type: "function" as const,
                  function: {
                    name: toolCall.function.name,
                    arguments: toolCall.function.arguments,
                  },
                })),
              });
            }
          } else if (message.content && typeof message.content === "string") {
            // Regular assistant message
            outputMessages.push({
              role: "assistant",
              content: message.content,
            });
          }
        }
      } else if (
        msg["event.name"] === "gen_ai.assistant.message" &&
        msg.content
      ) {
        outputMessages.push({
          role: "assistant",
          content: Array.isArray(msg.content)
            ? msg.content.join(", ")
            : msg.content,
        });
      } else if (
        msg["event.name"] === "gen_ai.tool.message" &&
        msg.content &&
        msg.id
      ) {
        outputMessages.push({
          role: "tool",
          content: Array.isArray(msg.content)
            ? msg.content.join(", ")
            : msg.content,
          tool_call_id: msg.id,
        });
      }
    }

    return {
      inputMessages,
      outputMessages,
    };
  } catch {
    return null;
  }
}

// pydantic_ai.all_messages needs to be parsed for input and output so we
// check for final_result and remove the last message if it matches.
//
// NOTE: it would be easier to look at finish_reason but that requires updating
// our internal format
function parsePydanticAIAllMessages(
  attributes: unknown,
  mode: "input" | "output",
): MessageField | undefined {
  const finalResultAttr = get(attributes, "final_result");
  const finalResult =
    finalResultAttr !== undefined && typeof finalResultAttr === "string"
      ? finalResultAttr
      : undefined;

  const allMessages = get(attributes, "pydantic_ai.all_messages");
  if (allMessages !== undefined && !isEmpty(allMessages)) {
    const result = translateGenAIMessages({
      value: typeof allMessages === "string" ? undefined : allMessages,
      valueJson: typeof allMessages === "string" ? allMessages : undefined,
    });
    if (result !== undefined && result.isLLM) {
      const lastMessage =
        result.data.length > 0
          ? result.data[result.data.length - 1]
          : undefined;
      if (
        finalResult !== undefined &&
        lastMessage !== undefined &&
        lastMessage.content === finalResult
      ) {
        if (mode === "output") {
          return {
            isLLM: result.isLLM,
            data: result.data.slice(-1),
          };
        } else {
          return {
            isLLM: result.isLLM,
            data: result.data.slice(0, -1),
          };
        }
      }
      return result;
    }
  }

  return undefined;
}
