import { describe, it, expect } from "vitest";
import { otelSpanToRow } from "../collector";

/**
 * Test data for pydantic-ai OTEL telemetry processing.
 *
 * This data was captured from running the pydantic-ai example in examples/pydantic-ai/
 * with BRAINTRUST_OTEL_CONSOLE_EXPORT=true. The example creates an agent with two tools
 * (roll_dice and get_player_name) and runs a dice guessing game.
 *
 * The pydantic-ai library generates several types of OTEL spans:
 * 1. Chat spans (gen_ai.operation.name: "chat") - contain events as JSON string in attributes
 * 2. Agent run spans (logfire.msg: "agent run") - contain all_messages_events as JSON string
 * 3. Tool execution spans (gen_ai.tool.name + logfire.msg: "running tool:") - individual tool calls
 *
 * The test data represents the console exporter output converted to proper OTEL format.
 */

describe("pydantic-ai 1.0.2", () => {
  it("handles pydantic-ai 1.0.2 chat span with events field", () => {
    const realSpan = {
      traceId: "lDuVYd5/smzT0FLHBaJ23Q==",
      spanId: "ahawOPYk8TA=",
      name: "chat gpt-4o",
      startTimeUnixNano: "1757709708523277000",
      endTimeUnixNano: "1757709709645462000",
      attributes: [
        {
          key: "gen_ai.operation.name",
          value: {
            stringValue: "chat",
          },
        },
        {
          key: "gen_ai.system",
          value: {
            stringValue: "openai",
          },
        },
        {
          key: "gen_ai.request.model",
          value: {
            stringValue: "gpt-4o",
          },
        },
        {
          key: "server.address",
          value: {
            stringValue: "api.openai.com",
          },
        },
        {
          key: "model_request_parameters",
          value: {
            stringValue:
              '{"function_tools": [{"name": "roll_dice", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Roll a six-sided die and return the result.", "outer_typed_dict_key": null, "strict": false, "sequential": false, "kind": "function"}, {"name": "get_player_name", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Get the player\'s name.", "outer_typed_dict_key": null, "strict": false, "sequential": false, "kind": "function"}], "builtin_tools": [], "output_mode": "text", "output_object": null, "output_tools": [], "allow_text_output": true}',
          },
        },
        {
          key: "gen_ai.input.messages",
          value: {
            stringValue:
              '[{"role": "system", "parts": [{"type": "text", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response."}]}, {"role": "user", "parts": [{"type": "text", "content": "My guess is 4"}]}]',
          },
        },
        {
          key: "gen_ai.output.messages",
          value: {
            stringValue:
              '[{"role": "assistant", "parts": [{"type": "tool_call", "id": "call_KmVd0l19975JnVeHizsz4k1L", "name": "roll_dice", "arguments": "{}"}, {"type": "tool_call", "id": "call_PXLu9MP7yn4SDMuAnJTesEbi", "name": "get_player_name", "arguments": "{}"}], "finish_reason": "tool_call"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"gen_ai.input.messages": {"type": "array"}, "gen_ai.output.messages": {"type": "array"}, "model_request_parameters": {"type": "object"}}}',
          },
        },
        {
          key: "gen_ai.usage.input_tokens",
          value: {
            intValue: "90",
          },
        },
        {
          key: "gen_ai.usage.output_tokens",
          value: {
            intValue: "41",
          },
        },
        {
          key: "gen_ai.response.model",
          value: {
            stringValue: "gpt-4o-2024-08-06",
          },
        },
        {
          key: "operation.cost",
          value: {
            doubleValue: 0.000635,
          },
        },
        {
          key: "gen_ai.response.id",
          value: {
            stringValue: "chatcmpl-CF4sWGEW3Uz4sl3ET2YvQ5UdzDUQ3",
          },
        },
        {
          key: "gen_ai.response.finish_reasons",
          value: {
            stringValue: "('tool_call',)",
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(realSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
      ],
      output: [
        {
          role: "assistant",
          tool_calls: [
            {
              id: "call_KmVd0l19975JnVeHizsz4k1L",
              type: "function",
              function: {
                name: "roll_dice",
                arguments: "{}",
              },
            },
            {
              id: "call_PXLu9MP7yn4SDMuAnJTesEbi",
              type: "function",
              function: {
                name: "get_player_name",
                arguments: "{}",
              },
            },
          ],
        },
      ],
      metadata: expect.objectContaining({
        "gen_ai.system": "openai",
        "gen_ai.operation.name": "chat",
        "gen_ai.request.model": "gpt-4o",
        tools: [
          {
            type: "function",
            function: {
              name: "roll_dice",
              description: "Roll a six-sided die and return the result.",
              parameters: {
                additionalProperties: false,
                type: "object",
                properties: {},
              },
            },
          },
          {
            type: "function",
            function: {
              name: "get_player_name",
              description: "Get the player's name.",
              parameters: {
                additionalProperties: false,
                type: "object",
                properties: {},
              },
            },
          },
        ],
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 90,
        completion_tokens: 41,
        tokens: 131,
      }),
      span_attributes: expect.objectContaining({
        name: "chat gpt-4o",
        type: "llm",
      }),
    });

    // Validate field processing stats - only specs with activity should appear
    expect(fieldStats.toObject()).toEqual({
      genAI: { ok: 5, errs: 0 },
      pydanticAI: { ok: 1, errs: 0 },
    });
  });

  it("handles pydantic-ai 1.0.2 agent run with tool calls", () => {
    const realSpan = {
      traceId: "lDuVYd5/smzT0FLHBaJ23Q==",
      spanId: "2AVEvayc0IA=",
      name: "agent run",
      startTimeUnixNano: "1757709708522589000",
      endTimeUnixNano: "1757709710737739000",
      attributes: [
        {
          key: "model_name",
          value: {
            stringValue: "gpt-4o",
          },
        },
        {
          key: "agent_name",
          value: {
            stringValue: "agent",
          },
        },
        {
          key: "logfire.msg",
          value: {
            stringValue: "agent run",
          },
        },
        {
          key: "final_result",
          value: {
            stringValue:
              "Thanks for playing, Anne! You guessed 4, but the dice showed 2. Better luck next time!",
          },
        },
        {
          key: "gen_ai.usage.input_tokens",
          value: {
            intValue: "237",
          },
        },
        {
          key: "gen_ai.usage.output_tokens",
          value: {
            intValue: "65",
          },
        },
        {
          key: "pydantic_ai.all_messages",
          value: {
            stringValue:
              '[{"role": "system", "parts": [{"type": "text", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response."}]}, {"role": "user", "parts": [{"type": "text", "content": "My guess is 4"}]}, {"role": "assistant", "parts": [{"type": "tool_call", "id": "call_KmVd0l19975JnVeHizsz4k1L", "name": "roll_dice", "arguments": "{}"}, {"type": "tool_call", "id": "call_PXLu9MP7yn4SDMuAnJTesEbi", "name": "get_player_name", "arguments": "{}"}], "finish_reason": "tool_call"}, {"role": "user", "parts": [{"type": "tool_call_response", "id": "call_KmVd0l19975JnVeHizsz4k1L", "name": "roll_dice", "result": "2"}, {"type": "tool_call_response", "id": "call_PXLu9MP7yn4SDMuAnJTesEbi", "name": "get_player_name", "result": "Anne"}]}, {"role": "assistant", "parts": [{"type": "text", "content": "Thanks for playing, Anne! You guessed 4, but the dice showed 2. Better luck next time!"}], "finish_reason": "stop"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"pydantic_ai.all_messages": {"type": "array"}, "final_result": {"type": "object"}}}',
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(realSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
        {
          role: "assistant",
          tool_calls: [
            {
              id: "call_KmVd0l19975JnVeHizsz4k1L",
              type: "function",
              function: {
                name: "roll_dice",
                arguments: "{}",
              },
            },
            {
              id: "call_PXLu9MP7yn4SDMuAnJTesEbi",
              type: "function",
              function: {
                name: "get_player_name",
                arguments: "{}",
              },
            },
          ],
        },
        {
          role: "tool",
          content: "2",
          tool_call_id: "call_KmVd0l19975JnVeHizsz4k1L",
        },
        {
          role: "tool",
          content: "Anne",
          tool_call_id: "call_PXLu9MP7yn4SDMuAnJTesEbi",
        },
      ],
      output: [
        {
          role: "assistant",
          content:
            "Thanks for playing, Anne! You guessed 4, but the dice showed 2. Better luck next time!",
        },
      ],
      metadata: expect.objectContaining({
        model: "gpt-4o",
        model_name: "gpt-4o",
        agent_name: "agent",
        final_result:
          "Thanks for playing, Anne! You guessed 4, but the dice showed 2. Better luck next time!",
        "gen_ai.usage.input_tokens": 237,
        "gen_ai.usage.output_tokens": 65,
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 237,
        completion_tokens: 65,
        tokens: 302,
      }),
      span_attributes: expect.objectContaining({
        name: "agent run",
        type: "llm",
      }),
    });

    // Validate field processing stats - only specs with activity should appear
    expect(fieldStats.toObject()).toEqual({
      genAI: { ok: 1, errs: 0 },
      pydanticAI: { ok: 3, errs: 0 },
    });
  });

  it("handles pydantic-ai 1.0.2 tool execution span", () => {
    const realToolSpan = {
      traceId: "lDuVYd5/smzT0FLHBaJ23Q==",
      spanId: "B99wcJaU/A8=",
      name: "running tool",
      startTimeUnixNano: "1757709709649303000",
      endTimeUnixNano: "1757709709651974000",
      attributes: [
        {
          key: "gen_ai.tool.name",
          value: {
            stringValue: "roll_dice",
          },
        },
        {
          key: "gen_ai.tool.call.id",
          value: {
            stringValue: "call_KmVd0l19975JnVeHizsz4k1L",
          },
        },
        {
          key: "tool_arguments",
          value: {
            stringValue: "{}",
          },
        },
        {
          key: "logfire.msg",
          value: {
            stringValue: "running tool: roll_dice",
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"tool_arguments": {"type": "object"}, "tool_response": {"type": "object"}, "gen_ai.tool.name": {}, "gen_ai.tool.call.id": {}}}',
          },
        },
        {
          key: "tool_response",
          value: {
            stringValue: "2",
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(realToolSpan);

    expect(row).toMatchObject({
      input: {},
      output: 2,
      metadata: expect.objectContaining({
        "gen_ai.tool.name": "roll_dice",
        "gen_ai.tool.call.id": "call_KmVd0l19975JnVeHizsz4k1L",
        "logfire.msg": "running tool: roll_dice",
        tools: [
          {
            function: {
              description: "Tool: roll_dice",
              name: "roll_dice",
              parameters: { properties: {}, required: [], type: "object" },
            },
            type: "function",
          },
        ],
      }),
      metrics: {
        end: 1757709709.651974,
        start: 1757709709.649303,
      },
      span_attributes: expect.objectContaining({
        name: "running tool: roll_dice",
        type: "tool",
      }),
    });

    // Validate field processing stats - only specs with activity should appear
    expect(fieldStats.toObject()).toEqual({
      genAI: { ok: 1, errs: 0 },
      pydanticAI: { ok: 3, errs: 0 },
    });
  });

  it("handles pydantic-ai 1.0.2 custom agent span with all_messages_events", () => {
    const customAgentSpan = {
      traceId: "6ug8QcTkf6N3DU202c5r6A==",
      spanId: "JMBDyvnXsmg=",
      name: "agent run",
      startTimeUnixNano: "1757710133684921000",
      endTimeUnixNano: "1757710137628583000",
      attributes: [
        {
          key: "model_name",
          value: {
            stringValue: "gpt-4o",
          },
        },
        {
          key: "agent_name",
          value: {
            stringValue: "joke_selection_agent",
          },
        },
        {
          key: "logfire.msg",
          value: {
            stringValue: "joke_selection_agent run",
          },
        },
        {
          key: "final_result",
          value: {
            stringValue:
              "Why did the scarecrow win an award? Because he was outstanding in his field!",
          },
        },
        {
          key: "gen_ai.usage.input_tokens",
          value: {
            intValue: "404",
          },
        },
        {
          key: "gen_ai.usage.output_tokens",
          value: {
            intValue: "128",
          },
        },
        {
          key: "pydantic_ai.all_messages",
          value: {
            stringValue:
              '[{"role": "system", "parts": [{"type": "text", "content": "Use the `joke_factory` to generate some jokes, then choose the best. You must return just a single joke."}]}, {"role": "user", "parts": [{"type": "text", "content": "Tell me a joke."}]}, {"role": "assistant", "parts": [{"type": "tool_call", "id": "call_qF1WEKucTfQUSEPS75z0iXlK", "name": "joke_factory", "arguments": "{\\"count\\":5}"}], "finish_reason": "tool_call"}, {"role": "user", "parts": [{"type": "tool_call_response", "id": "call_qF1WEKucTfQUSEPS75z0iXlK", "name": "joke_factory", "result": ["Why don\'t scientists trust atoms? Because they make up everything!", "What do you call fake spaghetti? An impasta!", "What\'s orange and sounds like a parrot? A carrot!", "Why don\'t skeletons fight each other? They don\'t have the guts.", "Why did the scarecrow win an award? Because he was outstanding in his field!"]}]}, {"role": "assistant", "parts": [{"type": "text", "content": "Why did the scarecrow win an award? Because he was outstanding in his field!"}], "finish_reason": "stop"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"pydantic_ai.all_messages": {"type": "array"}, "final_result": {"type": "object"}}}',
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(customAgentSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "Use the `joke_factory` to generate some jokes, then choose the best. You must return just a single joke.",
        },
        {
          role: "user",
          content: "Tell me a joke.",
        },
        {
          role: "assistant",
          tool_calls: [
            {
              id: "call_qF1WEKucTfQUSEPS75z0iXlK",
              type: "function",
              function: {
                name: "joke_factory",
                arguments: '{"count":5}',
              },
            },
          ],
        },
        {
          role: "tool",
          content: JSON.stringify([
            "Why don't scientists trust atoms? Because they make up everything!",
            "What do you call fake spaghetti? An impasta!",
            "What's orange and sounds like a parrot? A carrot!",
            "Why don't skeletons fight each other? They don't have the guts.",
            "Why did the scarecrow win an award? Because he was outstanding in his field!",
          ]),
          tool_call_id: "call_qF1WEKucTfQUSEPS75z0iXlK",
        },
      ],
      output: [
        {
          role: "assistant",
          content:
            "Why did the scarecrow win an award? Because he was outstanding in his field!",
        },
      ],
      span_attributes: expect.objectContaining({
        type: "llm",
      }),
    });

    expect(fieldStats.toObject()).toEqual({
      genAI: {
        errs: 0,
        ok: 1,
      },
      pydanticAI: {
        errs: 0,
        ok: 3,
      },
    });
  });
});

describe("pydantic-ai 0.8.1", () => {
  it("handles pydantic-ai 0.8.1 chat span with events field", () => {
    const realSpan = {
      traceId: "fD+LgdMt7qd32giBRg8hrQ==",
      spanId: "JZbuo+4UNnA=",
      name: "chat gpt-4o",
      startTimeUnixNano: "1757548760239936000",
      endTimeUnixNano: "1757548760918505000",
      attributes: [
        {
          key: "gen_ai.operation.name",
          value: {
            stringValue: "chat",
          },
        },
        {
          key: "gen_ai.system",
          value: {
            stringValue: "openai",
          },
        },
        {
          key: "gen_ai.request.model",
          value: {
            stringValue: "gpt-4o",
          },
        },
        {
          key: "server.address",
          value: {
            stringValue: "api.openai.com",
          },
        },
        {
          key: "model_request_parameters",
          value: {
            stringValue:
              '{"function_tools": [{"name": "roll_dice", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Roll a six-sided die and return the result.", "outer_typed_dict_key": null, "strict": false, "kind": "function"}, {"name": "get_player_name", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Get the player\'s name.", "outer_typed_dict_key": null, "strict": false, "kind": "function"}], "builtin_tools": [], "output_mode": "text", "output_object": null, "output_tools": [], "allow_text_output": true}',
          },
        },
        {
          key: "events",
          value: {
            stringValue:
              '[{"role": "system", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response.", "gen_ai.system": "openai", "gen_ai.message.index": 0, "event.name": "gen_ai.system.message"}, {"content": "My guess is 4", "role": "user", "gen_ai.system": "openai", "gen_ai.message.index": 0, "event.name": "gen_ai.user.message"}, {"role": "assistant", "tool_calls": [{"id": "call_6F9EotEVtgDAl2AvNrbONDT2", "type": "function", "function": {"name": "roll_dice", "arguments": "{}"}}, {"id": "call_tyuozKxgpbEdRVuganIK6zZJ", "type": "function", "function": {"name": "get_player_name", "arguments": "{}"}}], "gen_ai.system": "openai", "gen_ai.message.index": 1, "event.name": "gen_ai.assistant.message"}, {"content": "3", "role": "tool", "id": "call_6F9EotEVtgDAl2AvNrbONDT2", "name": "roll_dice", "gen_ai.system": "openai", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"content": "Anne", "role": "tool", "id": "call_tyuozKxgpbEdRVuganIK6zZJ", "name": "get_player_name", "gen_ai.system": "openai", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"index": 0, "message": {"role": "assistant", "content": "Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! \\ud83c\\udfb2"}, "gen_ai.system": "openai", "event.name": "gen_ai.choice"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"events": {"type": "array"}, "model_request_parameters": {"type": "object"}}}',
          },
        },
        {
          key: "gen_ai.usage.input_tokens",
          value: {
            stringValue: "147",
          },
        },
        {
          key: "gen_ai.usage.output_tokens",
          value: {
            stringValue: "24",
          },
        },
        {
          key: "gen_ai.response.model",
          value: {
            stringValue: "gpt-4o-2024-08-06",
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(realSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
      ],
      output: [
        {
          content: "3",
          role: "tool",
          tool_call_id: "call_6F9EotEVtgDAl2AvNrbONDT2",
        },
        {
          content: "Anne",
          role: "tool",
          tool_call_id: "call_tyuozKxgpbEdRVuganIK6zZJ",
        },
        {
          content:
            "Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! 🎲",
          role: "assistant",
        },
      ],
      metadata: expect.objectContaining({
        "gen_ai.operation.name": "chat",
        "gen_ai.system": "openai",
        "gen_ai.request.model": "gpt-4o",
        model: "gpt-4o",
        tools: [
          {
            type: "function",
            function: {
              name: "roll_dice",
              description: "Roll a six-sided die and return the result.",
              parameters: {
                additionalProperties: false,
                properties: {},
                type: "object",
              },
            },
          },
          {
            type: "function",
            function: {
              name: "get_player_name",
              description: "Get the player's name.",
              parameters: {
                additionalProperties: false,
                properties: {},
                type: "object",
              },
            },
          },
        ],
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 147,
        completion_tokens: 24,
        tokens: 171,
      }),
      span_attributes: expect.objectContaining({
        name: "chat gpt-4o",
        type: "llm",
      }),
    });

    // Verify that events field was deleted after processing
    expect(row?.metadata).not.toHaveProperty("events");

    // Validate field processing stats - only specs with activity should appear
    expect(fieldStats.toObject()).toEqual({
      genAI: { ok: 3, errs: 0 },
      pydanticAI: { ok: 3, errs: 0 },
    });
  });

  it("handles pydantic-ai 0.8.1 agent run with tool calls", () => {
    const realSpan = {
      traceId: "fD+LgdMt7qd32giBRg8hrQ==",
      spanId: "799U3iYctfs=",
      name: "agent run",
      startTimeUnixNano: "1757548759279468000",
      endTimeUnixNano: "1757548760918881000",
      attributes: [
        {
          key: "model_name",
          value: {
            stringValue: "gpt-4o",
          },
        },
        {
          key: "agent_name",
          value: {
            stringValue: "agent",
          },
        },
        {
          key: "logfire.msg",
          value: {
            stringValue: "agent run",
          },
        },
        {
          key: "final_result",
          value: {
            stringValue:
              "Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! \ud83c\udfb2",
          },
        },
        {
          key: "gen_ai.usage.input_tokens",
          value: {
            stringValue: "237",
          },
        },
        {
          key: "gen_ai.usage.output_tokens",
          value: {
            stringValue: "65",
          },
        },
        {
          key: "all_messages_events",
          value: {
            stringValue:
              '[{"role": "system", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response.", "gen_ai.message.index": 0, "event.name": "gen_ai.system.message"}, {"content": "My guess is 4", "role": "user", "gen_ai.message.index": 0, "event.name": "gen_ai.user.message"}, {"role": "assistant", "tool_calls": [{"id": "call_6F9EotEVtgDAl2AvNrbONDT2", "type": "function", "function": {"name": "roll_dice", "arguments": "{}"}}, {"id": "call_tyuozKxgpbEdRVuganIK6zZJ", "type": "function", "function": {"name": "get_player_name", "arguments": "{}"}}], "gen_ai.message.index": 1, "event.name": "gen_ai.assistant.message"}, {"content": "3", "role": "tool", "id": "call_6F9EotEVtgDAl2AvNrbONDT2", "name": "roll_dice", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"content": "Anne", "role": "tool", "id": "call_tyuozKxgpbEdRVuganIK6zZJ", "name": "get_player_name", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"role": "assistant", "content": "Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! \\ud83c\\udfb2", "gen_ai.message.index": 3, "event.name": "gen_ai.assistant.message"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"all_messages_events": {"type": "array"}, "final_result": {"type": "object"}}}',
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(realSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
        {
          role: "assistant",
          tool_calls: [
            {
              function: {
                arguments: "{}",
                name: "roll_dice",
              },
              id: "call_6F9EotEVtgDAl2AvNrbONDT2",
              type: "function",
            },
            {
              function: {
                arguments: "{}",
                name: "get_player_name",
              },
              id: "call_tyuozKxgpbEdRVuganIK6zZJ",
              type: "function",
            },
          ],
        },
        {
          content: [
            {
              text: "3",
              type: "text",
            },
          ],
          role: "tool",
          tool_call_id: "call_6F9EotEVtgDAl2AvNrbONDT2",
        },
        {
          content: [
            {
              text: "Anne",
              type: "text",
            },
          ],
          role: "tool",
          tool_call_id: "call_tyuozKxgpbEdRVuganIK6zZJ",
        },
      ],
      output: [
        {
          role: "assistant",
          content:
            "Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! 🎲",
        },
      ],
      metadata: expect.objectContaining({
        model: "gpt-4o",
        model_name: "gpt-4o",
        agent_name: "agent",
        final_result:
          "Hi Anne! You guessed 4, but the roll was a 3. Better luck next time! 🎲",
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 237,
        completion_tokens: 65,
        tokens: 302,
      }),
      span_attributes: expect.objectContaining({
        name: "agent run",
        type: "llm",
      }),
    });

    // Validate field processing stats - only specs with activity should appear
    expect(fieldStats.toObject()).toEqual({
      genAI: { ok: 1, errs: 0 },
      pydanticAI: { ok: 4, errs: 0 },
    });
  });

  it("handles pydantic-ai 0.8.1 tool execution span", () => {
    const realToolSpan = {
      traceId: "fD+LgdMt7qd32giBRg8hrQ==",
      spanId: "seE1iKyF2YA=",
      name: "running tool",
      startTimeUnixNano: "1757548760239057000",
      endTimeUnixNano: "1757548760239516000",
      attributes: [
        {
          key: "gen_ai.tool.name",
          value: {
            stringValue: "roll_dice",
          },
        },
        {
          key: "gen_ai.tool.call.id",
          value: {
            stringValue: "call_6F9EotEVtgDAl2AvNrbONDT2",
          },
        },
        {
          key: "tool_arguments",
          value: {
            stringValue: "{}",
          },
        },
        {
          key: "logfire.msg",
          value: {
            stringValue: "running tool: roll_dice",
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"tool_arguments": {"type": "object"}, "tool_response": {"type": "object"}, "gen_ai.tool.name": {}, "gen_ai.tool.call.id": {}}}',
          },
        },
        {
          key: "tool_response",
          value: {
            stringValue: "3",
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(realToolSpan);

    expect(row).toMatchObject({
      input: {}, // Tool arguments moved to input (empty object in this case)
      output: 3, // Tool response moved to output
      metadata: expect.objectContaining({
        "gen_ai.tool.name": "roll_dice",
        "gen_ai.tool.call.id": "call_6F9EotEVtgDAl2AvNrbONDT2",
        "logfire.msg": "running tool: roll_dice",
        tools: [
          {
            function: {
              description: "Tool: roll_dice",
              name: "roll_dice",
              parameters: { properties: {}, required: [], type: "object" },
            },
            type: "function",
          },
        ],
      }),
      span_attributes: expect.objectContaining({
        name: "running tool: roll_dice",
        type: "tool",
      }),
    });

    // Validate field processing stats - only specs with activity should appear
    expect(fieldStats.toObject()).toEqual({
      genAI: { ok: 1, errs: 0 },
      pydanticAI: { ok: 3, errs: 0 },
    });
  });

  it("handles pydantic-ai 0.8.1 custom agent span with all_messages_events", () => {
    const customAgentSpan = {
      traceId: "ZzRmFJriZ2SOAwxClA0LOQ==",
      spanId: "xTk7xCVSyrA=",
      name: "agent run",
      startTimeUnixNano: "1757630930493260000",
      endTimeUnixNano: "1757630936630434000",
      attributes: [
        {
          key: "model_name",
          value: {
            stringValue: "gpt-4o",
          },
        },
        {
          key: "agent_name",
          value: {
            stringValue: "joke_selection_agent",
          },
        },
        {
          key: "logfire.msg",
          value: {
            stringValue: "joke_selection_agent run",
          },
        },
        {
          key: "final_result",
          value: {
            stringValue:
              "Why don't scientists trust atoms? Because they make up everything!",
          },
        },
        {
          key: "gen_ai.usage.input_tokens",
          value: {
            stringValue: "415",
          },
        },
        {
          key: "gen_ai.usage.output_tokens",
          value: {
            stringValue: "149",
          },
        },
        {
          key: "all_messages_events",
          value: {
            stringValue:
              '[{"role": "system", "content": "Use the `joke_factory` to generate some jokes, then choose the best. You must return just a single joke.", "gen_ai.message.index": 0, "event.name": "gen_ai.system.message"}, {"content": "Tell me a joke.", "role": "user", "gen_ai.message.index": 0, "event.name": "gen_ai.user.message"}, {"role": "assistant", "tool_calls": [{"id": "call_IXrQXwdsxQsjS5vYnAPUSSP1", "type": "function", "function": {"name": "joke_factory", "arguments": "{\\"count\\":5}"}}], "gen_ai.message.index": 1, "event.name": "gen_ai.assistant.message"}, {"content": ["Why don\'t scientists trust atoms? Because they make up everything!", "Why don\'t we ever tell secrets on a farm? Because potatoes have eyes and corn has ears!", "Why don\'t programmers like nature? It has too many bugs!", "Why did the scarecrow win an award? Because he was outstanding in his field!", "Why can\'t you give Elsa a balloon? Because she will let it go!"], "role": "tool", "id": "call_IXrQXwdsxQsjS5vYnAPUSSP1", "name": "joke_factory", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"role": "assistant", "content": "Why don\'t scientists trust atoms? Because they make up everything!", "gen_ai.message.index": 3, "event.name": "gen_ai.assistant.message"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"all_messages_events": {"type": "array"}, "final_result": {"type": "object"}}}',
          },
        },
      ],
      status: {},
    };

    const { row, fieldStats } = otelSpanToRow(customAgentSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "Use the `joke_factory` to generate some jokes, then choose the best. You must return just a single joke.",
        },
        {
          role: "user",
          content: "Tell me a joke.",
        },
        {
          role: "assistant",
          tool_calls: [
            {
              id: "call_IXrQXwdsxQsjS5vYnAPUSSP1",
              type: "function",
              function: {
                name: "joke_factory",
                arguments: '{"count":5}',
              },
            },
          ],
        },
        {
          role: "tool",
          content: [
            {
              type: "text",
              text:
                "Why don't scientists trust atoms? Because they make up everything!, " +
                "Why don't we ever tell secrets on a farm? Because potatoes have eyes and corn has ears!, " +
                "Why don't programmers like nature? It has too many bugs!, " +
                "Why did the scarecrow win an award? Because he was outstanding in his field!, " +
                "Why can't you give Elsa a balloon? Because she will let it go!",
            },
          ],
          tool_call_id: "call_IXrQXwdsxQsjS5vYnAPUSSP1",
        },
      ],
      output: [
        {
          role: "assistant",
          content:
            "Why don't scientists trust atoms? Because they make up everything!",
        },
      ],
      span_attributes: expect.objectContaining({
        type: "llm",
      }),
    });

    expect(fieldStats.toObject()).toEqual({
      genAI: {
        errs: 0,
        ok: 1,
      },
      pydanticAI: {
        errs: 0,
        ok: 4,
      },
    });
  });
});

describe("pydantic-ai <0.8.1", () => {
  it("handles pydantic-ai <0.8.1 chat span with events field", () => {
    // Test a chat span with gen_ai.operation.name: "chat" and events in attributes
    const pydanticAiChatSpan = {
      traceId: "45d84ea0ce3397672dfc3c695ef557fb",
      spanId: "383402bd4031fdef",
      parentSpanId: "6448bf429cf7dbaf",
      name: "chat gpt-4o",
      startTimeUnixNano: "1725038115961117000",
      endTimeUnixNano: "1725038117111910000",
      attributes: [
        { key: "gen_ai.operation.name", value: { stringValue: "chat" } },
        { key: "gen_ai.system", value: { stringValue: "openai" } },
        { key: "gen_ai.request.model", value: { stringValue: "gpt-4o" } },
        { key: "server.address", value: { stringValue: "api.openai.com" } },
        { key: "gen_ai.usage.input_tokens", value: { intValue: 90 } },
        { key: "gen_ai.usage.output_tokens", value: { intValue: 41 } },
        {
          key: "gen_ai.response.model",
          value: { stringValue: "gpt-4o-2024-08-06" },
        },
        {
          key: "events",
          value: {
            stringValue:
              '[{"role": "system", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response.", "gen_ai.system": "openai", "gen_ai.message.index": 0, "event.name": "gen_ai.system.message"}, {"content": "My guess is 4", "role": "user", "gen_ai.system": "openai", "gen_ai.message.index": 0, "event.name": "gen_ai.user.message"}, {"index": 0, "message": {"role": "assistant", "tool_calls": [{"id": "call_44WrLygWFlHJhslzz3b8ntRn", "type": "function", "function": {"name": "get_player_name", "arguments": "{}"}}, {"id": "call_1zDy4xWoCKHsT01U5Rt1wbpR", "type": "function", "function": {"name": "roll_dice", "arguments": "{}"}}]}, "gen_ai.system": "openai", "event.name": "gen_ai.choice"}]',
          },
        },
        {
          key: "model_request_parameters",
          value: {
            stringValue:
              '{"function_tools": [{"name": "roll_dice", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Roll a six-sided die and return the result.", "outer_typed_dict_key": null, "strict": false, "kind": "function"}, {"name": "get_player_name", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Get the player\'s name.", "outer_typed_dict_key": null, "strict": false, "kind": "function"}], "builtin_tools": [], "output_mode": "text", "output_object": null, "output_tools": [], "allow_text_output": true, "temperature": 0.7, "max_tokens": 1000, "top_p": 0.9}',
          },
        },
        { key: "gen_ai.request.temperature", value: { doubleValue: 0.7 } },
        { key: "gen_ai.request.max_tokens", value: { intValue: 1000 } },
        { key: "gen_ai.request.top_p", value: { doubleValue: 0.9 } },
        { key: "telemetry.sdk.language", value: { stringValue: "python" } },
        { key: "telemetry.sdk.name", value: { stringValue: "opentelemetry" } },
        { key: "telemetry.sdk.version", value: { stringValue: "1.36.0" } },
        { key: "service.name", value: { stringValue: "unknown_service" } },
      ],
      events: [], // Empty - events are in the attributes.events field as JSON
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(pydanticAiChatSpan);

    // This should be processed by pydantic-AI parser for events and GenAI for metadata
    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
      ],
      output: [
        {
          role: "assistant",
          tool_calls: [
            {
              id: "call_44WrLygWFlHJhslzz3b8ntRn",
              type: "function",
              function: {
                name: "get_player_name",
                arguments: "{}",
              },
            },
            {
              id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
              type: "function",
              function: {
                name: "roll_dice",
                arguments: "{}",
              },
            },
          ],
        },
      ],
      metadata: expect.objectContaining({
        "gen_ai.operation.name": "chat",
        "gen_ai.system": "openai",
        "gen_ai.request.model": "gpt-4o",
        "gen_ai.request.temperature": 0.7,
        "gen_ai.request.max_tokens": 1000,
        "gen_ai.request.top_p": 0.9,
        model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 0.9,
        tools: [
          {
            type: "function",
            function: {
              name: "roll_dice",
              description: "Roll a six-sided die and return the result.",
              parameters: {
                additionalProperties: false,
                properties: {},
                type: "object",
              },
            },
          },
          {
            type: "function",
            function: {
              name: "get_player_name",
              description: "Get the player's name.",
              parameters: {
                additionalProperties: false,
                properties: {},
                type: "object",
              },
            },
          },
        ],
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 90,
        completion_tokens: 41,
        tokens: 131,
      }),
      span_attributes: {
        name: "chat gpt-4o",
        type: "llm",
      },
    });

    // Verify that events field was deleted after processing
    expect(row?.metadata).not.toHaveProperty("events");

    // Both parsers should be active
    expect(fieldStats.toObject()).toEqual({
      pydanticAI: {
        errs: 0,
        ok: 3, // input, output, metadata (tools) - span_attributes only for chat spans now handled by GenAI
      },
      genAI: {
        errs: 0,
        ok: 3, // metadata, metrics, span_attributes
      },
    });
  });

  it("handles pydantic-ai <0.8.1 agent run with tool calls", () => {
    // Test the main agent run span - this contains the full conversation flow
    const pydanticAiAgentSpan = {
      traceId: "45d84ea0ce3397672dfc3c695ef557fb",
      spanId: "6448bf429cf7dbaf",
      parentSpanId: null,
      name: "agent run",
      startTimeUnixNano: "1725038115960585000",
      endTimeUnixNano: "1725038118214845000",
      attributes: [
        { key: "model_name", value: { stringValue: "gpt-4o" } },
        { key: "agent_name", value: { stringValue: "agent" } },
        { key: "logfire.msg", value: { stringValue: "agent run" } },
        {
          key: "final_result",
          value: {
            stringValue:
              "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
          },
        },
        { key: "gen_ai.usage.input_tokens", value: { intValue: 237 } },
        { key: "gen_ai.usage.output_tokens", value: { intValue: 63 } },
        {
          key: "all_messages_events",
          value: {
            stringValue:
              '[{"role": "system", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response.", "gen_ai.message.index": 0, "event.name": "gen_ai.system.message"}, {"content": "My guess is 4", "role": "user", "gen_ai.message.index": 0, "event.name": "gen_ai.user.message"}, {"role": "assistant", "tool_calls": [{"id": "call_44WrLygWFlHJhslzz3b8ntRn", "type": "function", "function": {"name": "get_player_name", "arguments": "{}"}}, {"id": "call_1zDy4xWoCKHsT01U5Rt1wbpR", "type": "function", "function": {"name": "roll_dice", "arguments": "{}"}}], "gen_ai.message.index": 1, "event.name": "gen_ai.assistant.message"}, {"content": "Anne", "role": "tool", "id": "call_44WrLygWFlHJhslzz3b8ntRn", "name": "get_player_name", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"content": "5", "role": "tool", "id": "call_1zDy4xWoCKHsT01U5Rt1wbpR", "name": "roll_dice", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"role": "assistant", "content": "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!", "gen_ai.message.index": 3, "event.name": "gen_ai.assistant.message"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"all_messages_events": {"type": "array"}, "final_result": {"type": "object"}}}',
          },
        },
        { key: "telemetry.sdk.language", value: { stringValue: "python" } },
        { key: "telemetry.sdk.name", value: { stringValue: "opentelemetry" } },
        { key: "telemetry.sdk.version", value: { stringValue: "1.36.0" } },
        { key: "service.name", value: { stringValue: "unknown_service" } },
      ],
      events: [],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(pydanticAiAgentSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
        {
          role: "assistant",
          tool_calls: [
            {
              id: "call_44WrLygWFlHJhslzz3b8ntRn",
              type: "function",
              function: {
                name: "get_player_name",
                arguments: "{}",
              },
            },
            {
              id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
              type: "function",
              function: {
                name: "roll_dice",
                arguments: "{}",
              },
            },
          ],
        },
        {
          role: "tool",
          content: [
            {
              type: "text",
              text: "Anne",
            },
          ],
          tool_call_id: "call_44WrLygWFlHJhslzz3b8ntRn",
        },
        {
          role: "tool",
          content: [
            {
              type: "text",
              text: "5",
            },
          ],
          tool_call_id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
        },
      ],
      output: [
        {
          role: "assistant",
          content:
            "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
        },
      ],
      metadata: expect.objectContaining({
        model: "gpt-4o",
        model_name: "gpt-4o",
        agent_name: "agent",
        final_result:
          "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
        "gen_ai.usage.input_tokens": 237,
        "gen_ai.usage.output_tokens": 63,
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 237,
        completion_tokens: 63,
        tokens: 300,
      }),
      span_attributes: expect.objectContaining({
        name: "agent run",
        type: "llm",
      }),
    });

    // Complete row validation
    expect(row).toEqual({
      _is_merge: false,
      created: "2024-08-30T17:15:15.960Z",
      error: null,
      id: "6448bf429cf7dbaf",
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          content: "My guess is 4",
          role: "user",
        },
        {
          role: "assistant",
          tool_calls: [
            {
              function: {
                arguments: "{}",
                name: "get_player_name",
              },
              id: "call_44WrLygWFlHJhslzz3b8ntRn",
              type: "function",
            },
            {
              function: {
                arguments: "{}",
                name: "roll_dice",
              },
              id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
              type: "function",
            },
          ],
        },
        {
          role: "tool",
          content: [
            {
              type: "text",
              text: "Anne",
            },
          ],
          tool_call_id: "call_44WrLygWFlHJhslzz3b8ntRn",
        },
        {
          role: "tool",
          content: [
            {
              type: "text",
              text: "5",
            },
          ],
          tool_call_id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
        },
      ],
      metadata: {
        agent_name: "agent",
        final_result:
          "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
        "gen_ai.usage.input_tokens": 237,
        "gen_ai.usage.output_tokens": 63,
        "logfire.json_schema":
          '{"type": "object", "properties": {"all_messages_events": {"type": "array"}, "final_result": {"type": "object"}}}',
        "logfire.msg": "agent run",
        model: "gpt-4o",
        model_name: "gpt-4o",
        "service.name": "unknown_service",
        "telemetry.sdk.language": "python",
        "telemetry.sdk.name": "opentelemetry",
        "telemetry.sdk.version": "1.36.0",
      },
      metrics: {
        completion_tokens: 63,
        end: 1725038118.214845,
        prompt_tokens: 237,
        start: 1725038115.9605849,
        tokens: 300,
      },
      output: [
        {
          content:
            "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
          role: "assistant",
        },
      ],
      root_span_id: "45d84ea0ce3397672dfc3c695ef557fb",
      span_attributes: {
        name: "agent run",
        type: "llm",
      },
      span_id: "6448bf429cf7dbaf",
      span_parents: [],
    });

    // Verify that all_messages_events field was deleted after processing
    expect(row?.metadata).not.toHaveProperty("all_messages_events");

    expect(fieldStats.toObject()).toEqual({
      genAI: {
        errs: 0,
        ok: 1,
      },
      pydanticAI: {
        errs: 0,
        ok: 4,
      },
    });
  });

  it("handles pydantic-ai <0.8.1 tool execution span", () => {
    // Test individual tool execution span
    const pydanticAiToolSpan = {
      traceId: "45d84ea0ce3397672dfc3c695ef557fb",
      spanId: "47b675db3f574886",
      parentSpanId: "4ed197363463db15",
      name: "running tool",
      startTimeUnixNano: "1725038117112825000",
      endTimeUnixNano: "1725038117113689000",
      attributes: [
        { key: "gen_ai.tool.name", value: { stringValue: "get_player_name" } },
        {
          key: "gen_ai.tool.call.id",
          value: { stringValue: "call_44WrLygWFlHJhslzz3b8ntRn" },
        },
        { key: "tool_arguments", value: { stringValue: "{}" } },
        {
          key: "logfire.msg",
          value: { stringValue: "running tool: get_player_name" },
        },
        { key: "tool_response", value: { stringValue: "Anne" } },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"tool_arguments": {"type": "object"}, "tool_response": {"type": "object"}, "gen_ai.tool.name": {}, "gen_ai.tool.call.id": {}}}',
          },
        },
        { key: "telemetry.sdk.language", value: { stringValue: "python" } },
        { key: "telemetry.sdk.name", value: { stringValue: "opentelemetry" } },
        { key: "telemetry.sdk.version", value: { stringValue: "1.36.0" } },
        { key: "service.name", value: { stringValue: "unknown_service" } },
      ],
      events: [],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(pydanticAiToolSpan);

    expect(row).toMatchObject({
      input: {}, // Tool arguments moved to input (empty object in this case)
      output: "Anne", // Tool response moved to output
      metadata: expect.objectContaining({
        "gen_ai.tool.name": "get_player_name",
        "gen_ai.tool.call.id": "call_44WrLygWFlHJhslzz3b8ntRn",
        "logfire.msg": "running tool: get_player_name",
        "service.name": "unknown_service",
        "telemetry.sdk.language": "python",
        tools: [
          {
            type: "function",
            function: {
              name: "get_player_name",
              description: "Tool: get_player_name",
              parameters: {
                type: "object",
                properties: {},
                required: [],
              },
            },
          },
        ],
      }),
      span_attributes: expect.objectContaining({
        name: "running tool: get_player_name",
        type: "tool",
      }),
    });

    // Complete row validation for tool execution
    expect(row).toEqual({
      _is_merge: false,
      created: "2024-08-30T17:15:17.112Z",
      error: null,
      id: "47b675db3f574886",
      input: {},
      output: "Anne",
      metadata: {
        "gen_ai.tool.call.id": "call_44WrLygWFlHJhslzz3b8ntRn",
        "gen_ai.tool.name": "get_player_name",
        "logfire.json_schema":
          '{"type": "object", "properties": {"tool_arguments": {"type": "object"}, "tool_response": {"type": "object"}, "gen_ai.tool.name": {}, "gen_ai.tool.call.id": {}}}',
        "logfire.msg": "running tool: get_player_name",
        "service.name": "unknown_service",
        "telemetry.sdk.language": "python",
        "telemetry.sdk.name": "opentelemetry",
        "telemetry.sdk.version": "1.36.0",
        tools: [
          {
            function: {
              description: "Tool: get_player_name",
              name: "get_player_name",
              parameters: {
                properties: {},
                required: [],
                type: "object",
              },
            },
            type: "function",
          },
        ],
      },
      metrics: {
        end: 1725038117.1136892,
        start: 1725038117.1128252,
      },
      root_span_id: "45d84ea0ce3397672dfc3c695ef557fb",
      span_attributes: {
        name: "running tool: get_player_name",
        type: "tool",
      },
      span_id: "47b675db3f574886",
      span_parents: ["4ed197363463db15"],
    });

    expect(fieldStats.toObject()).toEqual({
      genAI: {
        errs: 0,
        ok: 1,
      },
      pydanticAI: {
        errs: 0,
        ok: 3,
      },
    });
  });

  it("handles pydantic-ai <0.8.1 custom agent span with all_messages_events", () => {
    const customAgentSpan = {
      traceId: new Uint8Array([
        0x45, 0xd8, 0x4e, 0xa0, 0xce, 0x33, 0x97, 0x67, 0x2d, 0xfc, 0x3c, 0x69,
        0x5e, 0xf5, 0x57, 0xfb,
      ]),
      spanId: new Uint8Array([0x47, 0xb6, 0x75, 0xdb, 0x3f, 0x57, 0x48, 0x86]),
      parentSpanId: new Uint8Array([
        0x4e, 0xd1, 0x97, 0x36, 0x34, 0x63, 0xdb, 0x15,
      ]),
      name: "joke_selection_agent run",
      startTimeUnixNano: "1725038115960585000",
      endTimeUnixNano: "1725038118214845000",
      attributes: [
        { key: "agent_name", value: { stringValue: "joke_selection_agent" } },
        {
          key: "logfire.msg",
          value: { stringValue: "joke_selection_agent run" },
        },
        {
          key: "all_messages_events",
          value: {
            stringValue: JSON.stringify([
              {
                role: "system",
                content:
                  "Use the `joke_factory` to generate some jokes, then choose the best. You must return just a single joke.",
                "gen_ai.message.index": 0,
                "event.name": "gen_ai.system.message",
              },
              {
                content: "Tell me a joke.",
                role: "user",
                "gen_ai.message.index": 0,
                "event.name": "gen_ai.user.message",
              },
              {
                role: "assistant",
                tool_calls: [
                  {
                    id: "call_y7QKne1WZOXFdzr0FKZuP5OK",
                    type: "function",
                    function: {
                      name: "joke_factory",
                      arguments: '{"count":5}',
                    },
                  },
                ],
                "gen_ai.message.index": 1,
                "event.name": "gen_ai.assistant.message",
              },
              {
                content: [
                  "Why did the tomato turn red? Because it saw the salad dressing!",
                  "Why don't scientists trust atoms? Because they make up everything!",
                  "Why was the cat sitting on the computer? It wanted to keep an eye on the mouse!",
                  "What do you call cheese that isn't yours? Nacho cheese!",
                  "Why don't elephants use computers? They're afraid of the mouse!",
                ],
                role: "tool",
                id: "call_y7QKne1WZOXFdzr0FKZuP5OK",
                name: "joke_factory",
                "gen_ai.message.index": 2,
                "event.name": "gen_ai.tool.message",
              },
              {
                role: "assistant",
                content:
                  "Why don't scientists trust atoms? Because they make up everything!",
                "gen_ai.message.index": 3,
                "event.name": "gen_ai.assistant.message",
              },
            ]),
          },
        },
        {
          key: "final_result",
          value: {
            stringValue:
              "Why don't scientists trust atoms? Because they make up everything!",
          },
        },
        { key: "gen_ai.usage.input_tokens", value: { intValue: 409 } },
        { key: "gen_ai.usage.output_tokens", value: { intValue: 144 } },
        { key: "model_name", value: { stringValue: "gpt-4o" } },
        { key: "telemetry.sdk.language", value: { stringValue: "python" } },
        { key: "telemetry.sdk.name", value: { stringValue: "opentelemetry" } },
        { key: "telemetry.sdk.version", value: { stringValue: "1.36.0" } },
        { key: "service.name", value: { stringValue: "unknown_service" } },
      ],
      events: [],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(customAgentSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "Use the `joke_factory` to generate some jokes, then choose the best. You must return just a single joke.",
        },
        {
          role: "user",
          content: "Tell me a joke.",
        },
        {
          role: "assistant",
          tool_calls: [
            {
              id: "call_y7QKne1WZOXFdzr0FKZuP5OK",
              type: "function",
              function: {
                name: "joke_factory",
                arguments: '{"count":5}',
              },
            },
          ],
        },
        {
          role: "tool",
          content: [
            {
              type: "text",
              text: "Why did the tomato turn red? Because it saw the salad dressing!, Why don't scientists trust atoms? Because they make up everything!, Why was the cat sitting on the computer? It wanted to keep an eye on the mouse!, What do you call cheese that isn't yours? Nacho cheese!, Why don't elephants use computers? They're afraid of the mouse!",
            },
          ],
          tool_call_id: "call_y7QKne1WZOXFdzr0FKZuP5OK",
        },
      ],
      output: [
        {
          role: "assistant",
          content:
            "Why don't scientists trust atoms? Because they make up everything!",
        },
      ],
      span_attributes: expect.objectContaining({
        type: "llm",
      }),
    });

    expect(fieldStats.toObject()).toEqual({
      genAI: {
        errs: 0,
        ok: 1,
      },
      pydanticAI: {
        errs: 0,
        ok: 4,
      },
    });
  });
});
