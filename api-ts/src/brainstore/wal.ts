import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import {
  BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
  BRAINSTORE_REALTIME_WAL_BUCKET_PREFIX,
} from "../env";
import {
  BackfillableObjectType,
  backfillableObjectTypeSchema,
  FullTrackedObject,
} from "@braintrust/local/app-schema";
import {
  rowRefSchema,
  OBJECT_TYPE_FIELD,
  ObjectIdsUnion,
  RowRef,
  auditLogOriginSchema,
} from "@braintrust/local/api-schema";
import { ROW_REF_FIELD } from "@braintrust/local";
import {
  TRANSACTION_ID_FIELD,
  _urljoin,
  deterministicReplacer,
  mapAt,
  mapSetDefault,
} from "braintrust/util";
import { getAclObjectId } from "../util";
import { getStorageToLogicalMap, getLogicalToStorageMap } from "../schema";
import {
  brainstoreEnabled,
  makeBrainstoreObjectIdFromParts,
} from "./brainstore";
import { ident, sql } from "@braintrust/btql/planner";
import { makeObjectStore } from "../object-storage/object-storage";
import { readableToString } from "../stream_util";
import { PoolingBtPgClient } from "@braintrust/local/bt-pg";

function computeTrackedObject({
  event,
  objectIds,
}: {
  event: Record<string, unknown>;
  objectIds: ObjectIdsUnion;
}): FullTrackedObject | undefined {
  const parentIds = z
    .object({
      project_id: z.string(),
      org_id: z.string(),
    })
    .safeParse(event);
  const objectType = backfillableObjectTypeSchema.safeParse(
    objectIds[OBJECT_TYPE_FIELD],
  );
  if (parentIds.success && objectType.success) {
    return {
      project_id: parentIds.data.project_id,
      org_id: parentIds.data.org_id,
      object_type: objectType.data,
    };
  } else {
    return undefined;
  }
}

// Returns a mapping from brainstore object ID to WAL token, for the set of row
// events we will want to insert into the realtime WAL.
//
// IMPORTANT: This function must be run *OUTSIDE* a transaction, so that it
// doesn't block concurrent transactions over the same objects.
export async function brainstoreRealtimeGetWalTokens(
  rows: { event: Record<string, unknown>; objectIds: ObjectIdsUnion }[],
  pgClient: PoolingBtPgClient,
): Promise<Map<string, string>> {
  if (!(brainstoreEnabled() && BRAINSTORE_REALTIME_WAL_BUCKET_NAME)) {
    return new Map();
  }
  const trackedObjectToBrainstoreObjectIds = new Map<string, Set<string>>();
  rows.forEach(({ event, objectIds }) => {
    const trackedObject = computeTrackedObject({ event, objectIds });
    if (!trackedObject) {
      return;
    }
    const objectId = getAclObjectId(objectIds);
    const brainstoreObjectId = makeBrainstoreObjectIdFromParts(
      trackedObject.object_type,
      objectId,
    );
    const key = JSON.stringify(trackedObject, deterministicReplacer);
    const keyEvents = mapSetDefault(
      trackedObjectToBrainstoreObjectIds,
      key,
      new Set<string>(),
    );
    keyEvents.add(brainstoreObjectId);
  });
  if (trackedObjectToBrainstoreObjectIds.size === 0) {
    return new Map();
  }

  const allObjectIds = [...trackedObjectToBrainstoreObjectIds.values()].flatMap(
    (events) => [...events.keys()],
  );
  if (allObjectIds.length === 0) {
    return new Map();
  }

  // Get the WAL token for each object. The query is analogous to
  // postgres_global_store:query_object_metadatas.
  const tableName = ident("brainstore_global_store_object_id_to_metadata");
  const queryTemplate = sql`
      with
      all_object_ids as (
          select unnest(${allObjectIds}::text[]) as object_id
      ),
      existing_objects as (
          select object_id, wal_token
          from ${tableName} join all_object_ids using (object_id)
      ),
      new_objects as (
          insert into ${tableName}(object_id)
          select object_id from all_object_ids
          where object_id not in (select object_id from existing_objects)
          -- This cannot be an 'on conflict do nothing' because of race
          -- conditions with a concurrent delete. Even if the row ends up deleted at
          -- the end of the day, we need to return something.
          on conflict (object_id) do update set object_id = excluded.object_id
          returning object_id, wal_token
      )
      select * from existing_objects
      union all select * from new_objects
    `;
  const { query, params } = queryTemplate.toNumericParamQuery();
  const { rows: queryResults } = await pgClient.query(query, params);
  return new Map<string, string>(
    z
      .object({ object_id: z.string(), wal_token: z.string() })
      .array()
      .parse(queryResults)
      .map((row) => [row.object_id, row.wal_token]),
  );
}

// Returns rowRefs for each input row that we successfully inserted into the
// WAL. Any rows that were skipped (if realtime was disabled or the object type
// is not managed by brainstore) will be missing.
export async function brainstoreRealtimeWalInsert(
  rows: {
    event: Record<string, unknown>;
    auditEvent: Record<string, unknown> | undefined;
    objectIds: ObjectIdsUnion;
  }[],
  brainstoreObjectIdToWalToken: Map<string, string>,
): Promise<Map<number, RowRef>> {
  if (!(brainstoreEnabled() && BRAINSTORE_REALTIME_WAL_BUCKET_NAME)) {
    return new Map();
  }
  const brainstoreObjectIdToXactIdToEvents =
    normalizeAndGroupByObjectIdAndXactId(rows, brainstoreObjectIdToWalToken);
  if (brainstoreObjectIdToXactIdToEvents.size === 0) {
    return new Map();
  }

  // Insert the events into the WAL in parallel. The logic is analogous to
  // ObjectStoreWal::insert defined in brainstore/storage/src/wal.rs.
  const objectStore = await makeObjectStore();
  const rowRefs = new Map<number, RowRef>();
  await Promise.all(
    [...brainstoreObjectIdToXactIdToEvents.entries()].map(
      async ([brainstoreObjectId, xactIdEvents]) => {
        const walToken = mapAt(
          brainstoreObjectIdToWalToken,
          brainstoreObjectId,
        );
        await Promise.all(
          [...xactIdEvents.entries()].map(async ([xactId, eventInfo]) => {
            const walFilename = `${xactId}.${uuidv4()}.jsonl`;
            const key = _urljoin(
              BRAINSTORE_REALTIME_WAL_BUCKET_PREFIX,
              `object-store-objects/${walToken}/${brainstoreObjectId}/${walFilename}`,
            );
            const textEncoder = new TextEncoder();
            const serializedEvents: Uint8Array[] = [];
            const serializedEventByteRanges: [number, number][] = [];
            eventInfo.forEach(({ event, inputIdx }) => {
              const serializedEvent = textEncoder.encode(
                JSON.stringify(event) + "\n",
              );
              const byte_range_start = serializedEventByteRanges.length
                ? serializedEventByteRanges[
                    serializedEventByteRanges.length - 1
                  ][1]
                : 0;
              const byte_range_end = byte_range_start + serializedEvent.length;
              serializedEventByteRanges.push([
                byte_range_start,
                byte_range_end,
              ]);
              serializedEvents.push(serializedEvent);
              rowRefs.set(inputIdx, {
                key,
                byte_range_start,
                byte_range_end,
              });
            });
            await objectStore.put({
              bucket: BRAINSTORE_REALTIME_WAL_BUCKET_NAME!,
              key,
              contentType: "application/json",
              body: Buffer.concat(serializedEvents),
            });
          }),
        );
      },
    ),
  );
  return rowRefs;
}

// Given a list of DB rows, group them into a mapping of
// JSON.stringify(FullTrackedObject) -> normalized_rows. Filter out events which
// are not trackable by Brainstore, or which we do not have a WAL token for.
function normalizeAndGroupByObjectIdAndXactId(
  rows: {
    event: Record<string, unknown>;
    auditEvent: Record<string, unknown> | undefined;
    objectIds: ObjectIdsUnion;
  }[],
  brainstoreObjectIdToWalToken: Map<string, string>,
): Map<
  string,
  Map<string, { event: Record<string, unknown>; inputIdx: number }[]>
> {
  const out: Map<
    string,
    Map<string, { event: Record<string, unknown>; inputIdx: number }[]>
  > = new Map();
  rows.forEach(({ event, auditEvent, objectIds }, inputIdx) => {
    const trackedObject = computeTrackedObject({ event, objectIds });
    if (!trackedObject) {
      return;
    }
    const objectId = getAclObjectId(objectIds);
    const brainstoreObjectId = makeBrainstoreObjectIdFromParts(
      trackedObject.object_type,
      objectId,
    );
    if (!brainstoreObjectIdToWalToken.has(brainstoreObjectId)) {
      return;
    }
    const objectMap = mapSetDefault(
      out,
      brainstoreObjectId,
      new Map<string, Record<string, unknown>[]>(),
    );
    const xactId = z.string().parse(event[TRANSACTION_ID_FIELD]);
    const xactEvents = mapSetDefault(objectMap, xactId, []);
    xactEvents.push({
      event: normalizeRow({
        event,
        auditEvent,
        objectType: trackedObject.object_type,
        objectId,
      }),
      inputIdx,
    });
  });
  return out;
}

// Mirrors the normalization logic in
// brainstore/storage/src/postgres_wal.rs::normalize_row.
function normalizeRow({
  event,
  auditEvent,
  objectType,
  objectId,
}: {
  event: Record<string, unknown>;
  auditEvent: Record<string, unknown> | undefined;
  objectType: BackfillableObjectType;
  objectId: string;
}): Record<string, unknown> {
  // Shallow-copy the event so we don't mutate the original.
  event = { ...event };

  // Normalize columns.
  const storageToLogicalMap = getStorageToLogicalMap(objectType);
  Object.entries(storageToLogicalMap).forEach(([k, v]) => {
    if (k in event) {
      event[v] = event[k];
      delete event[k];
    }
  });

  // If it's a comments row, do the same set of transformations we do in
  // brainstore/storage/src/postgres_wal.rs when `is_comments_row` is true, plus
  // including the required WalEntry fields.
  if ("comment" in event) {
    const fields = z
      .object({
        id: z.string(),
        created: z.string(),
        _pagination_key: z.string(),
        [TRANSACTION_ID_FIELD]: z.string(),
        origin: auditLogOriginSchema,
      })
      .parse(event);
    const span_id = uuidv4();
    event = {
      id: fields.origin.id,
      created: fields.created,
      _pagination_key: fields["_pagination_key"],
      [TRANSACTION_ID_FIELD]: fields[TRANSACTION_ID_FIELD],
      _is_standalone_comment: true,
      comments: [event],
      root_span_id: span_id,
      span_id: span_id,
    };
  }

  event["_object_type"] = objectType;
  event["_object_id"] = objectId;
  if (auditEvent) {
    event["_self_audit_data"] = auditEvent;
  }

  return event;
}

// Compute the pagination key as we do in
// brainstore/util/src/xact.rs:make_pagination_key. We use the
// IndependentCounter form, where rowIdx is the index of the row within the
// transaction.
export function makePaginationKey({
  xactId: xactIdStr,
  rowIdx,
}: {
  xactId: string;
  rowIdx: number;
}): string {
  const xactId = z.coerce.bigint().parse(xactIdStr);
  const paginationKey =
    ((xactId & 0xffffffffffffn) << 16n) | (BigInt(rowIdx) & 0xffffn);
  return `p${paginationKey.toString().padStart(20, "0")}`;
}

const maybeRowRefSchema = z.object({
  [ROW_REF_FIELD]: rowRefSchema.nullish(),
});

// Resolve any RowRefs in the given records to their actual data blobs.
export async function resolveRowRefs(
  records: { data: Record<string, unknown>; objectIds: ObjectIdsUnion }[],
): Promise<Record<string, unknown>[]> {
  // Obtain a list of record indices which contain RowRefs and the RowRefs themselves.
  const rowRefRecords = records
    .map((record, index) => {
      const rowRef = maybeRowRefSchema.parse(record.data)[ROW_REF_FIELD];
      if (rowRef) {
        return { index, rowRef };
      } else {
        return null;
      }
    })
    .filter((x) => x !== null);

  if (rowRefRecords.length === 0) {
    return records.map((r) => r.data);
  }

  const realtimeWalBucket = BRAINSTORE_REALTIME_WAL_BUCKET_NAME;
  if (!(brainstoreEnabled() && realtimeWalBucket)) {
    throw new Error("Should not have any row refs if realtime is disabled");
  }

  const objectStore = await makeObjectStore();
  // We could coalesce the ranges we read to minimize network requests, but
  // since this function is currently only used for reading merge row data,
  // which we assume to be a small number of rows, it may not be worthwhile.
  const rowRefPromises = await Promise.all(
    rowRefRecords.map(async ({ index, rowRef }) => {
      const getResult = await objectStore.get({
        bucket: realtimeWalBucket,
        key: rowRef.key,
        range: {
          offset: rowRef.byte_range_start,
          length: rowRef.byte_range_end - rowRef.byte_range_start,
        },
      });
      const bodyString = await readableToString(getResult.stream);
      const body = (() => {
        try {
          return z.record(z.unknown()).parse(JSON.parse(bodyString));
        } catch (e) {
          const errorMessage = e instanceof Error ? e.message : String(e);
          throw new Error(
            `Error parsing body at rowRef ${JSON.stringify(rowRef)}: ${errorMessage}`,
          );
        }
      })();
      const objectIds = records[index].objectIds;
      const logicalToStorageMap = getLogicalToStorageMap(
        objectIds[OBJECT_TYPE_FIELD],
      );
      Object.entries(logicalToStorageMap).forEach(([k, v]) => {
        if (k in body) {
          body[v] = body[k];
          delete body[k];
        }
      });
      return { index, body };
    }),
  );
  const rowRefResults = await Promise.all(rowRefPromises);
  const outRecords = records.map((r) => r.data);
  rowRefResults.forEach(({ index, body }) => {
    outRecords[index] = body;
  });
  return outRecords;
}
