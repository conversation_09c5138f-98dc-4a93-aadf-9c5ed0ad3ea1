import { Parser } from "#/parser";
import { LogicalSchema, ScalarType } from "#/schema";
import { expect, test } from "vitest";
import { bindExpr, bindQuery } from "./bind";
import { getExprScalarType } from "./types";

test("bind liters", () => {
  const cases: {
    literal: string;
    expected: ScalarType;
  }[] = [
    { literal: "true", expected: "boolean" },
    { literal: "false", expected: "boolean" },
    { literal: "2", expected: "integer" },
    // This is sort of a quirk of Javascript. parseFloat(2.0) == 2
    { literal: "2.0", expected: "integer" },
    { literal: "2.1", expected: "number" },
    { literal: "'a'", expected: "string" },
    { literal: "null", expected: "null" },
  ];

  for (const { literal, expected } of cases) {
    const parsed = new Parser(literal).parseExpr();
    const bound = bindExpr(
      {
        schema: {},
        queryText: literal,
        scope: {},
        bindingMeasures: false,
        skipFieldCasts: false,
      },
      parsed,
    );
    expect(bound.op).toBe("literal");
    if (bound.op !== "literal") continue;
    expect(bound.type).toBe(expected);
  }
});

const NumericSchema: LogicalSchema = {
  type: "object",
  properties: {
    a: { type: "boolean" },
    b: { type: "integer" },
    c: { type: "number" },
  },
};

test("bind numeric comparisons", () => {
  const cases = [
    { expr: "a = a", expected: "boolean" },
    { expr: "a = b", expected: "integer" },
    { expr: "a = c", expected: "number" },
    { expr: "b = a", expected: "integer" },
    { expr: "b = b", expected: "integer" },
    { expr: "b = c", expected: "number" },
    { expr: "c = a", expected: "number" },
    { expr: "c = b", expected: "number" },
    { expr: "c = c", expected: "number" },
  ];

  for (const { expr, expected } of cases) {
    const parsed = new Parser(expr).parseExpr();
    const bound = bindExpr(
      {
        schema: NumericSchema,
        queryText: expr,
        scope: {},
        bindingMeasures: false,
        skipFieldCasts: false,
      },
      parsed,
    );

    if (!("left" in bound) || !("right" in bound)) {
      throw new Error("expected binary op: " + JSON.stringify(bound));
    }

    expect(getExprScalarType(bound.left)).toBe(expected);
    expect(getExprScalarType(bound.right)).toBe(expected);
  }
});

test("bind numeric functions", () => {
  const cases = [
    { expr: "sum(a)", expected: "integer" },
    { expr: "sum(b)", expected: "integer" },
    { expr: "sum(c)", expected: "number" },
    { expr: "COALESCE(b, c)", expected: "number" },
    { expr: "COALESCE(0, b)", expected: "integer" },
    { expr: "COALESCE(b, 0)", expected: "integer" },
    { expr: "COALESCE(0, c)", expected: "number" },
    { expr: "COALESCE(c, 0)", expected: "number" },
    { expr: "COALESCE(NULL, 0)", expected: "integer" },
    { expr: "a + 1", expected: "integer" },
    { expr: "a + 1.1", expected: "number" },
    { expr: "a * 2", expected: "integer" },
    { expr: "a / 2", expected: "number" },
    { expr: "a - 1", expected: "integer" },
    { expr: "b + 1", expected: "integer" },
    { expr: "b + 1.0", expected: "integer" },
    { expr: "b + 1.1", expected: "number" },
    { expr: "b * 2", expected: "integer" },
    { expr: "b / 2", expected: "number" },
    { expr: "b - 1", expected: "integer" },
    { expr: "b - 1.0", expected: "integer" },
    { expr: "to_string(a)", expected: "string" },
    { expr: "to_string(b)", expected: "string" },
    { expr: "to_string(c)", expected: "string" },
    { expr: "to_string(123)", expected: "string" },
    { expr: "to_string(true)", expected: "string" },
    { expr: "to_number(a)", expected: "number" },
    { expr: "to_number(b)", expected: "number" },
    { expr: "to_number(c)", expected: "number" },
    { expr: "to_number(123)", expected: "number" },
    { expr: "to_number(true)", expected: "number" },
    { expr: "to_integer(a)", expected: "integer" },
    { expr: "to_integer(b)", expected: "integer" },
    { expr: "to_integer(c)", expected: "integer" },
    { expr: "to_integer(123)", expected: "integer" },
    { expr: "to_integer(true)", expected: "integer" },
    { expr: "to_boolean(a)", expected: "boolean" },
    { expr: "to_boolean(b)", expected: "boolean" },
    { expr: "to_boolean(c)", expected: "boolean" },
    { expr: "to_boolean(123)", expected: "boolean" },
    { expr: "to_boolean(true)", expected: "boolean" },
    { expr: "to_date(a)", expected: "date" },
    { expr: "to_date(b)", expected: "date" },
    { expr: "to_date(c)", expected: "date" },
    { expr: "to_date(123)", expected: "date" },
    { expr: "to_date(true)", expected: "date" },
    { expr: "to_datetime(a)", expected: "datetime" },
    { expr: "to_datetime(b)", expected: "datetime" },
    { expr: "to_datetime(c)", expected: "datetime" },
    { expr: "to_datetime(123)", expected: "datetime" },
    { expr: "to_datetime(true)", expected: "datetime" },
  ];

  for (const { expr, expected } of cases) {
    const parsed = new Parser(expr).parseExpr();
    const bound = bindExpr(
      {
        schema: NumericSchema,
        queryText: expr,
        scope: {},
        bindingMeasures: true,
        skipFieldCasts: false,
      },
      parsed,
    );

    expect(getExprScalarType(bound)).toBe(expected);
  }
});

test("bind timestamp functions", () => {
  const cases: { expr: string; expected: ScalarType }[] = [
    { expr: "now()", expected: "datetime" },
    { expr: "NOW()", expected: "datetime" },
    { expr: "current_timestamp", expected: "datetime" },
    { expr: "CURRENT_TIMESTAMP", expected: "datetime" },
    { expr: "current_date", expected: "date" },
    { expr: "current_date + interval 1 day", expected: "date" },
    { expr: "current_timestamp + interval 1 day", expected: "datetime" },
    { expr: "current_date > current_timestamp", expected: "boolean" },
  ];

  for (const { expr, expected } of cases) {
    const parsed = new Parser(expr).parseExpr();
    const bound = bindExpr(
      {
        schema: {
          type: "object",
          properties: {
            a: { type: "integer" },
          },
        },
        queryText: expr,
        scope: {},
        bindingMeasures: true,
        skipFieldCasts: false,
      },
      parsed,
    );

    console.log(expr, bound);
    expect(bound).toMatchSnapshot();
    expect(getExprScalarType(bound)).toBe(expected);
  }

  const bound = bindExpr(
    {
      schema: {
        type: "object",
        properties: {
          current_date: { type: "integer" },
        },
      },
      queryText: "current_date",
      scope: {},
      bindingMeasures: true,
      skipFieldCasts: false,
    },
    new Parser("current_date").parseExpr(),
  );
  expect(bound).toMatchSnapshot();
  expect(getExprScalarType(bound)).toBe("integer");
});

test("bind IN operator", () => {
  const parsed = new Parser("a IN [1, 2, 3]").parseExpr();
  const bound = bindExpr(
    {
      schema: NumericSchema,
      queryText: "a IN [1, 2, 3]",
      scope: {},
      bindingMeasures: true,
      skipFieldCasts: false,
    },
    parsed,
  );
  expect(bound).toMatchSnapshot();
});

test("bind NOT IN operator", () => {
  const parsed = new Parser("a NOT IN [1, 2, 3]").parseExpr();
  const bound = bindExpr(
    {
      schema: NumericSchema,
      queryText: "a NOT IN [1, 2, 3]",
      scope: {},
      bindingMeasures: true,
      skipFieldCasts: false,
    },
    parsed,
  );
  expect(bound).toMatchSnapshot();
});

test("bind sample with fields", () => {
  const parsed = new Parser("select: * | sample: 50% seed 123").parseQuery();
  const schema: LogicalSchema = {
    type: "array",
    items: {
      type: "object",
      properties: {
        category: { type: "string" },
        region: { type: "string" },
        user_id: { type: "string" },
      },
    },
  };

  const bound = bindQuery({
    query: parsed,
    schema,
    queryText: "select: * | sample: 50%",
  });

  expect(bound.sample).toBeDefined();
  expect(bound.sample?.method.type).toBe("rate");
  expect(bound.sample?.method.value).toBe(0.5);
});
