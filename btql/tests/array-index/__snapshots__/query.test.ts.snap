// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`filter by array index 1`] = `
{
  "bound": {
    "cursor": undefined,
    "dimensions": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "first_user",
        "expr": {
          "name": [
            "metadata",
            "users",
            0,
          ],
          "op": "field",
          "type": {},
        },
      },
      {
        "alias": "some_char",
        "expr": {
          "name": [
            "metadata",
            "users",
            -2,
            1,
          ],
          "op": "field",
          "type": {},
        },
      },
      {
        "alias": "foo",
        "expr": {
          "name": [
            "metadata",
            "users",
            2,
            "foo",
            1,
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "filter": null,
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "measures": [],
    "pivot": [],
    "sample": null,
    "sort": [
      {
        "alias": "id",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "dimensions": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 15,
              "line": 4,
            },
            "start": {
              "col": 13,
              "line": 4,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "first_user",
        "expr": {
          "loc": {
            "end": {
              "col": 30,
              "line": 5,
            },
            "start": {
              "col": 13,
              "line": 5,
            },
          },
          "name": [
            "metadata",
            "users",
            0,
          ],
          "op": "ident",
        },
      },
      {
        "alias": "some_char",
        "expr": {
          "loc": {
            "end": {
              "col": 34,
              "line": 6,
            },
            "start": {
              "col": 13,
              "line": 6,
            },
          },
          "name": [
            "metadata",
            "users",
            -2,
            1,
          ],
          "op": "ident",
        },
      },
      {
        "alias": "foo",
        "expr": {
          "loc": {
            "end": {
              "col": 37,
              "line": 7,
            },
            "start": {
              "col": 13,
              "line": 7,
            },
          },
          "name": [
            "metadata",
            "users",
            2,
            "foo",
            1,
          ],
          "op": "ident",
        },
      },
    ],
    "from": {
      "loc": {
        "end": {
          "col": 19,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 8,
            },
            "start": {
              "col": 17,
              "line": 8,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 19,
            "line": 8,
          },
          "start": {
            "col": 17,
            "line": 8,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "first_user": {},
          "foo": {},
          "id": {
            "type": "string",
          },
          "some_char": {},
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "logs",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."users"[0]",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "first_user",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."users"[#-2][1]",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "some_char",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."users"[2]."foo"[1]",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "foo",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
GROUP BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "1",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "2",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "3",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "4",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "id",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "first_user": "austin",
      "foo": null,
      "id": "a",
      "some_char": "d",
    },
    {
      "first_user": "brad",
      "foo": "y",
      "id": "b",
      "some_char": "a",
    },
    {
      "first_user": "cory",
      "foo": "z",
      "id": "c",
      "some_char": "b",
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "first_user": {},
        "foo": {},
        "id": {
          "type": "string",
        },
        "some_char": {},
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`filter by array index 2`] = `
{
  "bound": {
    "cursor": undefined,
    "dimensions": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "foo_is_null",
        "expr": {
          "expr": {
            "name": [
              "metadata",
              "users",
              2,
              "foo",
              2,
            ],
            "op": "field",
            "type": {},
          },
          "op": "isnull",
        },
      },
    ],
    "filter": {
      "left": {
        "name": [
          "metadata",
          "users",
          1,
          -1,
        ],
        "op": "field",
        "type": {},
      },
      "op": "eq",
      "right": {
        "op": "literal",
        "type": "string",
        "value": "b",
      },
    },
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "measures": [],
    "pivot": [],
    "sample": null,
    "sort": [
      {
        "alias": "id",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "dimensions": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 15,
              "line": 4,
            },
            "start": {
              "col": 13,
              "line": 4,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "foo_is_null",
        "expr": {
          "expr": {
            "loc": {
              "end": {
                "col": 37,
                "line": 5,
              },
              "start": {
                "col": 13,
                "line": 5,
              },
            },
            "name": [
              "metadata",
              "users",
              2,
              "foo",
              2,
            ],
            "op": "ident",
          },
          "loc": {
            "end": {
              "col": 45,
              "line": 5,
            },
            "start": {
              "col": 13,
              "line": 5,
            },
          },
          "op": "isnull",
        },
      },
    ],
    "filter": {
      "left": {
        "loc": {
          "end": {
            "col": 40,
            "line": 6,
          },
          "start": {
            "col": 19,
            "line": 6,
          },
        },
        "name": [
          "metadata",
          "users",
          1,
          -1,
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 46,
          "line": 6,
        },
        "start": {
          "col": 19,
          "line": 6,
        },
      },
      "op": "eq",
      "right": {
        "loc": {
          "end": {
            "col": 46,
            "line": 6,
          },
          "start": {
            "col": 43,
            "line": 6,
          },
        },
        "op": "literal",
        "value": "b",
      },
    },
    "from": {
      "loc": {
        "end": {
          "col": 19,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 7,
            },
            "start": {
              "col": 17,
              "line": 7,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 19,
            "line": 7,
          },
          "start": {
            "col": 17,
            "line": 7,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "foo_is_null": {
            "type": "boolean",
          },
          "id": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "logs",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "((",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            ParameterizedSnippet {
                              "fragments": [
                                PlainSnippet {
                                  "text": "",
                                },
                                PlainSnippet {
                                  "text": "json_extract",
                                },
                                PlainSnippet {
                                  "text": "(",
                                },
                                Ident {
                                  "name": [
                                    "logs",
                                    "metadata",
                                  ],
                                },
                                PlainSnippet {
                                  "text": ", ",
                                },
                                "$."users"[2]."foo"[2]",
                                PlainSnippet {
                                  "text": ")",
                                },
                              ],
                            },
                            PlainSnippet {
                              "text": "::",
                            },
                            ParameterizedSnippet {
                              "fragments": [
                                PlainSnippet {
                                  "text": "json",
                                },
                              ],
                            },
                            PlainSnippet {
                              "text": "",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": " IS NULL",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ") OR (",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "TRY_CAST(",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            ParameterizedSnippet {
                              "fragments": [
                                PlainSnippet {
                                  "text": "",
                                },
                                PlainSnippet {
                                  "text": "json_extract",
                                },
                                PlainSnippet {
                                  "text": "(",
                                },
                                Ident {
                                  "name": [
                                    "logs",
                                    "metadata",
                                  ],
                                },
                                PlainSnippet {
                                  "text": ", ",
                                },
                                "$."users"[2]."foo"[2]",
                                PlainSnippet {
                                  "text": ")",
                                },
                              ],
                            },
                            PlainSnippet {
                              "text": "::",
                            },
                            ParameterizedSnippet {
                              "fragments": [
                                PlainSnippet {
                                  "text": "json",
                                },
                              ],
                            },
                            PlainSnippet {
                              "text": "",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": " AS JSON) IS NOT DISTINCT FROM 'null'::JSON",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "))",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "foo_is_null",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "(",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "json_extract_string",
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    Ident {
                      "name": [
                        "logs",
                        "metadata",
                      ],
                    },
                    PlainSnippet {
                      "text": ", ",
                    },
                    "$."users"[1][#-1]",
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " ",
                },
                RawSnippet {
                  "query": "=",
                },
                PlainSnippet {
                  "text": " ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    "b",
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ")",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
GROUP BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "1",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "2",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "id",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "foo_is_null": true,
      "id": "b",
    },
    {
      "foo_is_null": false,
      "id": "c",
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "foo_is_null": {
          "type": "boolean",
        },
        "id": {
          "type": "string",
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;
