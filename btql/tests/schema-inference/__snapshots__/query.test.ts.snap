// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`filtered schema inference 1`] = `
{
  "bound": {
    "cursor": undefined,
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "filter": null,
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "op": "literal",
              "type": "integer",
              "value": 1,
            },
          ],
          "name": "count",
          "op": "function",
        },
      },
    ],
    "pivot": [],
    "sample": null,
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "loc": {
            "end": {
              "col": 40,
              "line": 1,
            },
            "start": {
              "col": 26,
              "line": 1,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
      },
    ],
    "from": {
      "loc": {
        "end": {
          "col": 11,
          "line": 1,
        },
        "start": {
          "col": 7,
          "line": 1,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "loc": {
                "end": {
                  "col": 60,
                  "line": 1,
                },
                "start": {
                  "col": 59,
                  "line": 1,
                },
              },
              "op": "literal",
              "value": 1,
            },
          ],
          "loc": {
            "end": {
              "col": 61,
              "line": 1,
            },
            "start": {
              "col": 53,
              "line": 1,
            },
          },
          "name": {
            "loc": {
              "end": {
                "col": 58,
                "line": 1,
              },
              "start": {
                "col": 53,
                "line": 1,
              },
            },
            "name": [
              "count",
            ],
            "op": "ident",
          },
          "op": "function",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 84,
              "line": 1,
            },
            "start": {
              "col": 70,
              "line": 1,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 84,
            "line": 1,
          },
          "start": {
            "col": 70,
            "line": 1,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "count(1)": {
            "type": "integer",
          },
          "model": {
            "type": [
              "string",
              "integer",
            ],
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."model"",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "model",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "count",
                      ],
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            1,
                            PlainSnippet {
                              "text": "",
                            },
                          ],
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "count(1)",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
GROUP BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "1",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            PlainSnippet {
                              "text": "json_extract",
                            },
                            PlainSnippet {
                              "text": "(",
                            },
                            Ident {
                              "name": [
                                "logs",
                                "metadata",
                              ],
                            },
                            PlainSnippet {
                              "text": ", ",
                            },
                            "$."model"",
                            PlainSnippet {
                              "text": ")",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "json",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "count(1)": 1n,
      "model": "asdf",
    },
    {
      "count(1)": 1n,
      "model": "basdf",
    },
    {
      "count(1)": 1n,
      "model": 1,
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "count(1)": {
          "type": "integer",
        },
        "model": {
          "type": [
            "string",
            "integer",
          ],
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`filtered schema inference 2`] = `
{
  "bound": {
    "cursor": undefined,
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "filter": {
      "haystack": {
        "name": [
          "tags",
        ],
        "op": "field",
        "type": {
          "default": [],
          "items": {
            "type": "string",
          },
          "type": "array",
        },
      },
      "needle": {
        "op": "literal",
        "type": "string",
        "value": "string-model",
      },
      "op": "includes",
    },
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "op": "literal",
              "type": "integer",
              "value": 1,
            },
          ],
          "name": "count",
          "op": "function",
        },
      },
    ],
    "pivot": [],
    "sample": null,
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "loc": {
            "end": {
              "col": 40,
              "line": 1,
            },
            "start": {
              "col": 26,
              "line": 1,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
      },
    ],
    "filter": {
      "haystack": {
        "loc": {
          "end": {
            "col": 99,
            "line": 1,
          },
          "start": {
            "col": 95,
            "line": 1,
          },
        },
        "name": [
          "tags",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 123,
          "line": 1,
        },
        "start": {
          "col": 95,
          "line": 1,
        },
      },
      "needle": {
        "loc": {
          "end": {
            "col": 123,
            "line": 1,
          },
          "start": {
            "col": 109,
            "line": 1,
          },
        },
        "op": "literal",
        "value": "string-model",
      },
      "op": "includes",
    },
    "from": {
      "loc": {
        "end": {
          "col": 11,
          "line": 1,
        },
        "start": {
          "col": 7,
          "line": 1,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "loc": {
                "end": {
                  "col": 60,
                  "line": 1,
                },
                "start": {
                  "col": 59,
                  "line": 1,
                },
              },
              "op": "literal",
              "value": 1,
            },
          ],
          "loc": {
            "end": {
              "col": 61,
              "line": 1,
            },
            "start": {
              "col": 53,
              "line": 1,
            },
          },
          "name": {
            "loc": {
              "end": {
                "col": 58,
                "line": 1,
              },
              "start": {
                "col": 53,
                "line": 1,
              },
            },
            "name": [
              "count",
            ],
            "op": "ident",
          },
          "op": "function",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 84,
              "line": 1,
            },
            "start": {
              "col": 70,
              "line": 1,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 84,
            "line": 1,
          },
          "start": {
            "col": 70,
            "line": 1,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "count(1)": {
            "type": "integer",
          },
          "model": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."model"",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "model",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "count",
                      ],
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            1,
                            PlainSnippet {
                              "text": "",
                            },
                          ],
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "count(1)",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "json_contains(",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "logs",
                        "tags",
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "to_json((",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        "string-model",
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "varchar",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ")",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
GROUP BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "1",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            PlainSnippet {
                              "text": "json_extract",
                            },
                            PlainSnippet {
                              "text": "(",
                            },
                            Ident {
                              "name": [
                                "logs",
                                "metadata",
                              ],
                            },
                            PlainSnippet {
                              "text": ", ",
                            },
                            "$."model"",
                            PlainSnippet {
                              "text": ")",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "json",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "count(1)": 1n,
      "model": "asdf",
    },
    {
      "count(1)": 1n,
      "model": "basdf",
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "count(1)": {
          "type": "integer",
        },
        "model": {
          "type": "string",
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`filtered schema inference 3`] = `
{
  "bound": {
    "cursor": undefined,
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "filter": {
      "haystack": {
        "name": [
          "tags",
        ],
        "op": "field",
        "type": {
          "default": [],
          "items": {
            "type": "string",
          },
          "type": "array",
        },
      },
      "needle": {
        "op": "literal",
        "type": "string",
        "value": "number-model",
      },
      "op": "includes",
    },
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "op": "literal",
              "type": "integer",
              "value": 1,
            },
          ],
          "name": "count",
          "op": "function",
        },
      },
    ],
    "pivot": [],
    "sample": null,
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "loc": {
            "end": {
              "col": 40,
              "line": 1,
            },
            "start": {
              "col": 26,
              "line": 1,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
      },
    ],
    "filter": {
      "haystack": {
        "loc": {
          "end": {
            "col": 99,
            "line": 1,
          },
          "start": {
            "col": 95,
            "line": 1,
          },
        },
        "name": [
          "tags",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 123,
          "line": 1,
        },
        "start": {
          "col": 95,
          "line": 1,
        },
      },
      "needle": {
        "loc": {
          "end": {
            "col": 123,
            "line": 1,
          },
          "start": {
            "col": 109,
            "line": 1,
          },
        },
        "op": "literal",
        "value": "number-model",
      },
      "op": "includes",
    },
    "from": {
      "loc": {
        "end": {
          "col": 11,
          "line": 1,
        },
        "start": {
          "col": 7,
          "line": 1,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "loc": {
                "end": {
                  "col": 60,
                  "line": 1,
                },
                "start": {
                  "col": 59,
                  "line": 1,
                },
              },
              "op": "literal",
              "value": 1,
            },
          ],
          "loc": {
            "end": {
              "col": 61,
              "line": 1,
            },
            "start": {
              "col": 53,
              "line": 1,
            },
          },
          "name": {
            "loc": {
              "end": {
                "col": 58,
                "line": 1,
              },
              "start": {
                "col": 53,
                "line": 1,
              },
            },
            "name": [
              "count",
            ],
            "op": "ident",
          },
          "op": "function",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 84,
              "line": 1,
            },
            "start": {
              "col": 70,
              "line": 1,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 84,
            "line": 1,
          },
          "start": {
            "col": 70,
            "line": 1,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "count(1)": {
            "type": "integer",
          },
          "model": {
            "type": "integer",
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."model"",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "model",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "count",
                      ],
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            1,
                            PlainSnippet {
                              "text": "",
                            },
                          ],
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "count(1)",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "json_contains(",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "logs",
                        "tags",
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "to_json((",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        "number-model",
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "varchar",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ")",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
GROUP BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "1",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            PlainSnippet {
                              "text": "json_extract",
                            },
                            PlainSnippet {
                              "text": "(",
                            },
                            Ident {
                              "name": [
                                "logs",
                                "metadata",
                              ],
                            },
                            PlainSnippet {
                              "text": ", ",
                            },
                            "$."model"",
                            PlainSnippet {
                              "text": ")",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "json",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "count(1)": 1n,
      "model": 1,
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "count(1)": {
          "type": "integer",
        },
        "model": {
          "type": "integer",
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`unpivot schema inference 1`] = `
{
  "bound": {
    "cursor": undefined,
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
      {
        "alias": "score",
        "expr": {
          "name": [
            "score",
          ],
          "op": "field",
          "source": {
            "type": "key",
            "unpivot": 0,
          },
          "type": {
            "type": "string",
          },
        },
      },
    ],
    "filter": null,
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "op": "literal",
              "type": "integer",
              "value": 1,
            },
          ],
          "name": "count",
          "op": "function",
        },
      },
    ],
    "pivot": [],
    "sample": null,
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
      {
        "alias": "score",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [
      {
        "expr": {
          "name": [
            "scores",
          ],
          "op": "field",
          "type": {
            "additionalProperties": {
              "maximum": 1,
              "minimum": 0,
              "type": "number",
            },
            "default": {},
            "properties": {},
            "type": "object",
          },
        },
        "type": "object",
      },
    ],
  },
  "parsed": {
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "loc": {
            "end": {
              "col": 33,
              "line": 4,
            },
            "start": {
              "col": 19,
              "line": 4,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "score",
        "expr": {
          "loc": {
            "end": {
              "col": 40,
              "line": 4,
            },
            "start": {
              "col": 35,
              "line": 4,
            },
          },
          "name": [
            "score",
          ],
          "op": "ident",
        },
      },
    ],
    "from": {
      "loc": {
        "end": {
          "col": 17,
          "line": 2,
        },
        "start": {
          "col": 13,
          "line": 2,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "loc": {
                "end": {
                  "col": 24,
                  "line": 5,
                },
                "start": {
                  "col": 23,
                  "line": 5,
                },
              },
              "op": "literal",
              "value": 1,
            },
          ],
          "loc": {
            "end": {
              "col": 25,
              "line": 5,
            },
            "start": {
              "col": 17,
              "line": 5,
            },
          },
          "name": {
            "loc": {
              "end": {
                "col": 22,
                "line": 5,
              },
              "start": {
                "col": 17,
                "line": 5,
              },
            },
            "name": [
              "count",
            ],
            "op": "ident",
          },
          "op": "function",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 27,
              "line": 6,
            },
            "start": {
              "col": 13,
              "line": 6,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 27,
            "line": 6,
          },
          "start": {
            "col": 13,
            "line": 6,
          },
        },
      },
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 34,
              "line": 6,
            },
            "start": {
              "col": 29,
              "line": 6,
            },
          },
          "name": [
            "score",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 34,
            "line": 6,
          },
          "start": {
            "col": 29,
            "line": 6,
          },
        },
      },
    ],
    "unpivot": [
      {
        "alias": [
          "score",
          "value",
        ],
        "expr": {
          "loc": {
            "end": {
              "col": 22,
              "line": 3,
            },
            "start": {
              "col": 16,
              "line": 3,
            },
          },
          "name": [
            "scores",
          ],
          "op": "ident",
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "count(1)": {
            "type": "integer",
          },
          "model": {
            "type": [
              "string",
              "integer",
            ],
          },
          "score": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."model"",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "model",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "__unpivot_0",
                    "key",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "score",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "count",
                      ],
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            1,
                            PlainSnippet {
                              "text": "",
                            },
                          ],
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "count(1)",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "
FROM ",
                },
                Ident {
                  "name": [
                    "logs",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "logs",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": ", unnest(map_entries(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "to_json(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "scores_map",
                          ],
                        },
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::json::map(string, json))) AS ",
                    },
                    Ident {
                      "name": [
                        "__unpivot_0",
                      ],
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    Ident {
                      "name": [
                        "__unpivot_0",
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
GROUP BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "1",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "2",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            PlainSnippet {
                              "text": "json_extract",
                            },
                            PlainSnippet {
                              "text": "(",
                            },
                            Ident {
                              "name": [
                                "logs",
                                "metadata",
                              ],
                            },
                            PlainSnippet {
                              "text": ", ",
                            },
                            "$."model"",
                            PlainSnippet {
                              "text": ")",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "json",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "score",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "count(1)": 1n,
      "model": "asdf",
      "score": "score",
    },
    {
      "count(1)": 1n,
      "model": "basdf",
      "score": "score",
    },
    {
      "count(1)": 1n,
      "model": 1,
      "score": "score",
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "count(1)": {
          "type": "integer",
        },
        "model": {
          "type": [
            "string",
            "integer",
          ],
        },
        "score": {
          "type": "string",
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`unpivot schema inference 2`] = `
{
  "bound": {
    "cursor": undefined,
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "filter": null,
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "op": "literal",
              "type": "integer",
              "value": 1,
            },
          ],
          "name": "count",
          "op": "function",
        },
      },
    ],
    "pivot": [
      {
        "alias": "score",
        "expr": {
          "name": [
            "score",
          ],
          "op": "field",
          "source": {
            "type": "key",
            "unpivot": 0,
          },
          "type": {
            "type": "string",
          },
        },
      },
    ],
    "sample": null,
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "name": [
            "metadata",
            "model",
          ],
          "op": "field",
          "type": {},
        },
      },
      {
        "alias": "score",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [
      {
        "expr": {
          "name": [
            "scores",
          ],
          "op": "field",
          "type": {
            "additionalProperties": {
              "maximum": 1,
              "minimum": 0,
              "type": "number",
            },
            "default": {},
            "properties": {},
            "type": "object",
          },
        },
        "type": "object",
      },
    ],
  },
  "parsed": {
    "dimensions": [
      {
        "alias": "model",
        "expr": {
          "loc": {
            "end": {
              "col": 33,
              "line": 4,
            },
            "start": {
              "col": 19,
              "line": 4,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
      },
    ],
    "from": {
      "loc": {
        "end": {
          "col": 17,
          "line": 2,
        },
        "start": {
          "col": 13,
          "line": 2,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "measures": [
      {
        "alias": "count(1)",
        "expr": {
          "args": [
            {
              "loc": {
                "end": {
                  "col": 24,
                  "line": 6,
                },
                "start": {
                  "col": 23,
                  "line": 6,
                },
              },
              "op": "literal",
              "value": 1,
            },
          ],
          "loc": {
            "end": {
              "col": 25,
              "line": 6,
            },
            "start": {
              "col": 17,
              "line": 6,
            },
          },
          "name": {
            "loc": {
              "end": {
                "col": 22,
                "line": 6,
              },
              "start": {
                "col": 17,
                "line": 6,
              },
            },
            "name": [
              "count",
            ],
            "op": "ident",
          },
          "op": "function",
        },
      },
    ],
    "pivot": [
      {
        "alias": "score",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 5,
            },
            "start": {
              "col": 14,
              "line": 5,
            },
          },
          "name": [
            "score",
          ],
          "op": "ident",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 27,
              "line": 7,
            },
            "start": {
              "col": 13,
              "line": 7,
            },
          },
          "name": [
            "metadata",
            "model",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 27,
            "line": 7,
          },
          "start": {
            "col": 13,
            "line": 7,
          },
        },
      },
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 34,
              "line": 7,
            },
            "start": {
              "col": 29,
              "line": 7,
            },
          },
          "name": [
            "score",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 34,
            "line": 7,
          },
          "start": {
            "col": 29,
            "line": 7,
          },
        },
      },
    ],
    "unpivot": [
      {
        "alias": [
          "score",
          "value",
        ],
        "expr": {
          "loc": {
            "end": {
              "col": 22,
              "line": 3,
            },
            "start": {
              "col": 16,
              "line": 3,
            },
          },
          "name": [
            "scores",
          ],
          "op": "ident",
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "count(1)": {
            "type": "integer",
          },
          "model": {
            "type": [
              "string",
              "integer",
            ],
          },
          "score": {
            "additionalProperties": {
              "properties": {
                "count(1)": {
                  "type": "integer",
                },
              },
              "type": "object",
            },
            "properties": {
              "score": {
                "type": "object",
              },
            },
            "propertyNames": {
              "enum": [
                "score",
              ],
            },
            "type": "object",
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."model"",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "model",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "__unpivot_0",
                    "key",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "score",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "count",
                      ],
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            1,
                            PlainSnippet {
                              "text": "",
                            },
                          ],
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "count(1)",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "
FROM ",
                },
                Ident {
                  "name": [
                    "logs",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "logs",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": ", unnest(map_entries(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "to_json(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "scores_map",
                          ],
                        },
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::json::map(string, json))) AS ",
                    },
                    Ident {
                      "name": [
                        "__unpivot_0",
                      ],
                    },
                    PlainSnippet {
                      "text": "(",
                    },
                    Ident {
                      "name": [
                        "__unpivot_0",
                      ],
                    },
                    PlainSnippet {
                      "text": ")",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
GROUP BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "ROLLUP(",
                },
                ParameterizedSnippet {
                  "fragments": [
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "1",
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    RawSnippet {
                      "query": ", ",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "2",
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                  ],
                },
                PlainSnippet {
                  "text": ")",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
HAVING ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "GROUPING(",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        PlainSnippet {
                          "text": "json_extract",
                        },
                        PlainSnippet {
                          "text": "(",
                        },
                        Ident {
                          "name": [
                            "logs",
                            "metadata",
                          ],
                        },
                        PlainSnippet {
                          "text": ", ",
                        },
                        "$."model"",
                        PlainSnippet {
                          "text": ")",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "json",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ")=0",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "",
                            },
                            PlainSnippet {
                              "text": "json_extract",
                            },
                            PlainSnippet {
                              "text": "(",
                            },
                            Ident {
                              "name": [
                                "logs",
                                "metadata",
                              ],
                            },
                            PlainSnippet {
                              "text": ", ",
                            },
                            "$."model"",
                            PlainSnippet {
                              "text": ")",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "json",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "score",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "1",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                RawSnippet {
                  "query": ", ",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    PlainSnippet {
                      "text": "2",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "count(1)": 1n,
      "model": "asdf",
      "score": {
        "score": {
          "count(1)": 1n,
        },
      },
    },
    {
      "count(1)": 1n,
      "model": "basdf",
      "score": {
        "score": {
          "count(1)": 1n,
        },
      },
    },
    {
      "count(1)": 1n,
      "model": 1,
      "score": {
        "score": {
          "count(1)": 1n,
        },
      },
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "count(1)": {
          "type": "integer",
        },
        "model": {
          "type": [
            "string",
            "integer",
          ],
        },
        "score": {
          "additionalProperties": {
            "properties": {
              "count(1)": {
                "type": "integer",
              },
            },
            "type": "object",
          },
          "properties": {
            "score": {
              "type": "object",
            },
          },
          "propertyNames": {
            "enum": [
              "score",
            ],
          },
          "type": "object",
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;
