import { expect, test } from "vitest";
import { parse<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from ".";

test("parser corner cases", () => {
  const cases = [
    {
      query:
        "from: project('495d041a-7790-4907-b3c7-20ee864569cc') | select: scores.foo",
    },
    {
      query: 'select: a AS b, c AS "d"',
    },
    {
      query: `
      from: foo
      select:
        a,
        b,
        c,
      `,
    },
    {
      query: `
      from: foo
      select:
        a,
        b,
        c AS d,
      `,
    },
    {
      query: `
      from: foo
      select:
        a,
        b,
        c,
      filter
      `,
      error: true,
    },
    {
      query: `
      from: foo
      select:
        a,
        b,
        c,
      filter:
      `,
      error: true,
    },
    {
      query: `filter: a = {"a": 1}`,
    },
    {
      query: `filter: a = {'a': 1}`,
    },
    {
      query: `filter: a = {a: 1}`,
    },
    {
      query: `filter: a = "a"`,
    },
    {
      query: `filter: a = ["a"]`,
    },
    {
      query: `filter: a = ['a']`,
    },
    {
      query: `filter: "a" = ["a"]`,
    },
    {
      // Even though second is a keyword, it can be used as an identifier
      query: `filter: second = 1`,
    },
    {
      query: "filter: `name with space` > 5",
    },
    {
      query: "sample: 10%",
    },
    {
      query: "sample: 100",
    },
    {
      query: "sample: 50% seed 123",
    },
    {
      query: "sample: 1000 seed 456",
    },
    {
      query: "sample: 0.5",
      error: true,
    },
    {
      query: "sample: 100.5",
      error: true,
    },
  ];

  for (const { query, error } of cases) {
    console.log(query);

    let errored = false;
    try {
      const parsed = parseQuery(query);
      expect(parsed).toMatchSnapshot();
    } catch (e) {
      expect(e).toMatchSnapshot();
      errored = true;
    }
    expect(!!error).toBe(errored);
  }
});

test("literals", () => {
  const cases = [
    {
      query: "1",
      expected: 1,
    },
    {
      query: "1.5",
      expected: 1.5,
    },
    {
      query: "true",
      expected: true,
    },
    {
      query: "false",
      expected: false,
    },
    {
      query: "null",
      expected: null,
    },
    {
      query: "'hello'",
      expected: "hello",
    },
    {
      query: '"hello"',
      ast: {
        op: "literal",
        value: "hello",
      },
    },
    {
      query: '"hello"."world"',
      ast: {
        op: "ident",
        name: ["hello", "world"],
      },
    },
    {
      query: `['a', 1, true]`,
      expected: ["a", 1, true],
    },
    {
      query: `{"a": 1, "b": 2}`,
      expected: { a: 1, b: 2 },
    },
    {
      query: "interval 1 DAY",
      ast: {
        op: "interval",
        value: 1,
        unit: "day",
      },
    },
    {
      query: "50%",
      ast: {
        op: "literal",
        value: 0.5,
      },
    },
    {
      query: "50.3%",
      ast: {
        op: "literal",
        value: 0.503,
      },
    },
    {
      query: "0%",
      ast: {
        op: "literal",
        value: 0,
      },
    },
    {
      query: "100%",
      ast: {
        op: "literal",
        value: 1,
      },
    },
    {
      query: "interval 1.5 day",
      error: true,
    },
    {
      query: "expected is 'none'",
      error: true,
    },
    {
      query: "expected is 1",
      error: true,
    },
  ];

  for (const { query, expected, ast, error } of cases) {
    const parser = new Parser(query);
    let parseResult;
    try {
      const { loc: _, ...parsed } = parser.parseExpr();
      parseResult = parsed;
    } catch (e) {
      if (!error) {
        throw e;
      }
      expect(!!error).toBe(true);
      continue;
    }

    if (ast) {
      expect(parseResult).toEqual(ast);
    } else {
      expect(parseResult).toEqual({ op: "literal", value: expected });
    }
    expect(!!error).toBe(false);
  }
});

test("not ops", () => {
  const cases = [
    {
      query: "filter: metadata NOT LIKE '%hi%'",
      expected: {
        filter: {
          op: "not",
          expr: {
            op: "like",
            left: {
              op: "ident",
              name: ["metadata"],
            },
            right: {
              op: "literal",
              value: "%hi%",
            },
          },
        },
      },
    },
    {
      query: "filter: metadata NOT ILIKE '%hi%'",
      expected: {
        filter: {
          op: "not",
          expr: {
            op: "ilike",
            left: {
              op: "ident",
              name: ["metadata"],
            },
            right: {
              op: "literal",
              value: "%hi%",
            },
          },
        },
      },
    },
    {
      query: "filter: metadata NOT CONTAINS 'hi'",
      expected: {
        filter: {
          op: "not",
          expr: {
            op: "includes",
            haystack: {
              op: "ident",
              name: ["metadata"],
            },
            needle: {
              op: "literal",
              value: "hi",
            },
          },
        },
      },
    },
    {
      query: "filter: metadata NOT INCLUDES 'hi'",
      expected: {
        filter: {
          op: "not",
          expr: {
            op: "includes",
            haystack: {
              op: "ident",
              name: ["metadata"],
            },
            needle: {
              op: "literal",
              value: "hi",
            },
          },
        },
      },
    },
  ];
  for (const { query, expected } of cases) {
    const parsed = parseQuery(query);
    expect(parsed).toMatchObject(expected);
  }
});
