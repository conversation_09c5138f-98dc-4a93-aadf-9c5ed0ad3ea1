// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`parser corner cases 1`] = `
{
  "from": {
    "args": [
      {
        "loc": {
          "end": {
            "col": 53,
            "line": 1,
          },
          "start": {
            "col": 15,
            "line": 1,
          },
        },
        "op": "literal",
        "value": "495d041a-7790-4907-b3c7-20ee864569cc",
      },
    ],
    "loc": {
      "end": {
        "col": 54,
        "line": 1,
      },
      "start": {
        "col": 7,
        "line": 1,
      },
    },
    "name": {
      "loc": {
        "end": {
          "col": 14,
          "line": 1,
        },
        "start": {
          "col": 7,
          "line": 1,
        },
      },
      "name": [
        "project",
      ],
      "op": "ident",
    },
    "op": "function",
  },
  "select": [
    {
      "alias": "foo",
      "expr": {
        "loc": {
          "end": {
            "col": 75,
            "line": 1,
          },
          "start": {
            "col": 65,
            "line": 1,
          },
        },
        "name": [
          "scores",
          "foo",
        ],
        "op": "ident",
      },
    },
  ],
}
`;

exports[`parser corner cases 2`] = `
{
  "select": [
    {
      "alias": "b",
      "expr": {
        "loc": {
          "end": {
            "col": 10,
            "line": 1,
          },
          "start": {
            "col": 9,
            "line": 1,
          },
        },
        "name": [
          "a",
        ],
        "op": "ident",
      },
    },
    {
      "alias": ""d"",
      "expr": {
        "loc": {
          "end": {
            "col": 18,
            "line": 1,
          },
          "start": {
            "col": 17,
            "line": 1,
          },
        },
        "name": [
          "c",
        ],
        "op": "ident",
      },
    },
  ],
}
`;

exports[`parser corner cases 3`] = `
{
  "from": {
    "loc": {
      "end": {
        "col": 16,
        "line": 2,
      },
      "start": {
        "col": 13,
        "line": 2,
      },
    },
    "name": [
      "foo",
    ],
    "op": "ident",
  },
  "select": [
    {
      "alias": "a",
      "expr": {
        "loc": {
          "end": {
            "col": 10,
            "line": 4,
          },
          "start": {
            "col": 9,
            "line": 4,
          },
        },
        "name": [
          "a",
        ],
        "op": "ident",
      },
    },
    {
      "alias": "b",
      "expr": {
        "loc": {
          "end": {
            "col": 10,
            "line": 5,
          },
          "start": {
            "col": 9,
            "line": 5,
          },
        },
        "name": [
          "b",
        ],
        "op": "ident",
      },
    },
    {
      "alias": "c",
      "expr": {
        "loc": {
          "end": {
            "col": 10,
            "line": 6,
          },
          "start": {
            "col": 9,
            "line": 6,
          },
        },
        "name": [
          "c",
        ],
        "op": "ident",
      },
    },
  ],
}
`;

exports[`parser corner cases 4`] = `
{
  "from": {
    "loc": {
      "end": {
        "col": 16,
        "line": 2,
      },
      "start": {
        "col": 13,
        "line": 2,
      },
    },
    "name": [
      "foo",
    ],
    "op": "ident",
  },
  "select": [
    {
      "alias": "a",
      "expr": {
        "loc": {
          "end": {
            "col": 10,
            "line": 4,
          },
          "start": {
            "col": 9,
            "line": 4,
          },
        },
        "name": [
          "a",
        ],
        "op": "ident",
      },
    },
    {
      "alias": "b",
      "expr": {
        "loc": {
          "end": {
            "col": 10,
            "line": 5,
          },
          "start": {
            "col": 9,
            "line": 5,
          },
        },
        "name": [
          "b",
        ],
        "op": "ident",
      },
    },
    {
      "alias": "d",
      "expr": {
        "loc": {
          "end": {
            "col": 10,
            "line": 6,
          },
          "start": {
            "col": 9,
            "line": 6,
          },
        },
        "name": [
          "c",
        ],
        "op": "ident",
      },
    },
  ],
}
`;

exports[`parser corner cases 5`] = `[Error: Failed to parse filter at line 8, col 7 unexpected end of input]`;

exports[`parser corner cases 6`] = `[Error: Failed to parse primary expression at line 8, col 7 unexpected end of input]`;

exports[`parser corner cases 7`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 10,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "a",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 21,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 21,
          "line": 1,
        },
        "start": {
          "col": 13,
          "line": 1,
        },
      },
      "op": "literal",
      "value": {
        "a": 1,
      },
    },
  },
}
`;

exports[`parser corner cases 8`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 10,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "a",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 21,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 21,
          "line": 1,
        },
        "start": {
          "col": 13,
          "line": 1,
        },
      },
      "op": "literal",
      "value": {
        "a": 1,
      },
    },
  },
}
`;

exports[`parser corner cases 9`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 10,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "a",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 19,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 19,
          "line": 1,
        },
        "start": {
          "col": 13,
          "line": 1,
        },
      },
      "op": "literal",
      "value": {
        "a": 1,
      },
    },
  },
}
`;

exports[`parser corner cases 10`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 10,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "a",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 16,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 16,
          "line": 1,
        },
        "start": {
          "col": 13,
          "line": 1,
        },
      },
      "op": "literal",
      "value": "a",
    },
  },
}
`;

exports[`parser corner cases 11`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 10,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "a",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 18,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 18,
          "line": 1,
        },
        "start": {
          "col": 13,
          "line": 1,
        },
      },
      "op": "literal",
      "value": [
        "a",
      ],
    },
  },
}
`;

exports[`parser corner cases 12`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 10,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "a",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 18,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 18,
          "line": 1,
        },
        "start": {
          "col": 13,
          "line": 1,
        },
      },
      "op": "literal",
      "value": [
        "a",
      ],
    },
  },
}
`;

exports[`parser corner cases 13`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 12,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "op": "literal",
      "value": "a",
    },
    "loc": {
      "end": {
        "col": 20,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 20,
          "line": 1,
        },
        "start": {
          "col": 15,
          "line": 1,
        },
      },
      "op": "literal",
      "value": [
        "a",
      ],
    },
  },
}
`;

exports[`parser corner cases 14`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 15,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "second",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 19,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "eq",
    "right": {
      "loc": {
        "end": {
          "col": 19,
          "line": 1,
        },
        "start": {
          "col": 18,
          "line": 1,
        },
      },
      "op": "literal",
      "value": 1,
    },
  },
}
`;

exports[`parser corner cases 15`] = `
{
  "filter": {
    "left": {
      "loc": {
        "end": {
          "col": 26,
          "line": 1,
        },
        "start": {
          "col": 9,
          "line": 1,
        },
      },
      "name": [
        "name with space",
      ],
      "op": "ident",
    },
    "loc": {
      "end": {
        "col": 30,
        "line": 1,
      },
      "start": {
        "col": 9,
        "line": 1,
      },
    },
    "op": "gt",
    "right": {
      "loc": {
        "end": {
          "col": 30,
          "line": 1,
        },
        "start": {
          "col": 29,
          "line": 1,
        },
      },
      "op": "literal",
      "value": 5,
    },
  },
}
`;

exports[`parser corner cases 16`] = `
{
  "sample": {
    "method": {
      "type": "rate",
      "value": 0.1,
    },
  },
}
`;

exports[`parser corner cases 17`] = `
{
  "sample": {
    "method": {
      "type": "count",
      "value": 100,
    },
  },
}
`;

exports[`parser corner cases 18`] = `
{
  "sample": {
    "method": {
      "type": "rate",
      "value": 0.5,
    },
    "seed": 123,
  },
}
`;

exports[`parser corner cases 19`] = `
{
  "sample": {
    "method": {
      "type": "count",
      "value": 1000,
    },
    "seed": 456,
  },
}
`;

exports[`parser corner cases 20`] = `[Error: count sampling value must be >= 1, use % suffix for rates (e.g. 50%) at line 1, col 12 unexpected end of input]`;

exports[`parser corner cases 21`] = `[Error: count sampling value must be an integer at line 1, col 14 unexpected end of input]`;
