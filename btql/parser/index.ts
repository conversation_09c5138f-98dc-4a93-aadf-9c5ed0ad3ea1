import {
  AliasExpr,
  Expr,
  Function,
  Ident,
  IdentPiece,
  intervalUnitSchema,
  Literal,
  LiteralValue,
  Loc,
  ParsedQuery,
  Sample,
  shapeSchema,
  SortExpr,
  Star,
  UnpivotAliasExpr,
} from "./ast";
import { keywords, Token, TokenInfo, tokenize, Tokenizer } from "./lexer";

export * from "./ast";
export { reservedWords, keywords, Tokenizer, TokenizerError } from "./lexer";
export type * from "./lexer";

export class ParserError extends Error {
  constructor(
    public prefix: string,
    public expected: string | string[] | undefined,
    public line: number,
    public col: number,
    public suffix: string,
  ) {
    const expectedStr = Array.isArray(expected)
      ? expected.join(", ")
      : expected;
    super(
      `${prefix}${
        expectedStr ? " (expected '" + expectedStr + "')" : ""
      } at line ${line}, col ${col}${suffix ? ` ${suffix}` : ""}`,
    );
  }
}

export interface ParserOpts {
  maxExprDepth?: number;
}

export class Parser {
  private tokenizer: Tokenizer;
  private exprGuard: number = 0;
  private maxExprDepth: number;

  constructor(
    private query: string,
    opts: ParserOpts = {},
  ) {
    this.tokenizer = tokenize(query, { skipComments: true });
    this.maxExprDepth = opts.maxExprDepth || 100;
  }

  public parseQuery(): ParsedQuery {
    const clauses: ParsedQuery = {};
    while (true) {
      let token = this.tokenizer.nextToken();
      while (token && token.type === "pipe") {
        // Pipe is a syntactic sugar for a new clause
        token = this.tokenizer.nextToken();
      }
      if (!token) {
        break;
      }

      switch (token.type) {
        case "dimensions":
        case "measures":
        case "pivot":
          if (clauses[token.type]) {
            this.fail(`${token.type} already defined`);
          }
          this.mustReadToken(token.type, "colon");
          clauses[token.type] = this.parseList(
            this.parseAliasExpr.bind(this),
            "comma",
          );
          break;
        case "unpivot":
          if (clauses.unpivot) {
            this.fail(`${token.type} already defined`);
          }
          this.mustReadToken(token.type, "colon");
          clauses.unpivot = this.parseList(
            this.parseUnpivotAliasExpr.bind(this),
            "comma",
          );
          break;
        case "select":
          if (clauses.select) {
            this.fail(`${token.type} already defined`);
          }
          this.mustReadToken(token.type, "colon");
          clauses.select = this.parseList(
            this.parseAliasOrStar.bind(this),
            "comma",
          );
          break;
        case "infer":
          if (clauses.infer) {
            this.fail(`${token.type} already defined`);
          }
          this.mustReadToken(token.type, "colon");
          clauses.infer = this.parseList(
            this.parseIdentOrStar.bind(this),
            "comma",
          );
          break;
        case "filter":
          if (clauses.filter) {
            this.fail("filter already defined");
          }
          this.mustReadToken("filter", "colon");
          clauses.filter = this.parseExpr();
          break;
        case "from":
          if (clauses.from) {
            this.fail("from already defined");
          }
          this.mustReadToken("from", "colon");
          const fromExpr = this.parseIdentOrFunction();
          clauses.from =
            fromExpr.op === "literal" ? coerceToIdent(fromExpr) : fromExpr;

          if (clauses.from?.op === "function") {
            const nextToken = this.tokenizer.peekToken();
            const shapeValue = shapeSchema.safeParse(nextToken?.value);
            if (shapeValue.success) {
              this.tokenizer.nextToken();
              clauses.from.shape = shapeValue.data;
            }
          }
          break;
        case "sort":
          if (clauses.sort) {
            this.fail("sort already defined");
          }
          this.mustReadToken("sort", "colon");
          clauses.sort = this.parseList(this.parseSortExpr.bind(this), "comma");
          break;
        case "limit":
          if (clauses.limit) {
            this.fail("limit already defined");
          }
          this.mustReadToken("limit", "colon");
          const limitToken = this.mustReadToken("limit", "number");
          if (!isInt(limitToken.value)) {
            this.fail("limit must be an integer");
          }
          clauses.limit = parseInt(limitToken.value);
          break;
        case "cursor":
          if (clauses.cursor) {
            this.fail("cursor already defined");
          }
          this.mustReadToken("cursor", "colon");
          const cursorToken = this.mustReadToken("cursor");
          if (
            cursorToken.type !== "singleQuotedString" &&
            cursorToken.type !== "doubleQuotedString" &&
            cursorToken.type !== "word"
          ) {
            this.fail("cursor must be a string");
          }
          clauses.cursor = stripQuotes(
            cursorToken.value,
            cursorToken.type === "singleQuotedString" ? "'" : '"',
          );
          break;
        case "comparison_key":
          if (clauses.comparison_key) {
            this.fail("comparison_key already defined");
          }
          this.mustReadToken("comparison_key", "colon");
          clauses.comparison_key = this.parseExpr();
          break;
        case "weighted_scores":
          if (clauses.weighted_scores) {
            this.fail("weighted_scores already defined");
          }
          this.mustReadToken("weighted_scores", "colon");
          clauses.weighted_scores = this.parseList(
            this.parseAliasExpr.bind(this),
            "comma",
          );
          break;
        case "custom_columns":
          if (clauses.custom_columns) {
            this.fail("custom_columns already defined");
          }
          this.mustReadToken("custom_columns", "colon");
          clauses.custom_columns = this.parseList(
            this.parseAliasExpr.bind(this),
            "comma",
          );
          break;
        case "preview_length":
          if (clauses.preview_length) {
            this.fail("preview_length already defined");
          }
          this.mustReadToken("preview_length", "colon");
          let negate = false;
          if (this.tokenizer.peekToken()?.type === "minus") {
            this.tokenizer.nextToken();
            negate = true;
          }
          const previewLengthToken = this.mustReadToken(
            "preview_length",
            "number",
          );
          if (!isInt(previewLengthToken.value)) {
            this.fail("preview_length must be an integer");
          }
          clauses.preview_length =
            parseInt(previewLengthToken.value) * (negate ? -1 : 1);
          break;
        case "inference_budget":
          if (clauses.inference_budget) {
            this.fail("inference_budget already defined");
          }
          this.mustReadToken("inference_budget", "colon");
          const inferenceBudgetToken = this.mustReadToken(
            "inference_budget",
            "number",
          );
          if (!isInt(inferenceBudgetToken.value)) {
            this.fail("inference_budget must be an integer");
          }
          clauses.inference_budget = parseInt(inferenceBudgetToken.value);
          break;
        case "sample":
          if (clauses.sample) {
            this.fail("sample already defined");
          }
          this.mustReadToken("sample", "colon");
          clauses.sample = this.parseSample();
          break;
        default:
          this.failOp(
            "query",
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            [
              "dimensions",
              "pivot",
              "unpivot",
              "measures",
              "select",
              "infer",
              "filter",
              "from",
              "sort",
              "limit",
              "cursor",
              "comparison_key",
              "weighted_scores",
              "custom_columns",
              "preview_length",
              "inference_budget",
              "sample",
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            ].filter((x) => !clauses[x as keyof typeof clauses]) as Token[],
          );
      }
    }

    return clauses;
  }

  public finished(): boolean {
    return this.tokenizer.eof;
  }

  private parseAliasExpr(): AliasExpr {
    const expr = this.parseExpr();
    if (!expr.loc) {
      this.fail("no location information for alias expression");
    }
    let alias = this.getSubstringFromLoc(expr.loc);
    if (expr.op === "ident") {
      const lastName = expr.name[expr.name.length - 1];
      if (typeof lastName === "string") {
        alias = lastName;
      }
    }
    const token = this.tokenizer.peekToken();
    if (token && token.type === "as") {
      this.tokenizer.nextToken();
      alias = this.parseAliasValue();
    }

    return { expr, alias };
  }

  // Add this new method to the Parser class
  private getSubstringFromLoc(loc: Loc): string {
    const lines = this.query.split("\n");
    if (loc.start.line === loc.end.line) {
      return lines[loc.start.line - 1].substring(
        loc.start.col - 1,
        loc.end.col - 1,
      );
    } else {
      const startLine = lines[loc.start.line - 1].substring(loc.start.col - 1);
      const middleLines = lines.slice(loc.start.line, loc.end.line - 1);
      const endLine = lines[loc.end.line - 1].substring(0, loc.end.col - 1);
      return [startLine, ...middleLines, endLine].join("\n");
    }
  }

  private parseAliasValue(): string {
    const aliasToken = this.tokenizer.nextToken();
    if (!aliasToken || !this.isIdentPart(aliasToken)) {
      this.failOp("alias expression", ["identifier"]);
    }
    return aliasToken.value;
  }

  private parseUnpivotAliasExpr(): UnpivotAliasExpr {
    const expr = this.parseExpr();
    if (!expr.loc) {
      this.fail("no location information for alias expression");
    }
    this.mustReadToken("unpivot alias expression", "as");
    const firstAliasToken = this.tokenizer.peekToken();
    if (!firstAliasToken) {
      this.failOp("unpivot alias expression", ["identifier"]);
    }
    let alias: string | [string, string];
    switch (firstAliasToken.type) {
      case "word":
      case "doubleQuotedString":
      case "backtickQuotedString":
        alias = this.parseAliasValue();
        break;
      case "lparen":
        this.mustReadToken("unpivot alias expression", "lparen");
        const firstAlias = this.parseAliasValue();
        this.mustReadToken("unpivot alias expression", "comma");
        const secondAlias = this.parseAliasValue();
        this.mustReadToken("unpivot alias expression", "rparen");
        alias = [firstAlias, secondAlias];
        break;
      default:
        this.failOp("unpivot alias expression", ["identifier"]);
    }

    return { expr, alias };
  }

  private parseAliasOrStar(): AliasExpr | Star {
    if (this.tokenizer.peekToken()?.type === "star") {
      this.mustReadToken("select", "star");
      return { op: "star" };
    }
    return this.parseAliasExpr();
  }

  private parseIdentOrStar(): Ident | Star {
    if (this.tokenizer.peekToken()?.type === "star") {
      this.mustReadToken("select", "star");
      return { op: "star" };
    }
    const ident = this.parseIdent({ allowInitialDoubleQuoted: true });
    if (ident.op !== "ident") {
      this.failOp("select", ["identifier", "star"]);
    }
    return ident;
  }

  public parseSortExpr(): SortExpr {
    const expr = this.parseExpr();
    const start = expr.loc?.start;
    let end = expr.loc?.end;

    let dir: SortExpr["dir"] = "asc";
    switch (this.tokenizer.peekToken()?.type) {
      case "asc":
      case "desc":
        const dirToken = this.tokenizer.nextToken()!;
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        dir = dirToken?.type as SortExpr["dir"];
        end = makeLoc(dirToken).end;
        break;
    }
    return {
      expr,
      dir: dir,
      loc:
        start && end
          ? {
              start,
              end,
            }
          : undefined,
    };
  }

  private parseSample(): Sample {
    const valueToken = this.mustReadToken("sample");
    let method: Sample["method"];

    if (valueToken.type === "percentNumber") {
      // Rate sampling: 50% -> 0.5
      const numStr = valueToken.value.slice(0, -1);
      const value = parseFloat(numStr) / 100;
      method = { type: "rate", value };
    } else if (valueToken.type === "number") {
      // Count sampling: 1000 -> 1000
      const value = parseFloat(valueToken.value);
      if (value < 1) {
        this.fail(
          "count sampling value must be >= 1, use % suffix for rates (e.g. 50%)",
        );
      }
      if (!isInt(valueToken.value)) {
        this.fail("count sampling value must be an integer");
      }
      method = { type: "count", value: parseInt(valueToken.value) };
    } else {
      this.failOp("sample", ["number", "percentNumber"]);
    }

    const sample: Sample = { method };

    // Parse seed if present
    this.parseSamplingMethod(sample);

    return sample;
  }

  private parseSamplingMethod(sample: Sample): void {
    const nextToken = this.tokenizer.peekToken();

    if (nextToken?.type === "seed") {
      this.tokenizer.nextToken();
      const seedToken = this.mustReadToken("sample seed", "number");
      const seedValue = parseInt(seedToken.value);
      if (!isInt(seedToken.value)) {
        this.fail("seed value must be an integer");
      }
      sample.seed = seedValue;
    }
  }

  private parseList<T>(parseChild: () => T, separator: Token): T[] {
    const list: T[] = [];
    while (true) {
      const snapshot = this.tokenizer.snapshot();
      try {
        list.push(parseChild());
      } catch {
        this.tokenizer.restore(snapshot);
        break;
      }
      const token = this.tokenizer.peekToken();
      if (!token || token.type !== separator) {
        break;
      }
      this.tokenizer.nextToken();
    }

    return list;
  }

  public parseExpr(): Expr {
    if (++this.exprGuard > this.maxExprDepth) {
      throw new Error("Maximum expression depth exceeded");
    }

    const ret = this.parseTernaryExpr();
    --this.exprGuard;

    return ret;
  }

  private parseTernaryExpr(): Expr {
    const expr = this.parseOrExpr();
    const start = expr.loc?.start;

    const peekToken = this.tokenizer.peekToken();
    if (peekToken?.type === "question") {
      this.tokenizer.nextToken();
      const thenExpr = this.parseOrExpr();
      this.mustReadToken("ternary expression", "colon");
      const elseExpr = this.parseTernaryExpr();
      const end = elseExpr.loc?.end;

      const conds = [{ cond: expr, then: thenExpr }];
      let finalElse = elseExpr;

      if (elseExpr.op === "if") {
        conds.push(...elseExpr.conds);
        finalElse = elseExpr.else;
      }

      return {
        op: "if",
        conds,
        else: finalElse,
        loc: start && end ? { start, end } : undefined,
      };
    }

    return expr;
  }

  private parseLeftAssocExpr(
    parseChild: () => Expr,
    token: Token | Token[],
    makeOp: (left: Expr, right: Expr, token: Token, loc?: Loc) => Expr,
  ): Expr {
    let expr = parseChild();
    const start = expr.loc?.start;
    const tokList = Array.isArray(token) ? token : [token];
    while (true) {
      const peekToken = this.tokenizer.peekToken();
      if (!peekToken || !tokList.includes(peekToken.type)) {
        break;
      }
      this.tokenizer.nextToken();
      const right = parseChild();
      const end = right.loc?.end;

      expr = makeOp(
        expr,
        right,
        peekToken.type,
        start && end ? { start, end } : undefined,
      );
    }

    return expr;
  }

  private parseRightAssocExpr<T extends Token>(
    parseChild: () => Expr,
    token: T | T[],
    notTokens: T[],
    makeOp: (
      left: Expr,
      right: Expr,
      not: boolean,
      token: T,
      loc?: Loc,
    ) => Expr,
  ): Expr {
    const expr = parseChild();
    const start = expr.loc?.start;
    const tokList = Array.isArray(token) ? token : [token];

    let opToken = this.tokenizer.peekToken();
    let not = false;
    if (notTokens.length > 0 && opToken && opToken.type === "not") {
      this.tokenizer.nextToken();
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      opToken = this.mustPeekToken("not op", ...(notTokens as Token[]));
      not = true;
    }
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    if (opToken && (tokList as Token[]).includes(opToken.type)) {
      this.tokenizer.nextToken();
      const right = this.parseRightAssocExpr(
        parseChild,
        token,
        notTokens,
        makeOp,
      );
      const end = right.loc?.end;
      return makeOp(
        expr,
        right,
        not,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        opToken.type as T,
        start && end ? { start, end } : undefined,
      );
    }

    return expr;
  }

  public parseOrExpr(): Expr {
    return this.parseLeftAssocExpr(
      this.parseAndExpr.bind(this),
      "or",
      (left, right, _, loc) => ({
        op: "or",
        children: [left, right],
        loc,
      }),
    );
  }

  public parseAndExpr(): Expr {
    return this.parseLeftAssocExpr(
      this.parseNotExpr.bind(this),
      "and",
      (left, right, _, loc) => ({
        op: "and",
        children: [left, right],
        loc,
      }),
    );
  }

  public parseNotExpr(): Expr {
    if (this.tokenizer.peekToken()?.type === "not") {
      const token = this.tokenizer.nextToken();
      const expr = this.parseNotExpr();

      const start = makeLoc(token!).start;
      const end = expr.loc?.end;

      return {
        op: "not",
        expr,
        loc: end ? { start, end } : undefined,
      };
    }

    return this.parseEqualityExpr();
  }

  public parseEqualityExpr(): Expr {
    return this.parseRightAssocExpr(
      this.parseInequalityExpr.bind(this),
      [
        "equalOp",
        "notEqualOp",
        "doubleAngle",
        "includes",
        "contains",
        "like",
        "ilike",
        "match",
        "in",
      ],
      ["like", "ilike", "contains", "includes", "match", "in"],
      (left, right, not, token, loc) => {
        const op: Expr =
          token === "equalOp"
            ? {
                op: "eq",
                left,
                right,
                loc,
              }
            : token === "notEqualOp" || token === "doubleAngle"
              ? {
                  op: "ne",
                  left,
                  right,
                  loc,
                }
              : token === "like" ||
                  token === "ilike" ||
                  token === "match" ||
                  token === "in"
                ? {
                    op: token,
                    left,
                    right,
                    loc,
                  }
                : {
                    op: "includes",
                    haystack: left,
                    needle: right,
                    loc,
                  };
        return not ? { op: "not", expr: op } : op;
      },
    );
  }

  public parseInequalityExpr(): Expr {
    return this.parseRightAssocExpr(
      this.parseIsExpr.bind(this),
      ["lessThan", "lessThanOrEqual", "greaterThan", "greaterThanOrEqual"],
      [],
      (left, right, _not, token, loc) => ({
        op:
          token === "lessThan"
            ? "lt"
            : token === "lessThanOrEqual"
              ? "le"
              : token === "greaterThan"
                ? "gt"
                : "ge",
        left,
        right,
        loc,
      }),
    );
  }

  public parseIsExpr(): Expr {
    const expr = this.parseAddExpr();
    const start = expr.loc?.start;

    const token = this.tokenizer.peekToken();
    if (token?.type === "is") {
      this.tokenizer.nextToken();
      if (this.tokenizer.peekToken()?.type === "not") {
        this.tokenizer.nextToken();
        const lastToken = this.mustReadToken("is not", "null");
        return {
          op: "isnotnull",
          expr,
          loc: start ? { start, end: makeLoc(lastToken).end } : undefined,
        };
      } else {
        const lastToken = this.mustReadToken("is", "null");
        return {
          op: "isnull",
          expr,
          loc: start ? { start, end: makeLoc(lastToken).end } : undefined,
        };
      }
    }

    return expr;
  }

  public parseAddExpr(): Expr {
    return this.parseLeftAssocExpr(
      this.parseMulExpr.bind(this),
      ["plus", "minus"],
      (left, right, token, loc) => ({
        op: token === "plus" ? "add" : "sub",
        left,
        right,
        loc,
      }),
    );
  }

  public parseMulExpr(): Expr {
    return this.parseLeftAssocExpr(
      this.parseUnaryExpr.bind(this),
      ["star", "slash", "percent"],
      (left, right, token, loc) => ({
        op: token === "star" ? "mul" : token === "slash" ? "div" : "mod",
        left,
        right,
        loc,
      }),
    );
  }

  public parseUnaryExpr(): Expr {
    if (this.tokenizer.peekToken()?.type === "minus") {
      const token = this.tokenizer.nextToken();
      const start = makeLoc(token!).start;
      const expr = this.parseUnaryExpr();
      return {
        op: "neg",
        expr,
        loc: expr.loc ? { start, end: expr.loc.end } : undefined,
      };
    } else if (this.tokenizer.peekToken()?.type === "plus") {
      this.tokenizer.nextToken();
      return this.parseUnaryExpr();
    }

    return this.parsePrimaryExpr(true);
  }

  public parsePrimaryExpr(allowExprRecursion: boolean): Expr {
    const token = this.tokenizer.nextToken();
    if (!token) {
      this.failOp("primary expression");
    }

    switch (token.type) {
      case "true":
        return {
          op: "literal",
          value: true,
          loc: makeLoc(token),
        };
      case "false":
        return {
          op: "literal",
          value: false,
          loc: makeLoc(token),
        };
      case "null":
        return {
          op: "literal",
          value: null,
          loc: makeLoc(token),
        };
      case "star":
        return {
          op: "star",
          loc: makeLoc(token),
        };
      case "number":
      case "percentNumber":
        const numStr =
          token?.type === "percentNumber"
            ? token.value.slice(0, -1)
            : token.value;
        const numValue = isInt(numStr) ? parseInt(numStr) : parseFloat(numStr);
        return {
          op: "literal",
          value: token.type === "percentNumber" ? numValue / 100 : numValue,
          loc: makeLoc(token),
        };
      case "singleQuotedString":
        // Strip quotes
        return {
          op: "literal",
          value: stripQuotes(token.value, "'"),
          loc: makeLoc(token),
        };
      case "lsquare":
        const peekToken = this.tokenizer.peekToken();
        if (peekToken?.type === "rsquare") {
          this.tokenizer.nextToken();
          return {
            op: "literal",
            value: [],
            loc: { start: makeLoc(token).start, end: makeLoc(peekToken).end },
          };
        }
        return {
          op: "literal",
          value: this.parseList(this.parseLiteral.bind(this), "comma"),
          loc: {
            start: makeLoc(token).start,
            end: makeLoc(this.mustReadToken("list", "rsquare")).end,
          },
        };
      case "lcurly": {
        const peekToken = this.tokenizer.peekToken();
        if (peekToken?.type === "rcurly") {
          this.tokenizer.nextToken();
          return {
            op: "literal",
            value: {},
            loc: { start: makeLoc(token).start, end: makeLoc(peekToken).end },
          };
        }
        return {
          op: "literal",
          value: Object.fromEntries(
            this.parseList(this.parseObjectPair.bind(this), "comma"),
          ),
          loc: {
            start: makeLoc(token).start,
            end: makeLoc(this.mustReadToken("object", "rcurly")).end,
          },
        };
      }
      case "interval":
        const value = this.mustReadToken("interval", "number");
        if (!isInt(value.value)) {
          this.fail("interval value must be an integer");
        }
        const unit = this.mustReadToken("interval");
        const parsedUnit = intervalUnitSchema.safeParse(
          unit.value.toLowerCase(),
        );
        if (parsedUnit.success !== true) {
          this.fail("invalid interval unit");
        }

        return {
          op: "interval",
          value: parseInt(value.value),
          unit: parsedUnit.data,
          loc: { start: makeLoc(token).start, end: makeLoc(unit).end },
        };
      case "lparen":
        if (!allowExprRecursion) {
          this.fail("Cannot parse nested expressions in this context");
        }
        const expr = this.parseExpr();
        this.mustReadToken("primary expression", "rparen");
        return expr;
    }

    if (this.isIdentPart(token)) {
      return this.parseIdentOrFunction(token);
    }
    this.failOp("primary expression", ["number", "singleQuotedString"]);
  }

  private parseLiteral(): LiteralValue {
    const ret = this.parsePrimaryExpr(false);
    if (ret.op !== "literal") {
      this.fail("literal");
    }
    return ret.value;
  }

  private parseObjectPair(): [string, LiteralValue] {
    const key = this.parsePrimaryExpr(false);

    let name;
    if (key.op === "ident") {
      if (key.name.length !== 1) {
        this.fail("object key must be a single identifier");
      }
      if (typeof key.name[0] !== "string") {
        this.fail("object key must be a string");
      }
      name = key.name[0];
    } else if (key.op === "literal") {
      if (typeof key.value !== "string") {
        this.fail("object key must be a string");
      }
      name = key.value;
    } else {
      this.fail("object key must be a string or identifier");
    }
    this.mustReadToken("object pair", "colon");
    const value = this.parseLiteral();
    return [name, value];
  }

  private isIdentPart(token: TokenInfo): boolean {
    return [
      "word",
      "doubleQuotedString",
      "backtickQuotedString",
      ...keywords,
    ].includes(token.type);
  }

  private parseIdentPiece(): { piece: IdentPiece; endToken: TokenInfo } {
    const startTokenType = this.tokenizer.nextToken()?.type;
    const nextToken = this.mustReadToken("primary expression");
    if (!nextToken) {
      this.failOp("primary expression", ["identifier"]);
    }

    if (startTokenType === "period") {
      if (!this.isIdentPart(nextToken)) {
        this.failOp("primary expression", ["identifier"]);
      }
      return {
        piece: stripIfDoubleOrBacktickQuoted(nextToken),
        endToken: nextToken,
      };
    } else if (startTokenType === "lsquare") {
      const isNegativeIndex = nextToken.type === "minus";
      const numericToken = isNegativeIndex
        ? this.mustReadToken("primary expression", "number")
        : nextToken;

      if (numericToken.type !== "number" || !isInt(numericToken.value)) {
        this.failOp("primary expression", ["identifier"]);
      }

      const intValue = parseInt(numericToken.value);

      return {
        piece: isNegativeIndex ? -intValue : intValue,
        endToken: this.mustReadToken("identifier", "rsquare"),
      };
    } else {
      throw new Error("Expected ident piece to begin with period or lsquare");
    }
  }

  public parseIdent({
    initial,
    allowInitialDoubleQuoted,
  }: {
    initial?: TokenInfo;
    allowInitialDoubleQuoted?: boolean;
  }): Ident | Literal {
    if (!initial) {
      initial = this.mustReadToken("identifier");
    }

    if (!this.isIdentPart(initial)) {
      this.failOp("ident", ["word"]);
    }

    const name: IdentPiece[] = [stripIfDoubleOrBacktickQuoted(initial)];
    const start = makeLoc(initial).start;
    let end = makeLoc(initial).end;
    if (isPeriodOrLsquare(this.tokenizer.peekToken()?.type)) {
      while (isPeriodOrLsquare(this.tokenizer.peekToken()?.type)) {
        const { piece, endToken } = this.parseIdentPiece();
        name.push(piece);
        end = makeLoc(endToken).end;
      }
    } else if (
      initial.type === "doubleQuotedString" &&
      !allowInitialDoubleQuoted
    ) {
      // A single double-quoted string is treated as a literal.
      return {
        op: "literal",
        value: name[0],
        loc: makeLoc(initial),
      };
    }
    return {
      op: "ident" as const,
      name: name,
      loc: { start, end },
    };
  }

  private parseIdentOrFunction(
    initial?: TokenInfo,
  ): Ident | Literal | Function {
    const ident = this.parseIdent({ initial });

    if (this.tokenizer.peekToken()?.type === "lparen") {
      this.tokenizer.nextToken();

      let args: Expr[] = [];
      if (this.tokenizer.peekToken()?.type !== "rparen") {
        args = this.parseList(this.parseExpr.bind(this), "comma");
      }
      const lastToken = this.mustReadToken("function call", "rparen");
      return {
        op: "function",
        name: coerceToIdent(ident),
        args,
        loc: { start: ident.loc!.start, end: makeLoc(lastToken).end },
      };
    } else {
      return ident;
    }
  }

  private mustReadToken(operator: string, type?: Token): TokenInfo {
    const token = this.tokenizer.nextToken();
    if (!token) {
      this.failOp(operator);
    } else if (type && token.type !== type) {
      this.failOp(operator, type);
    }

    return token;
  }

  private mustPeekToken(operator: string, ...types: Token[]): TokenInfo {
    const token = this.tokenizer.peekToken();
    if (!token) {
      this.failOp(operator);
    } else if (types.length > 0 && !types.includes(token.type)) {
      this.failOp(operator, types);
    }

    return token;
  }

  private fail(message: string, expected?: string | string[]): never {
    if (this.tokenizer.eof) {
      throw new ParserError(
        message,
        expected,
        this.tokenizer.line,
        this.tokenizer.col,
        "unexpected end of input",
      );
    }

    const currentToken = this.tokenizer.currentToken();

    const offending = this.tokenizer.currentToken()?.value || "";
    const preview = this.tokenizer.substringCursor(10, 10);
    throw new ParserError(
      message,
      expected,
      currentToken?.line || this.tokenizer.line,
      currentToken?.col || this.tokenizer.col,
      `at${offending ? ` ${offending}` : ""}${
        preview ? ` near '${preview}'` : ""
      }`,
    );
  }
  private failOp(operator: string, expected?: string | string[]): never {
    this.fail("Failed to parse " + operator, expected);
  }
}

export function parseQuery(text: string) {
  return new Parser(text).parseQuery();
}

export function parseExpr(text: string) {
  return new Parser(text).parseExpr();
}

function isInt(str: string) {
  str = str.trim();
  if (!str) {
    return false;
  }
  str = str.replace(/^0+/, "") || "0";
  const n = Math.floor(Number(str));
  return n !== Infinity && String(n) === str && n >= 0;
}

function stripQuotes(str: string, quote: "'" | '"' | "`") {
  if (str[0] !== quote || str[str.length - 1] !== quote) {
    return str;
  } else {
    return str.slice(1, -1);
  }
}

function stripIfDoubleOrBacktickQuoted(tok: TokenInfo): string {
  return tok.type === "doubleQuotedString"
    ? stripQuotes(tok.value, '"')
    : tok.type === "backtickQuotedString"
      ? stripQuotes(tok.value, "`")
      : tok.value;
}

function makeLoc(token: TokenInfo): Loc {
  return {
    start: {
      line: token.line,
      col: token.col,
    },
    end: {
      line: token.line,
      col: token.col + token.value.length,
    },
  };
}

function coerceToIdent(literal: Literal | Ident): Ident {
  if (literal.op === "ident") {
    return literal;
  } else if (typeof literal.value === "string") {
    return {
      op: "ident",
      name: [literal.value],
      loc: literal.loc,
    };
  } else {
    throw new Error("Expected string literal");
  }
}

function isPeriodOrLsquare(token?: Token) {
  return token === "period" || token === "lsquare";
}

export function traverseQuery(
  query: ParsedQuery,
  // Returns true if we should continue traversing
  handleFn: (expr: Expr) => boolean,
) {
  if (query.filter) {
    traverseExpr(query.filter, handleFn);
  }
  if (query.sort) {
    query.sort.forEach((sort) =>
      "expr" in sort ? traverseExpr(sort.expr, handleFn) : null,
    );
  }
  if (query.select) {
    query.select.forEach((alias) =>
      "expr" in alias ? traverseExpr(alias.expr, handleFn) : null,
    );
  }
  if (query.dimensions) {
    query.dimensions.forEach((alias) =>
      "expr" in alias ? traverseExpr(alias.expr, handleFn) : null,
    );
  }
  if (query.pivot) {
    query.pivot.forEach((alias) =>
      "expr" in alias ? traverseExpr(alias.expr, handleFn) : null,
    );
  }
  if (query.unpivot) {
    query.unpivot.forEach((expr) =>
      "expr" in expr ? traverseExpr(expr.expr, handleFn) : null,
    );
  }
  if (query.measures) {
    query.measures.forEach((alias) =>
      "expr" in alias ? traverseExpr(alias.expr, handleFn) : null,
    );
  }
}

export function traverseExpr(
  expr: Expr,
  // Returns true if we should continue traversing
  handleFn: (expr: Expr) => boolean,
) {
  if (!handleFn(expr)) {
    return;
  }
  switch (expr.op) {
    case "literal":
      break;
    case "interval":
      break;
    case "ident":
      break;
    case "star":
      if (expr.replace) {
        Object.values(expr.replace).forEach((replaceExpr) =>
          traverseExpr(replaceExpr, handleFn),
        );
      }
      break;
    case "function":
      expr.args.forEach((arg) => traverseExpr(arg, handleFn));
      break;
    case "eq":
    case "is":
    case "ne":
    case "lt":
    case "le":
    case "gt":
    case "ge":
    case "add":
    case "sub":
    case "mul":
    case "div":
    case "mod":
    case "like":
    case "ilike":
    case "match":
    case "in":
      traverseExpr(expr.left, handleFn);
      traverseExpr(expr.right, handleFn);
      break;
    case "and":
    case "or":
      if (expr.left) traverseExpr(expr.left, handleFn);
      if (expr.right) traverseExpr(expr.right, handleFn);
      expr.children?.forEach((child) => traverseExpr(child, handleFn));
      break;
    case "includes":
      traverseExpr(expr.haystack, handleFn);
      traverseExpr(expr.needle, handleFn);
      break;
    case "neg":
    case "not":
    case "isnull":
    case "isnotnull":
      traverseExpr(expr.expr, handleFn);
      break;
    case "if":
      expr.conds.forEach((cond) => {
        traverseExpr(cond.cond, handleFn);
        traverseExpr(cond.then, handleFn);
      });
      traverseExpr(expr.else, handleFn);
      break;
    case "btql":
      const parsed = parseExpr(expr.btql);
      traverseExpr(parsed, handleFn);
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
      delete (expr as any).btql;
      Object.assign(expr, parsed);
      break;
    default:
      if ("btql" in expr) {
        return;
      }
      const _: never = expr;
  }
}
